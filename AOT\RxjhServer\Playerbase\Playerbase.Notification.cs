﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

    public partial class PlayersBes : X_Khi_Cong_Thuoc_Tinh
    {
    
	public void ThongBao_NhacNho()
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(1025);
		sendingClass.Write8(1008000093L);
		sendingClass.Write4(3);
		sendingClass.Write4(1);
		sendingClass.Write4(2000000012);
		sendingClass.Write4(0);
		if (Client != null)
		{
			Client.SendPak(sendingClass, 15104, SessionID);
		}
	}

	public void TipsForGettingMoney(uint uint_0)
	{
		var array = Converter.HexStringToByte("AA554200EA010D0034000100000000000000000000000094357700000000A701000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		var bytes = BitConverter.GetBytes(uint_0);
		System.Buffer.BlockCopy(bytes, 0, array, 30, bytes.Length);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void TipsForGettingMoneyForDupe(uint uint_0, long time)
	{
		var array = Converter.HexStringToByte("AA554200EA010D0034000100000000000000000000000094357700000000A701000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		var bytes = BitConverter.GetBytes(uint_0);
		System.Buffer.BlockCopy(bytes, 0, array, 30, bytes.Length);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void LevelUpNoti(int int_109)
	{
		SendingClass sendingClass = new();
		sendingClass.Write(Player_Level);
		sendingClass.Write(int_109);
		sendingClass.Write4(0);
		sendingClass.Write4(0);
		if (Client != null)
		{
			Client.SendPak(sendingClass, 30464, SessionID);
			SendCurrentRangeBroadcastData(sendingClass, 30464, SessionID);
		}
	}

	public void PhanGiaiVatPham_NhacNho(int int_109)
	{
		var array = Converter.HexStringToByte("AA5512003B0231170400FEFFFFFF000000000000B0EB55AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 10, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void PurchaseItemReminder(int int_109)
	{
		var array = Converter.HexStringToByte("AA551300000093000800050000000E000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 14, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void PickUpItemReminder(int int_109, long long_5)
	{
		var array = Converter.HexStringToByte("AA551A0000000D000C00030000006843030000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 10, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(long_5), 0, array, 14, 8);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void StoreTips(int int_109)
	{
		var array = Converter.HexStringToByte("AA5512000000CF000700010D0000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 11, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void NhacNho_Move_Party_Src_15m(int int_109, int int_110, int int_111)
	{
		var array = Converter.HexStringToByte("AA552200D001121614000106000078DC143C010000000100000009943577000000000000A65455AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_110), 0, array, 15, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_111), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 22, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void NhacNho_Move_Party(int int_109, int int_110, int int_111)
	{
		var array = Converter.HexStringToByte("AA552200D001121614000106000078DC143C010000000100000009943577000000000000A65455AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_110), 0, array, 11, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_111), 0, array, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 18, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void GangTeleporterPrompt(int int_109, int int_110, int int_111)
	{
		var array = Converter.HexStringToByte("AA552000D001311614000106000078DC143C010000000100000009943577000000000000A65455AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_110), 0, array, 11, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_111), 0, array, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 18, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void RollingAnnouncement(int int_109)
	{
		if (!Client.TreoMay)
		{
			var array = Converter.HexStringToByte("AA554D000F27B018480001000000941100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 14, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 4, 2);
			if (Client != null)
			{
				Client.SendMultiplePackage(array, array.Length);
			}
		}
	}

	public string Unitoccp1258(string nguon)
	{
		var text = "ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặẸẹẺẻẼẽẾếỀềỂểỄễỆệỈỉỊịỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợỤụỦủỨứỪừỬửỮữỰựỲỳỴỵỶỷỸỹ";
		var array = new string[134]
		{
			"AÌ", "Aì", "Â", "AÞ", "EÌ", "Eì", "Ê", "IÌ", "Iì", "OÌ",
			"Oì", "Ô", "OÞ", "UÌ", "Uì", "Yì", "aÌ", "aì", "â", "aÞ",
			"eÌ", "eì", "ê", "iÌ", "iì", "oÌ", "oì", "ô", "oÞ", "uÌ",
			"uì", "yì", "Ã", "ã", "Ð", "ð", "IÞ", "iÞ", "UÞ", "uÞ",
			"Õ", "õ", "Ý", "ý", "Aò", "aò", "AÒ", "aÒ", "Âì", "âì",
			"ÂÌ", "âÌ", "ÂÒ", "âÒ", "ÂÞ", "âÞ", "Âò", "âò", "Ãì", "ãì",
			"ÃÌ", "ãÌ", "ÃÒ", "ãÒ", "ÃÞ", "ãÞ", "Ãò", "ãò", "Eò", "eò",
			"EÒ", "eÒ", "EÞ", "eÞ", "Êì", "êì", "ÊÌ", "êÌ", "ÊÒ", "êÒ",
			"ÊÞ", "êÞ", "Êò", "êò", "IÒ", "iÒ", "Iò", "iò", "Oò", "oò",
			"OÒ", "oÒ", "Ôì", "ôì", "ÔÌ", "ôÌ", "ÔÒ", "ôÒ", "ÔÞ", "ôÞ",
			"Ôò", "ôò", "Õì", "õì", "ÕÌ", "õÌ", "ÕÒ", "õÒ", "ÕÞ", "õÞ",
			"Õò", "õò", "Uò", "uò", "UÒ", "uÒ", "Ýì", "ýì", "ÝÌ", "ýÌ",
			"ÝÒ", "ýÒ", "ÝÞ", "ýÞ", "Ýò", "ýò", "YÌ", "yÌ", "Yò", "yò",
			"YÒ", "yÒ", "YÞ", "yÞ"
		};
		var text2 = "";
		for (var i = 0; i < nguon.Length; i++)
		{
			var num = text.IndexOf(nguon[i]);
			text2 = ((num <= 0) ? (text2 + nguon[i]) : (text2 + array[num]));
		}
		return text2;
	}

	public void HeThongNhacNho(string string_11, int int_109, string string_12)
	{
		try
		{
			string_11 = Unitoccp1258(string_11);
			string_12 = Unitoccp1258(string_12);
			var array = Converter.HexStringToByte("AA55A50000006600970000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			array[10] = (byte)int_109;
			if (string_11.Length > 100)
			{
				string_11 = string_11.Substring(0, 100);
			}
			var bytes = Encoding.GetEncoding(1252).GetBytes(string_12);
			var bytes2 = Encoding.GetEncoding(1252).GetBytes(string_11);
			System.Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			System.Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}

		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi HeThongNhacNho :" + ex.Message);
		}

	}

	public void HeThongNhacNho(string string_11)
	{
		string_11 = Unitoccp1258(string_11);
		if (string_11.Length > 121)
		{
			string_11 = string_11.Substring(0, 121);
		}
		var array = Converter.HexStringToByte("AA55A5000000660097000F000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		var bytes = Encoding.GetEncoding(1252).GetBytes(string_11);
		System.Buffer.BlockCopy(bytes, 0, array, 35, bytes.Length);
		System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void Tang_Hoa_Thong_Bao(string msg)
	{
		var text = "AA55B7002F046600A800F1000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		var array2 = Converter.TangHoaKetHon(msg);
		System.Buffer.BlockCopy(array2, 0, array, 35, array2.Length);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void SystemNotification(string string_11)
	{
		try
		{
			string_11 = Unitoccp1258(string_11);
			if (string_11.Length > 121)
			{
				string_11 = string_11.Substring(0, 121);
			}
			var array = Converter.HexStringToByte("AA55A50000006600970008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			var bytes = Encoding.GetEncoding(1252).GetBytes(string_11);
			System.Buffer.BlockCopy(bytes, 0, array, 35, bytes.Length);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Thiên cơ các Thông Báo gặp lỗi !!");
		}
	}

	public void SystemRollingAnnouncement(string string_11)
	{
		string_11 = Unitoccp1258(string_11);
		var array = Converter.HexStringToByte("AA55A50000006600970008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		var bytes = Encoding.GetEncoding(1252).GetBytes(string_11);
		System.Buffer.BlockCopy(bytes, 0, array, 35, bytes.Length);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public virtual void SendCurrentRangeBroadcastData(byte[] data, int length, bool skipBs = false)
	{
	}

	public virtual void SendCurrentRangeBroadcastData(SendingClass pak, int id, int wordid)
	{
	}

	public void NewMailNotification(int int_109, int int_110)
	{
		if (DanhSach_TruyenThu == null)
		{
			return;
		}
		foreach (var value in DanhSach_TruyenThu.Values)
		{
			if (value.DaXemHayChua == 0)
			{
				var array = Converter.HexStringToByte("AA550F000000B200010002000000000000F1A755AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 10, 1);
				if (int_109 == 3)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(int_110), 0, array, 11, 4);
				}
				Client.Send_Map_Data(array, array.Length);
			}
		}
	}
		public void GuiDi_TheLucChien_LoiMoi_New2()
	{
		var array = Converter.HexStringToByte("AA552E000F2713222000010001000B00000001000000030000000500000005000000620F000000000000000000000000BD8455AA");
		if (World.tmc_flag)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(55555), 0, array, 34, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(55555), 0, array, 38, 4);
		}
		else
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_ChinhPhai_SoNguoi), 0, array, 34, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_TaPhai_SoNguoi), 0, array, 38, 4);
		}
		System.Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 22, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void TheLucChien_LuaChonHoiSinh()
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000010001000A000000010000000400000005000000050000000000000000000000000000000000153555AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void TheLucChien_AnhHungHoiSinh_NhacNho(int int_109)
	{
		var array = Converter.HexStringToByte("AA552E000F2713222000080001000100000001000000360E0000000000000000000000000000000000000000000000007D5C55AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 22, 1);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}

	public void Send_LoiMoi_TLC_Packet(int int_109, int int_110, int int_111)
	{
		using SendingClass sendingClass = new();
		sendingClass.Write4(int_110);
		sendingClass.Write2(1);
		if (int_109 == 14)
		{
			sendingClass.Write2(70);
		}
		else if (int_109 == 3 && int_111 == 11)
		{
			sendingClass.Write2(int_111);
		}
		else
		{
			sendingClass.Write2(70);
		}
		sendingClass.Write4(0);
		if (int_109 == 1 && int_111 == 2)
		{
			sendingClass.Write4(2);
		}
		else if (int_109 == 1 && int_111 != 2)
		{
			sendingClass.Write4(1);
		}
		else
		{
			sendingClass.Write4(int_109);
		}
		if (int_109 == 3 && int_111 == 11)
		{
			sendingClass.Write4(World.TheLucChien_ChinhPhai_DiemSo);
			sendingClass.Write4(World.TheLucChien_TaPhai_DiemSo);
			sendingClass.Write4(World.TheLucChien_ChinhPhai_SoNguoi);
			sendingClass.Write4(World.TheLucChien_TaPhai_SoNguoi);
		}
		else if (int_109 == 1 && int_111 == 2)
		{
			sendingClass.Write4(8);
			sendingClass.Write4(8);
			sendingClass.Write4(3636);
			sendingClass.Write4(0);
		}
		else if (int_109 == 1 && int_111 != 2)
		{
			sendingClass.Write4(8);
			sendingClass.Write4(8);
			sendingClass.Write4(3635);
			sendingClass.Write4(0);
		}
		else
		{
			sendingClass.Write4(int_111);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
		}
		sendingClass.Write4(0);
		sendingClass.Write2(0);
		if (Client != null)
		{
			Client.SendPak(sendingClass, 4898, SessionID);
		}
	}

	public void GuiDi_CongThanhChien_DemNguoc(int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TheLucChien_DemNguoc(int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void Time_Dem_Nguoc(int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TheLucChien_TinTuc_TranChienSapBatDau(int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA551B00A205BA000D00020000100E0000000000000000000000000000AC4155AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 13, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_Progress), 0, array, 10, 1);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_ChinhPhai_DiemSo), 0, array, 15, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_TaPhai_DiemSo), 0, array, 19, 4);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TheLucChien_TinTuc2(Players player)
	{
		try
		{
			var array = Converter.HexStringToByte("AA551B00A205BA000D000303271806FFFF0000FFFF000000000000000059DF55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 10, 1);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_MidTime), 0, array, 13, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_ChinhPhai_DiemSo), 0, array, 15, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_TaPhai_DiemSo), 0, array, 19, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TheLucChien_CapNhat_DiemSo(Players player)
	{
		try
		{
			var array = Converter.HexStringToByte("AA551B00A205BA000D000303271806FFFF0000FFFF000000000000000059DF55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_Progress), 0, array, 10, 1);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_MidTime), 0, array, 13, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_ChinhPhai_DiemSo), 0, array, 15, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_TaPhai_DiemSo), 0, array, 19, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public static void GUI_DI_THE_LUC_BATTLE_RECORD(Players player)
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write2(World.EventTop.Count);
			sendingClass.Write2(0);
			foreach (var item in World.EventTop.Values.OrderByDescending((EventTopClass x) => x.GietNguoiSoLuong))
			{
				sendingClass.Write4(item.Full_Server_ID);
				if (item.TheLuc == "CHINH_PHAI")
				{
					sendingClass.Write1(1);
				}
				else if (item.TheLuc == "TA_PHAI")
				{
					sendingClass.Write1(2);
				}
				sendingClass.WriteString(item.TenNhanVat, 15);
				sendingClass.Write2(item.DangCap);
				sendingClass.Write2(item.GietNguoiSoLuong);
				sendingClass.Write2(item.TuVongSoLuong);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write4(item.GietNguoiSoLuong);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
			}
			if (player.Client != null)
			{
				player.Client.SendPak(sendingClass, 5410, player.SessionID);
			}
		}
		catch
		{
		}
	}

	public static void GUI_DI_DCH_CHINHPHAI_RECORD(Players player)
	{
		try
		{
			SendingClass sendingClass = new();
			var source = World.EventTopDCH.Where((KeyValuePair<string, EventTopDCHClass> zz) => zz.Value.TheLuc == "CHINHPHAI");
			sendingClass.Write4(source.Count());
			var num = 1;
			foreach (var item in from x in World.EventTopDCH.Values
								 orderby x.Dame_Tru descending, x.GietNguoiSoLuong descending
								 select x)
			{
				if (item.TheLuc == "CHINHPHAI")
				{
					sendingClass.Write4(item.Full_Server_ID);
					sendingClass.Write2(num);
					sendingClass.WriteString(item.TenNhanVat, 15);
					sendingClass.Write2(item.DangCap);
					sendingClass.Write2(item.GietNguoiSoLuong);
					sendingClass.Write2(item.HopTacGietNguoi);
					sendingClass.Write2(item.TuVongSoLuong);
					sendingClass.Write4(0);
					sendingClass.Write4(item.Dame_Tru);
					num++;
				}
			}
			if (player.Client != null)
			{
				player.Client.SendPak(sendingClass, 9297, player.SessionID);
			}
		}
		catch
		{
		}
	}

	public static void GUI_DI_DCH_TAPHAI_RECORD(Players player)
	{
		try
		{
			SendingClass sendingClass = new();
			var source = World.EventTopDCH.Where((KeyValuePair<string, EventTopDCHClass> zz) => zz.Value.TheLuc == "TAPHAI");
			sendingClass.Write4(source.Count());
			var num = 1;
			foreach (var item in from x in World.EventTopDCH.Values
								 orderby x.Dame_Tru descending, x.GietNguoiSoLuong descending
								 select x)
			{
				if (item.TheLuc == "TAPHAI")
				{
					sendingClass.Write4(item.Full_Server_ID);
					sendingClass.Write2(num);
					sendingClass.WriteString(item.TenNhanVat, 15);
					sendingClass.Write2(item.DangCap);
					sendingClass.Write2(item.GietNguoiSoLuong);
					sendingClass.Write2(item.HopTacGietNguoi);
					sendingClass.Write2(item.TuVongSoLuong);
					sendingClass.Write4(0);
					sendingClass.Write4(item.Dame_Tru);
					num++;
				}
			}
			if (player.Client != null)
			{
				player.Client.SendPak(sendingClass, 9553, player.SessionID);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TheLucChien_TinTuc1()
	{
		try
		{
			var array = Converter.HexStringToByte("AA551D005400B7000F000212270300000064000000640002000000000000008E1B55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TheLucChien_KetThuc_TinTuc(int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA551A00B902BA000C00041127031200000018000000000000000000822255AA");
			switch (int_109)
			{
				case 1:
					if (Player_Zx == 1 && TheLucChien_PhePhai != "CHINH_PHAI")
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 13, 1);
					}
					else
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 13, 1);
					}
					break;
				case 2:
					if (Player_Zx == 1 && TheLucChien_PhePhai != "CHINH_PHAI")
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 13, 1);
					}
					else
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 13, 1);
					}
					break;
				case 3:
					System.Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 13, 1);
					break;
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_Progress), 0, array, 10, 1);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_ChinhPhai_DiemSo), 0, array, 14, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(World.TheLucChien_TaPhai_DiemSo), 0, array, 18, 4);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_NoiDungKetThuc_TheLucChien()
	{
		try
		{
			var array = Converter.HexStringToByte("AA551B00A205BA000D0006132700000000000000000000000000000000AC4155AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (Client != null)
			{
				Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}
	
	public void SendGroupMessageBroadcastData(byte[] byte_0, int int_109, ConcurrentDictionary<int, Players> threadSafeDictionary_0)
	{
		try
		{
			foreach (var value in threadSafeDictionary_0.Values)
			{
				if (value.Client != null)
				{
					value.Client.Send_Map_Data(byte_0, int_109);
				}
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TruyenAm_TinTucSoLieu(byte[] byte_0, int int_109, string string_11, string string_12, int int_110)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.CharacterName != string_11)
				{
					continue;
				}
				if (value.Config.TruyenAm == 0)
				{
					HeThongNhacNho("Đối phương đã thiết lập cấm truyền âm!", 10, "Thiên cơ các");
					break;
				}
				if (Client != null)
				{
					Client.Send_Map_Data(byte_0, int_109);
				}
				GuiDi_TruyenAm_TinTuc(value, string_12, int_110);
				break;
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TruyenAm_TinTuc(Players players_0, string string_11, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55A50000006600970000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			array[10] = (byte)int_109;
			var bytes = Encoding.Default.GetBytes(CharacterName);
			var bytes2 = Encoding.Default.GetBytes(string_11);
			System.Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			System.Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			if (players_0.Client != null)
			{
				players_0.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_TruyenAm_TinTuc(string string_11, int int_109, Players players_0, string string_12, int int_110)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55A50000006600970000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			array[10] = (byte)int_110;
			var bytes = Encoding.Default.GetBytes(string_11);
			var bytes2 = Encoding.Default.GetBytes(string_12);
			System.Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			System.Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 4, 2);
			if (players_0.Client != null)
			{
				players_0.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void GuiDi_DCH_LoiMoi_New2()
	{
		var array = Converter.HexStringToByte("AA5516000F05000003510E0000000000FFFFFFFFFFFF0600000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send(array, array.Length);
		}
	}

	public void GuiDi_DCH_XacNhanVaoMap()
	{
		var array = Converter.HexStringToByte("AA551600C301000004510E0001000000FFFFFFFFFFFFA59C000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send(array, array.Length);
		}
	}

	public void GuiDi_TheLucChien_LoiMoi_New()
	{
		var array = Converter.HexStringToByte("AA552E000F2713222000010001000B00000001000000020000000500000005000000340E000000000000000000000000BD8455AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 22, 1);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
		}
	}
	
	public void SendSpiritBeastData(long long_5, X_Linh_Thu_Loai SpiritBeast类_0)
	{
		var array = Converter.HexStringToByte("AA555A002D0159104C000B00000001000000000000000000000000000000000000000000000002036300000000000000000000000000000000000000000000000000000000000000000000000000DE071600000000000000000000000000000055AA");
		var bytes = Encoding.Default.GetBytes(SpiritBeast类_0.Name);
		System.Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_JOB), 0, array, 38, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_JOB_LEVEL), 0, array, 39, 1);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_LEVEL), 0, array, 40, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_MAGIC1), 0, array, 42, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_MAGIC2), 0, array, 46, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_MAGIC3), 0, array, 50, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_MAGIC4), 0, array, 54, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SpiritBeast类_0.FLD_MAGIC5), 0, array, 58, 4);
		double num = Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL]) - Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL - 1]);
		double num2 = CharacterBeast.FLD_EXP - Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL - 1]);
		if (num2 < 1.0)
		{
			CharacterBeast.FLD_EXP = Convert.ToInt64(World.lever[CharacterBeast.FLD_LEVEL - 1]);
			num2 = 0.0;
		}
		System.Buffer.BlockCopy(BitConverter.GetBytes((int)num2), 0, array, 62, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes((int)num), 0, array, 70, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(long_5), 0, array, 78, 8);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.SendMultiplePackage(array, array.Length);
		}
	}

	public async void SendSpiritBeastData(long long_5)
	{
		var num = 0;
		try
		{
			num = 1;
			//var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_Cw  WHERE  ItmeId  ={long_5}");
			var dBToDataTable = await GameDb.GetPlayerPet(long_5);
			num = 2;
			if (dBToDataTable == null)
			{
				return;
			}
			num = 3;
			if (dBToDataTable != null)
			{
				num = 4;
				var array = Converter.HexStringToByte("AA555A002D0159104C000B00000001000000000000000000000000000000000000000000000002036300000000000000000000000000000000000000000000000000000000000000000000000000DE071600000000000000000000000000000055AA");
				num = 5;
				System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				num = 6;
				var s = dBToDataTable.name.ToString();
				num = 7;
				var num2 = long.Parse(dBToDataTable.fld_exp.ToString());
				num = 8;
				var num3 = (int)dBToDataTable.fld_level;
				num = 9;
				var value = (int)dBToDataTable.fld_job;
				num = 10;
				var value2 = (int)dBToDataTable.fld_job_level;
				num = 11;
				var num4 = (int)dBToDataTable.fld_hp;
				num = 12;
				var value3 = (int)dBToDataTable.fld_magic1;
				num = 13;
				var value4 = (int)dBToDataTable.fld_magic2;
				num = 14;
				var value5 = (int)dBToDataTable.fld_magic3;
				num = 15;
				var value6 = (int)dBToDataTable.fld_magic4;
				num = 16;
				var value7 = (int)dBToDataTable.fld_magic5;
				num = 17;
				var bytes = Encoding.Default.GetBytes(s);
				num = 18;
				System.Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
				num = 19;
				System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 38, 1);
				num = 20;
				System.Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array, 39, 1);
				num = 21;
				System.Buffer.BlockCopy(BitConverter.GetBytes(num3), 0, array, 40, 2);
				num = 22;
				System.Buffer.BlockCopy(BitConverter.GetBytes(value3), 0, array, 42, 4);
				num = 23;
				System.Buffer.BlockCopy(BitConverter.GetBytes(value4), 0, array, 46, 4);
				num = 24;
				System.Buffer.BlockCopy(BitConverter.GetBytes(value5), 0, array, 50, 4);
				num = 25;
				System.Buffer.BlockCopy(BitConverter.GetBytes(value6), 0, array, 54, 4);
				num = 26;
				System.Buffer.BlockCopy(BitConverter.GetBytes(value7), 0, array, 58, 4);
				num = 27;
				double num5 = Convert.ToInt64(World.lever[num3]) - Convert.ToInt64(World.lever[num3 - 1]);
				num = 28;
				double num6 = num2 - Convert.ToInt64(World.lever[num3 - 1]);
				num = 29;
				if (num6 < 1.0)
				{
					num = 30;
					Convert.ToInt64(World.lever[num3 - 1]);
					num6 = 0.0;
				}
				num = 31;
				System.Buffer.BlockCopy(BitConverter.GetBytes((int)num6), 0, array, 62, 4);
				num = 32;
				System.Buffer.BlockCopy(BitConverter.GetBytes((int)num5), 0, array, 70, 4);
				num = 33;
				System.Buffer.BlockCopy(BitConverter.GetBytes(long_5), 0, array, 78, 8);
				num = 34;
				if (Client != null)
				{
					num = 35;
					Client.SendMultiplePackage(array, array.Length);
				}
			}
			num = 36;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi gửi dữ liệu linh thú (qjid dài) - Lỗi tại num: [" + num + "] - [" + AccountID + "][" + CharacterName + "][" + long_5 + "]  " + ex.Message);
		}
	}

	public async void CapNhatXepHang_HoaHong(Players players_0, Players players_1, int int_109)
	{
		try
		{

			var top = await GameDb.FindRoseTop(players_0.CharacterName);
			if (top != null)
			{
				GameDb.UpdateRoseTop(players_0.CharacterName, int_109);
			}
			else
			{
				await GameDb.InsertRoseTop(players_0.CharacterName, 0, int_109, players_0.Player_Zx);
			}

			var top2 = await GameDb.FindRoseTop(players_1.CharacterName);
			if (top2 != null)
			{
				GameDb.UpdateRoseTop(players_1.CharacterName, int_109);
			}
			else
			{
				await GameDb.InsertRoseTop(players_1.CharacterName, int_109, 0, players_1.Player_Zx);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Cap Nhat XepHang_HoaHong() error [" + AccountID + "][" + CharacterName + "][Phát phần thưởng SoLuong:" + int_109 + "] " + ex.Message);
		}
	}

	public async void OpenTheRoseRanking(byte[] byte_0, int int_109)
	{
		try
		{
			var num = 1;
			var num2 = 1;
			SendingClass sendingClass = new();
			for (var i = 0; i < 4; i++)
			{
				switch (i)
				{
					case 1:
						num = 2;
						num2 = 1;
						break;
					case 2:
						num = 1;
						num2 = 2;
						break;
					case 3:
						num = 2;
						num2 = 2;
						break;
				}
				var dBToDataTable = await GameDb.FindRoseTop(CharacterName, num);
				if (dBToDataTable != null && dBToDataTable.Count > 0)
				{
					for (var j = 0; j < dBToDataTable.Count; j++)
					{
						var value = j + 1;
						sendingClass.Write1(value);
						sendingClass.WriteString(dBToDataTable[j].fld_name.ToString(), 15);
						sendingClass.Write4((int)dBToDataTable[j].fld_innum);
						sendingClass.Write4(0);
					}
					if (dBToDataTable.Count < 5)
					{
						for (var k = 0; k < 5 - dBToDataTable.Count; k++)
						{
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
							sendingClass.Write8(0L);
						}
					}
				}
				else
				{
					for (var l = 0; l < 5; l++)
					{
						sendingClass.Write8(0L);
						sendingClass.Write8(0L);
						sendingClass.Write8(0L);
					}
				}
			}
			var num3 = 0;
			var num4 = 0;
			var num5 = 0;
			var dBToDataTable2 = await GameDb.FindRoseTops();
			if (dBToDataTable2 != null && dBToDataTable2.Count > 0)
			{
				for (var m = 0; m < dBToDataTable2.Count; m++)
				{
					if (dBToDataTable2[m].fld_name.ToString() == CharacterName)
					{
						num3 = m + 1;
						num4 = (int)dBToDataTable2[m].fld_outnum;
						num5 = (int)dBToDataTable2[m].fld_innum;
						break;
					}
				}
			}
			sendingClass.Write8(num3);
			sendingClass.Write8(num4);
			sendingClass.Write8(num5);
			sendingClass.Write4(0);
			if (Client != null)
			{
				Client.SendPak(sendingClass, 1795, SessionID);
			}
		}
		catch
		{
		}
	}

	public SendingClass GetUpdatedCharacterRankingData(string string_11, string string_12, int int_109, int int_110, int int_111, int int_112, int int_113)
	{
		SendingClass sendingClass = new();
		try
		{
			//var dBToDataTable = DBA.GetDBToDataTable($"SELECT  FLD_FACE,FLD_WEARITEM,FLD_JOB_LEVEL    FROM  TBL_XWWL_Char    where  FLD_NAME='{string_11}'", "GameServer");
			var dBToDataTable =  GameDb.FindCharacter(string_11).GetAwaiter().GetResult();
			if (dBToDataTable != null )
			{
				X_Character_Template_Class x_Character_Template_Class = new((byte[])dBToDataTable.fld_face);
				var value = (int)dBToDataTable.fld_job_level;
				var array = (byte[])dBToDataTable.fld_wearitem;
				var array2 = new Item[15];
				for (var i = 0; i < 15; i++)
				{
					var array3 = new byte[World.Item_Db_Byte_Length];
					if (array.Length >= i * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
					{
						try
						{
							System.Buffer.BlockCopy(array, i * World.Item_Db_Byte_Length, array3, 0, World.Item_Db_Byte_Length);
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Debug, " --- " + ex);
						}
					}
					array2[i] = new(array3, i);
				}
				var value2 = BitConverter.ToInt32(array2[0].VatPham_ID, 0);
				var value3 = BitConverter.ToInt32(array2[1].VatPham_ID, 0);
				var value4 = BitConverter.ToInt32(array2[2].VatPham_ID, 0);
				var value5 = BitConverter.ToInt32(array2[4].VatPham_ID, 0);
				var value6 = BitConverter.ToInt32(array2[3].VatPham_ID, 0);
				var value7 = BitConverter.ToInt32(array2[5].VatPham_ID, 0);
				var value8 = BitConverter.ToInt32(array2[6].VatPham_ID, 0);
				var value9 = BitConverter.ToInt32(array2[7].VatPham_ID, 0);
				var value10 = BitConverter.ToInt32(array2[11].VatPham_ID, 0);
				var value11 = BitConverter.ToInt32(array2[13].VatPham_ID, 0);
				var fLD_CuongHoaSoLuong = array2[3].FLD_CuongHoaSoLuong;;
				sendingClass.WriteString(string_11, 14);
				sendingClass.Write4(0);
				sendingClass.Write2(0);
				sendingClass.WriteString(string_12, 14);
				sendingClass.Write4(0);
				sendingClass.Write(int_109);
				sendingClass.Write(int_110);
				sendingClass.Write(value);
				sendingClass.Write(int_111);
				sendingClass.Write2(0);
				sendingClass.Write2(x_Character_Template_Class.MauToc);
				sendingClass.Write2(x_Character_Template_Class.KieuToc);
				sendingClass.Write2(x_Character_Template_Class.KhuonMat);
				sendingClass.Write(0);
				sendingClass.Write(x_Character_Template_Class.GioiTinh);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(value2);
				sendingClass.Write4(0);
				sendingClass.Write4(value3);
				sendingClass.Write4(0);
				sendingClass.Write4(value4);
				sendingClass.Write4(0);
				sendingClass.Write4(value5);
				sendingClass.Write4(0);
				sendingClass.Write4(value6);
				sendingClass.Write4(0);
				sendingClass.Write4(value7);
				sendingClass.Write4(0);
				sendingClass.Write4(value8);
				sendingClass.Write4(0);
				sendingClass.Write4(value9);
				sendingClass.Write4(0);
				sendingClass.Write4(fLD_CuongHoaSoLuong);
				sendingClass.Write4(value10);
				sendingClass.Write4(0);
				sendingClass.Write8(0L);
				sendingClass.Write8(0L);
				sendingClass.Write8(0L);
				sendingClass.Write8(0L);
				sendingClass.Write4(value11);
				sendingClass.Write4(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write(new byte[48], 0, 48);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write(0);
				sendingClass.WriteName("");
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write2(0);
				sendingClass.Write(new byte[74]);
				sendingClass.Write4(int_113);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write2(0);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write2(0);
			}
			return sendingClass;
		}
		catch (Exception ex2)
		{
			var array4 = new string[8]
			{
				"Get Updated Character Data lỗi 02 ",
				Client.PlayerSessionID.ToString(),
				"|",
				Client.ToString(),
				"  ",
				Converter.ToString(sendingClass.ToArray3()),
				"  ",
				null
			};
			array4[7] = ex2?.ToString();
			LogHelper.WriteLine(LogLevel.Error, string.Concat(array4));
			return null;
		}
	}

	public void DangNhapVaoTroChoi_GuiDiTruyenThu(string string_11, string string_12, string string_13)
	{
		try
		{
			if (GameDb.CheckCharacterExists(string_12).GetAwaiter().GetResult() && GetCharacterData(string_12) != null)
			{
				X_Nguoi_Truyen_Thu_Loai x_Nguoi_Truyen_Thu_Loai = new();
				x_Nguoi_Truyen_Thu_Loai.TruyenThuID = (int)RxjhClass.CreateItemSeries();
				x_Nguoi_Truyen_Thu_Loai.TruyenThuNguoiGui = string_13;
				x_Nguoi_Truyen_Thu_Loai.TruyenThuNoiDung = string_11;
				x_Nguoi_Truyen_Thu_Loai.TruyenThuThoiGian = DateTime.Now;
				x_Nguoi_Truyen_Thu_Loai.CoPhaiLaNPC = 1;
				x_Nguoi_Truyen_Thu_Loai.DaXemHayChua = 0;
				DanhSach_TruyenThu.Add(x_Nguoi_Truyen_Thu_Loai.TruyenThuID, x_Nguoi_Truyen_Thu_Loai);
				GetAllMails();
				NewMailNotification(2, 0);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Dang Nhap Vao game gui TruyenThu lỗi !!");
		}
	}

    }

