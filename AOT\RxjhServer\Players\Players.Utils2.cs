﻿using HeroYulgang.Database.FreeSql.Entities.Game;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.AOI;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using HeroYulgang.Services;

namespace RxjhServer;

public partial class Players
{

	public void NumberOfSpiritBeasts()
	{
		try
		{
			if (BitConverter.ToInt32(Item_Wear[14].VatPham_ID, 0) == 0)
			{
				var array = Converter.HexStringToByte("AA5516000100591008000B00000000000000000000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
			}
			else
			{
				SendSpiritBeastData(BitConverter.ToInt64(Item_Wear[14].ItemGlobal_ID, 0));
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Number Of Spirit Beasts() error[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void UnknownFunction1()
	{
		var array = Converter.HexStringToByte("AA55BE000F276600B800180********0000000000000000000000000000000000000533120536574546972626542794D617020204465627567506F735B335D204D61705B313230315D20205472696265547970655B305D2054726962655B315D20205472696265556E6368616E67654D61705B2D315D0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void UnknownFunction3()
	{
		var array = Converter.HexStringToByte("************************");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void UnknownFunction4()
	{
		var array = Converter.HexStringToByte("AA554E00B6054B104800000F0000000000000000000000000000000200031C980A00A0BB0D00000000000000000000000000323031393131323431360020202020203230313931313235313000202020202055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void UnknownFunction5()
	{
		var array = Converter.HexStringToByte("AA55BE000F276600B8000800300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006900008914000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void UnknownFunction6()
	{
		var array = Converter.HexStringToByte("AA550800B60515020200010055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void UnknownFunction2()
	{
		var array = Converter.HexStringToByte("AA55BE000F276600B800180********00000000000000000000000000000000000001C53657444656661756C745472696265556E6368616E67654D61702829000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void ServerTime()// tuyet roi
	{
		//int num = DateTime.Now.Hour * 3600 + DateTime.Now.Minute * 60 + DateTime.Now.Second;
		//num = (int)(((DateTime.Now.Minute / 1.25) % 24) * 1000);
		string hex = "aa550000000080000600ef000000000055aa";
		byte[] dst = Converter.HexStringToByte(hex);
		//System.Buffer.BlockCopy(BitConverter.GetBytes(num), 0, dst, 10, 2);

		//if (World.CoMoRa_TuyetRoiHayKhong == 1)
		//{
		//	System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, dst, 13, 1); // Enable snow
		//}
		//else
		//{
		//	System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, dst, 13, 1); // Disable snow
		//}

		if (base.Client != null)
		{
			base.Client.Send_Map_Data(dst, dst.Length);
		}
	}
	public void Detection()
	{
		using SendingClass sendingClass = new();
		sendingClass.Write4(1);
		Client?.SendPak(sendingClass, 1536, SessionID);
	}


	public void Ramdom_ThanThu(int vatPhamId)
	{
	}

	public void SetTitleItems()
	{
		foreach (var value in TitleDrug.Values)
		{
			Set_ThuocTinh_Pill(value);
		}
	}


	public void ThanThuTamPhap_MauTim_VoHuan(byte[] data, int length)
	{
		HeThongNhacNho("Chức năng này tạm thời bị phong ấn!!", 7, "Thiên cơ các");
	}


	public void GetTheTitleType(int vatPhamId, int switchOnOff)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(0);
		sendingClass.Write4(0);
		sendingClass.Write4(vatPhamId);
		sendingClass.Write4(0);
		sendingClass.Write2(0);
		sendingClass.Write2(1);
		var num = Convert.ToUInt32(DateTime.Now.AddDays(30.0).ToString("yyMMddHHmm"));
		var num2 = Convert.ToUInt32(DateTime.Now.ToString("yyMMddHHmm"));
		if (switchOnOff != 0)
		{
			sendingClass.Write8(num);
		}
		else
		{
			sendingClass.Write8(0L);
		}
		sendingClass.Write2(switchOnOff);
		sendingClass.Write2(0);
		if (switchOnOff != 0)
		{
			sendingClass.Write8(num2);
		}
		else
		{
			sendingClass.Write8(0L);
		}

		Client?.SendPak(sendingClass, 17153, SessionID);
	}

	public void UpdateHonor(byte[] packetData, int packetSize)
	{
		try
		{
			var num = BitConverter.ToInt32(packetData, 14);
			if (BitConverter.ToInt32(packetData, 10) == 3)
			{
				World.LoadDuLieuThanhVienBangPhai();
				switch (num)
				{
					case 1008001322:
						{
							foreach (var value in World.GuildList.Values)
							{
								var characterData3 = GetCharacterData(value.PlayerName);
								if (characterData3 != null)
								{
									characterData3.MartialTitleType = 6101;
									characterData3.GetTheTitleType(1008001322, 1);
									characterData3.UpdateCharacterData(characterData3);
									characterData3.UpdateBroadcastCharacterData();
								}
							}
							return;
						}
					case 1008001323:
						{
							foreach (var value2 in World.GuildList.Values)
							{
								var characterData2 = GetCharacterData(value2.PlayerName);
								if (characterData2 != null)
								{
									characterData2.MartialTitleType = 6001;
									characterData2.GetTheTitleType(1008001323, 1);
									characterData2.UpdateCharacterData(characterData2);
									characterData2.UpdateBroadcastCharacterData();
								}
							}
							return;
						}
					case 1008001324:
						{
							foreach (var value3 in World.GuildList.Values)
							{
								var characterData = GetCharacterData(value3.PlayerName);
								if (characterData != null)
								{
									characterData.MartialTitleType = 5901;
									characterData.GetTheTitleType(1008001324, 1);
									characterData.UpdateCharacterData(characterData);
									characterData.UpdateBroadcastCharacterData();
								}
							}
							return;
						}
				}
				{
					foreach (var value4 in World.GuildList.Values)
					{
						var characterData4 = GetCharacterData(value4.PlayerName);
						if (characterData4 != null)
						{
							characterData4.MartialTitleType = 0;
							characterData4.UpdateCharacterData(characterData4);
							characterData4.UpdateBroadcastCharacterData();
						}
					}
					return;
				}
			}
			var num2 = 1008001049;
			var num3 = 1008001049;
			var num4 = 1008001099;
			var num5 = 1008001099;
			if (num.ToString().Contains("100800120"))
			{
				num5 = 1008001199;
			}
			else if (num.ToString().Contains("100800121"))
			{
				num5 = 1008001209;
			}
			else if (num.ToString().Contains("100800122"))
			{
				num5 = 1008001219;
			}
			else if (num.ToString().Contains("100800123"))
			{
				num5 = 1008001229;
			}
			else if (num.ToString().Contains("100800124"))
			{
				num5 = 1008001239;
			}
			if (num.ToString().Contains("100800125"))
			{
				num3 = 1008001239;
			}
			else if (num.ToString().Contains("100800126"))
			{
				num3 = 1008001249;
			}
			else if (num.ToString().Contains("100800127"))
			{
				num3 = 1008001259;
			}
			else if (num.ToString().Contains("100800128"))
			{
				num3 = 1008001269;
			}
			else if (num.ToString().Contains("100800129"))
			{
				num3 = 1008001279;
			}
			if (!num.ToString().Contains("100800120") && !num.ToString().Contains("100800121") && !num.ToString().Contains("100800122") && !num.ToString().Contains("100800123") && !num.ToString().Contains("100800124"))
			{
				if (!num.ToString().Contains("100800125") && !num.ToString().Contains("100800126") && !num.ToString().Contains("100800127") && !num.ToString().Contains("100800128") && !num.ToString().Contains("100800129"))
				{
					switch (num)
					{
						default:
							FLD_HonorID = 0;
							TitleRanking = 0;
							break;
						case 1008001567:
							FLD_HonorID = 601;
							break;
						case 1008001568:
							FLD_HonorID = 602;
							break;
						case 1008001569:
							FLD_HonorID = 603;
							break;
						case 1008001570:
							FLD_HonorID = 604;
							break;
						case 1008001571:
							FLD_HonorID = 605;
							break;
						case 1008001395:
							FLD_HonorID = 461;
							break;
						case 1008001396:
							FLD_HonorID = 462;
							break;
						case 1008001397:
							FLD_HonorID = 463;
							break;
						case 1008001398:
							FLD_HonorID = 464;
							break;
						case 1008001399:
							FLD_HonorID = 465;
							break;
					}
				}
				else
				{
					FLD_HonorID = num - num2;
					TitleRanking = num - num3;
				}
			}
			else
			{
				FLD_HonorID = num - num4;
				TitleRanking = num - num5;
			}
			UpdateCharacterData(this);
			UpdateBroadcastCharacterData();
			CapNhat_HP_MP_SP();
			UpdateMartialArtsAndStatus();
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Gặp sự cố khi cập nhật Update Honor !!");
		}
	}

	public void Save_NgocLienHoan_CamSu()
	{
		var array = Converter.HexStringToByte("AA5532002C01C600240002000000E5EA0D0031BD0D0031BD0D0031BD0D00E6EA0D0031BD0D0031BD0D0031BD0D0000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		NgocLienHoan.Clear();
		for (var i = 0; i < 6; i++)
		{
			NgocLienHoan.Add(900401);
		}

		Client?.Send_Map_Data(array, array.Length);
	}

	public void HonoraryTitle()
	{
		if (World.BangPhaiXepHangSoLieu.Count == 0)
		{
			return;
		}
		for (var i = 0; i < World.BangPhaiXepHangSoLieu.Count; i++)
		{
			if (i == 0 && World.BangPhaiXepHangSoLieu[i] != null && World.BangPhaiXepHangSoLieu[i].BangPhaiBangPhaiTen == GuildName)
			{
				if (GangCharacterLevel == 6)
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						value.HeThongNhacNhoNew(1, 4582, CharacterName, CharacterName);
					}
					HeThongNhacNhoNew(3, 4582, CharacterName, CharacterName);
				}
				GetTheTitleType(1008001322, 1);
				GetTheTitleType(1008001323, 0);
				GetTheTitleType(1008001324, 0);
				MartialTitleType = 6101;
				BangPhaiTitleAddedAttack = int.Parse(World.BangHang1PhanThuong_DanhHieu[0]);
				BangPhaiTitleAddedDefense = int.Parse(World.BangHang1PhanThuong_DanhHieu[1]);
				BangPhaiTitleAddedHP = int.Parse(World.BangHang1PhanThuong_DanhHieu[2]);
				HeThongNhacNho("Phần thưởng: Công kích +" + int.Parse(World.BangHang1PhanThuong_DanhHieu[0]) + ", Phòng ngự +" + int.Parse(World.BangHang1PhanThuong_DanhHieu[1]) + ", Máu +" + int.Parse(World.BangHang1PhanThuong_DanhHieu[2]), 9, "Truyền Âm Các");
			}
			if (i == 1 && World.BangPhaiXepHangSoLieu[i] != null && World.BangPhaiXepHangSoLieu[i].BangPhaiBangPhaiTen == GuildName)
			{
				if (GangCharacterLevel == 6)
				{
					foreach (var value2 in World.allConnectedChars.Values)
					{
						value2.HeThongNhacNhoNew(1, 4583, CharacterName, CharacterName);
					}
					HeThongNhacNhoNew(3, 4583, CharacterName, CharacterName);
				}
				GetTheTitleType(1008001322, 0);
				GetTheTitleType(1008001323, 1);
				GetTheTitleType(1008001324, 0);
				MartialTitleType = 6001;
				BangPhaiTitleAddedAttack = int.Parse(World.BangHang2PhanThuong_DanhHieu[0]);
				BangPhaiTitleAddedDefense = int.Parse(World.BangHang2PhanThuong_DanhHieu[1]);
				BangPhaiTitleAddedHP = int.Parse(World.BangHang2PhanThuong_DanhHieu[2]);
				HeThongNhacNho("Phần thưởng: Công kích +" + int.Parse(World.BangHang2PhanThuong_DanhHieu[0]) + ", Phòng ngự +" + int.Parse(World.BangHang2PhanThuong_DanhHieu[1]) + ", Máu +" + int.Parse(World.BangHang2PhanThuong_DanhHieu[2]), 9, "Truyền Âm Các");
			}
			if (i != 2 || World.BangPhaiXepHangSoLieu[i] == null || !(World.BangPhaiXepHangSoLieu[i].BangPhaiBangPhaiTen == GuildName))
			{
				continue;
			}
			if (GangCharacterLevel == 6)
			{
				foreach (var value3 in World.allConnectedChars.Values)
				{
					value3.HeThongNhacNhoNew(1, 4584, CharacterName, CharacterName);
				}
				HeThongNhacNhoNew(3, 4584, CharacterName, CharacterName);
			}
			GetTheTitleType(1008001322, 0);
			GetTheTitleType(1008001323, 0);
			GetTheTitleType(1008001324, 1);
			MartialTitleType = 5901;
			BangPhaiTitleAddedAttack = int.Parse(World.BangHang3PhanThuong_DanhHieu[0]);
			BangPhaiTitleAddedDefense = int.Parse(World.BangHang3PhanThuong_DanhHieu[1]);
			BangPhaiTitleAddedHP = int.Parse(World.BangHang3PhanThuong_DanhHieu[2]);
			HeThongNhacNho("Phần thưởng: Công kích +" + int.Parse(World.BangHang3PhanThuong_DanhHieu[0]) + ", Phòng ngự +" + int.Parse(World.BangHang3PhanThuong_DanhHieu[1]) + ", Máu +" + int.Parse(World.BangHang3PhanThuong_DanhHieu[2]), 9, "Truyền Âm Các");
		}
	}

	public async void ObtainTheAttributeOfTheRoseTitle()
	{
		var tops = await GameDb.FindRoseTop(CharacterName, Player_Zx);
		if (tops == null)
		{
			return;
		}

		if (tops.Count > 0)
		{
			for (var i = 0; i < tops.Count; i++)
			{
				if (tops[i].fld_name == CharacterName)
				{
					switch (i)
					{
						case 0:
							// base.RoseTitleAddAttack = int.Parse(World.HoaHongHang1GiaiThuong[1]);
							// base.RoseTitleAddDefense = int.Parse(World.HoaHongHang1GiaiThuong[2]);
							// base.RoseTitleAddHP = int.Parse(World.HoaHongHang1GiaiThuong[3]);
							base.FLD_HonorID = 461;
							GetTheTitleType(1008001395, 1);
							break;
						case 1:
							// base.RoseTitleAddAttack = int.Parse(World.HoaHongHang2GiaiThuong[1]);
							// base.RoseTitleAddDefense = int.Parse(World.HoaHongHang2GiaiThuong[2]);
							// base.RoseTitleAddHP = int.Parse(World.HoaHongHang2GiaiThuong[3]);
							base.FLD_HonorID = 462;
							GetTheTitleType(1008001396, 1);
							break;
						case 2:
							// base.RoseTitleAddAttack = int.Parse(World.HoaHongHang3GiaiThuong[1]);
							// base.RoseTitleAddDefense = int.Parse(World.HoaHongHang3GiaiThuong[2]);
							// base.RoseTitleAddHP = int.Parse(World.HoaHongHang3GiaiThuong[3]);
							base.FLD_HonorID = 463;
							GetTheTitleType(1008001397, 1);
							break;
						case 3:
							// base.RoseTitleAddAttack = int.Parse(World.HoaHongHang4GiaiThuong[1]);
							// base.RoseTitleAddDefense = int.Parse(World.HoaHongHang4GiaiThuong[2]);
							// base.RoseTitleAddHP = int.Parse(World.HoaHongHang4GiaiThuong[3]);
							base.FLD_HonorID = 464;
							GetTheTitleType(1008001398, 1);
							break;
						case 4:
							// base.RoseTitleAddAttack = int.Parse(World.HoaHongHang5GiaiThuong[1]);
							// base.RoseTitleAddDefense = int.Parse(World.HoaHongHang5GiaiThuong[2]);
							// base.RoseTitleAddHP = int.Parse(World.HoaHongHang5GiaiThuong[3]);
							base.FLD_HonorID = 465;
							GetTheTitleType(1008001399, 1);
							break;

					}
				}
			}
		}

	}


	public async void Checkgianhapbang()
	{
		CheckGiaNhapBang = await GameDb.CheckplayerHasGuild(CharacterName);
	}

	private static readonly Dictionary<int, GuildBuff> guildBuffs = new Dictionary<int, GuildBuff>
	{
		{ 1, new GuildBuff { HP = 10, MP = 10, Level = 2, PillID= 0 } },
		{ 2, new GuildBuff { HP = 10, MP = 10, Level = 2, PillID= 0 } },
		{ 3, new GuildBuff { HP = 20, MP = 20, Level = 3, PillID= 0 } },
		{ 4, new GuildBuff { HP = 30, MP = 30, Level = 4, PillID= 0 } },
		{ 5, new GuildBuff { HP = 50, MP = 50, Level = 5, PillID= 0 } },
		{ 6, new GuildBuff { HP = 100, MP = 100, Level = 6 , PillID= 0} },
		{ 7, new GuildBuff { HP = 200, MP = 200, TanCong = 10, PhongThu = 5, CLVC = 0.01, ULPT = 100, Level = 7, PillID= 0 } },
	};
	public async Task UpdateGuildBuff()
	{
		try
		{
			// Get guild buff active
			var guild = await GameDb.GetPlayerGuildData(CharacterName);
			if (guild == null || !guild.IsBuffActive)
			{
				//remove buff
				foreach (var key in guildBuffs.Keys)
				{
					if (AppendStatusList.ContainsKey(key))
					{
						AppendStatusList[key].ThoiGianKetThucSuKien();
						AppendStatusList.Remove(key);
						StatusEffect(BitConverter.GetBytes(key), 0, 0);
					}
				}
				return;
			}
			if (guildBuffs.TryGetValue(GangLevel, out var buff))
			{
				Player_HP_Guild = buff.HP;
				Player_MP_Guild = buff.MP;
				Player_TanCong_Guild = buff.TanCong;
				Player_PhongThu_Guild = buff.PhongThu;
				Player_CLVC_Guild = buff.CLVC;
				Player_ULPT_Guild = buff.ULPT;
				Player_KhiCong_Guild = buff.KhiCong;
			}
			foreach (var key in guildBuffs.Keys)
			{
				if (key != 0)
					if (AppendStatusList.TryGetValue(key, out StatusEffect value))
					{
                        value.ThoiGianKetThucSuKien();
						AppendStatusList.Remove(key);
						StatusEffect(BitConverter.GetBytes(key), 0, 0);
					}
			}
			if (buff.PillID != 0)
			{
				var time = DateTime.Now.Date.AddDays(1) - DateTime.Now;
				var status = new StatusEffect(this, time.TotalMilliseconds, buff.PillID, 0);
				AppendStatusList[buff.PillID] = status;
				StatusEffect(BitConverter.GetBytes(buff.PillID), 1, (int)time.TotalMilliseconds);
				UpdateMartialArtsAndStatus();
				UpdateCharacterData(this);
				CapNhat_HP_MP_SP();
			}

		}
		catch
		{
		}
	}

	public void HopChua_ManhGhep_TrangBi_VoHuan_12x()
	{
		var parcelVacancy = GetParcelVacancy(this);
		if (parcelVacancy != -1)
		{
			AddItem_ThuocTinh_int(1000000269, parcelVacancy, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3);
		}
		else
		{
			HeThongNhacNho("Không còn vị trí trống trong hành trang!!", 20, "Thiên cơ các");
		}
	}

	public void HopChua_ManhGhep_TrangBi_VoHuan_13xC()
	{
		var parcelVacancy = GetParcelVacancy(this);
		if (parcelVacancy != -1)
		{
			AddItem_ThuocTinh_int(1000000266, parcelVacancy, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
		}
		else
		{
			HeThongNhacNho("Không còn vị trí trống trong hành trang!!", 20, "Thiên cơ các");
		}
	}
	public void TongHop_Top_Solo_DCH()
	{
		Logger.Instance.Error("TongHop_Top_Solo_DCH not implemented");
		// var top20 = await GameDb.GetTop20DCHPlayersWithRewards();
		// foreach (var player in top20)
		// {
		// 	if (player.PlayerName == CharacterName)
		// 	{
		// 		Player_WuXun += player.WuXunReward;
		// 		HeThongNhacNho(player.RewardMessage, 22, "Thiên cơ các");
		// 	}
		// }
	}


	public void PhanThuong_HopThanKhi_Top_10_TLC(List<eventtop> topPlayers)
	{
		for (var i = 0; i < topPlayers.Count; i++)
		{
			if (i >= 10)
			{
				break;
			}
			var text = (string)topPlayers[i].tennhanvat;
			if (text == CharacterName)
			{
				var parcelVacancy = GetParcelVacancy(this);
				if (parcelVacancy != -1)
				{
					AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000085), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 0);
					HeThongNhacNho("Đại hiệp nhận thêm phần thưởng [Hộp Thần Khí] từ Top 10 Thế Lực Chiến!", 22, "Thiên cơ các");
				}
				else
				{
					HeThongNhacNho("Hành trang không còn chỗ trống!!", 20, "Thiên cơ các");
				}
			}
		}
	}

	public void Top1_den_15_PhanThuong_HuyChuongDanhDu(List<eventtop> topPlayers)
	{

		Random random = new();
		var num = random.Next(topPlayers.Count);
		var text = (string)topPlayers[num].tennhanvat;
		if (text != CharacterName)
		{
			return;
		}
		var parcelVacancy = GetParcelVacancy(this);
		if (parcelVacancy != -1)
		{
			AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(*********), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 90);
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					value.HeThongNhacNho("[" + CharacterName + "] là người chơi TOP [" + (num + 1) + "] may mắn nhận được Huy Chương Danh Dự !!", 23, "Thiên cơ các");
				}
				return;
			}
		}
		HeThongNhacNho("Hành trang không còn chỗ trống!!", 20, "Thiên cơ các");
	}


	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		Player_VoDich = false;
	}

	private void TocDo_TinhToan(float x, float y)
	{
		var num = (int)DateTime.Now.Subtract(_movingTime).TotalMilliseconds;
		_movingTime = DateTime.Now;
		var num2 = x - _toaDoCuoiCungX;
		var num3 = y - _toaDoCuoiCungY;
		var num4 = (float)Math.Sqrt(num2 * (double)num2 + num3 * (double)num3) * 1000f / num;
		var flag = false;
		if (CharacterBeast != null && CharacterBeast.CuoiThu == 1)
		{
			flag = true;
		}
		if (num4 <= (double)TocDoDiChuyen_Max || Speed.ToString("F2") == "正无穷大")
		{
			return;
		}
		_yxsl++;
		if (_yxsl <= World.SoLan_VuotQuaChoPhep_Trong30Giay)
		{
			return;
		}
		switch (World.TocDoLonNhat_VuotQuaSoLan_ThaoTac)
		{
			case 0:
				LogHelper.WriteLine(LogLevel.Error, "DiDong Vượt ra ngoài 11 [" + TocDoDiChuyen_Max + "]  cưỡi thú [" + flag + "]  CharID[" + WalkingStatusId + "] Job:[" + Player_Job + "]-[" + AccountID + "][" + CharacterName + "]  NowSpeed:" + num4.ToString("F2"));
				break;
			case 1:
				LogHelper.WriteLine(LogLevel.Error, "DiDong Vượt ra ngoài 22 [" + TocDoDiChuyen_Max + "]  cưỡi thú [" + flag + "]  CharID[" + WalkingStatusId + "] Job:[" + Player_Job + "]-[" + AccountID + "][" + CharacterName + "]  NowSpeed:" + num4.ToString("F2"));
				break;
			case 2:
				LogHelper.WriteLine(LogLevel.Error, "DiDong Vượt ra ngoài 33 [" + TocDoDiChuyen_Max + "]  cưỡi thú [" + flag + "]  CharID[" + WalkingStatusId + "] Job:[" + Player_Job + "]-[" + AccountID + "][" + CharacterName + "]  NowSpeed:" + num4.ToString("F2"));
				if (Client != null)
				{
					Client.Dispose();
					LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 47]");
				}
				break;
			case 3:
				BanAccount(720, AccountID, "DiDong Canh bao !!");
				break;
		}
	}


	public void 触发人物靠近()
	{
		foreach (var value in MapClass.GetnpcTemplate(MapID).Values)
		{
			if (value.NPCDeath && LookInNpc(50, value))
			{
				if (!value.templayer.ContainsKey(SessionID))
				{
					value.templayer.Add(SessionID, this);
					if (value.FLD_PID == 16607 && value.templayer.Count >= value.怪物数字)
					{
						value.PhoBan_Event_FireDragon_StatusEffect_KetThuc(是否玩家击杀: true);
					}
				}
			}
			else if (value.templayer.ContainsKey(SessionID))
			{
				value.templayer.Remove(SessionID);
			}
		}
	}

	private void TINH_TOAN_NHANVAT_TOADO()
	{
		try
		{
			if (Automatic_Coordinates == null)
			{
				Automatic_Coordinates = new System.Timers.Timer(500.0);
				Automatic_Coordinates.Elapsed += AUTO_COORDINATES_ELAPSED;
				Automatic_Coordinates.Enabled = true;
				Automatic_Coordinates.AutoReset = true;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tính toán nhân vật di động tọa độ phạm sai lầm[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	private void AUTO_COORDINATES_ELAPSED(object sender, ElapsedEventArgs e)
	{
		try
		{
			var @float = 0f;
			var float2 = 0f;
			CACULATOR_REALTIME_PLAYER_MOVE(out @float, out float2);
			AOIExtensions.UpdateAOIPosition(this, @float, float2);
			if (Automatic_Coordinates != null)
			{
				Automatic_Coordinates.Enabled = false;
				Automatic_Coordinates.Close();
				Automatic_Coordinates.Dispose();
				Automatic_Coordinates = null;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "RefCoordinateCallBack Phạm sai lầm[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	private double Tinh_Toan_ToDo_Chuan(Pointx pointx0, Pointx pointx1)
	{
		return Math.Sqrt(Math.Pow(Math.Max(pointx0.x, pointx1.x) - Math.Min(pointx0.x, pointx1.x), 2.0) + Math.Pow(Math.Max(pointx0.y, pointx1.y) - Math.Min(pointx0.y, pointx1.y), 2.0));
	}

	private void CACULATOR_REALTIME_PLAYER_MOVE(out float float5, out float float6)
	{
		try
		{
			var num = TargetPositionX - PosX;
			var num2 = TargetPositionY - PosY;
			TruocMat_DiDong_KhoangCach = (float)Math.Sqrt(num * num + num2 * num2);
			var num3 = TINH_TOAN_TOC_DO_DI_CHUYEN();
			if (TruocMat_DiDong_KhoangCach == 0.0)
			{
				float5 = TargetPositionX;
				float6 = TargetPositionY;
			}
			else
			{
				num *= num3 / (float)TruocMat_DiDong_KhoangCach;
				num2 *= num3 / (float)TruocMat_DiDong_KhoangCach;
				float5 = PosX + num;
				float6 = PosY + num2;
			}
			if (TruocMat_DiDong_KhoangCach <= num3)
			{
				float5 = TargetPositionX;
				float6 = TargetPositionY;
				TruocMat_DiDong_KhoangCach = 0.0;
				if (Automatic_Coordinates != null)
				{
					Automatic_Coordinates.Enabled = false;
					Automatic_Coordinates.Close();
					Automatic_Coordinates.Dispose();
					Automatic_Coordinates = null;
				}
			}
		}
		catch (Exception ex)
		{
			if (Automatic_Coordinates != null)
			{
				Automatic_Coordinates.Enabled = false;
				Automatic_Coordinates.Close();
				Automatic_Coordinates.Dispose();
				Automatic_Coordinates = null;
			}
			LogHelper.WriteLine(LogLevel.Error, "Nhân vật di động GET Phạm sai lầm[" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
			float5 = TargetPositionX;
			float6 = TargetPositionY;
		}
	}

	public float TINH_TOAN_TOC_DO_DI_CHUYEN()
	{
		try
		{
			if (CharacterBeast != null && CharacterBeast.CuoiThu == 1)
			{
				return 52.499996f;
			}
			if (WalkingStatusId == 1)
			{
				return 46.5f;
			}
			if (WalkingStatusId == 2)
			{
				return 68f;
			}
			if (WalkingStatusId == 3)
			{
				return 115f;
			}
			if (WalkingStatusId == 5)
			{
				return 152.5f;
			}
			if (WalkingStatusId == 6)
			{
				return 37.5f;
			}
			if (WalkingStatusId == 7)
			{
				return 47f;
			}
			if (WalkingStatusId == 8)
			{
				return 52.499996f;
			}
			if (WalkingStatusId == 9)
			{
				return 57.5f;
			}
			return 46.5f;
		}
		catch
		{
			return 46.5f;
		}
	}

	public bool CheckIfThePlayerIsInTheHangUpDoubleArea(Players playe)
	{
		try
		{
			return playe.MapID == 101 && playe.PosX < 720.0 && playe.PosX > -40.0 && playe.PosY < 1360.0 && playe.PosY > 970.0;
		}
		catch
		{
			return false;
		}
	}


	public int GetSetItemId(int ngheNghiep, int level, int reside, int sex, int zx)
	{
		foreach (var value in World.ItemList.Values)
		{
			if (value.FLD_RESIDE1 == ngheNghiep && value.FLD_RESIDE2 == reside && (value.FLD_SEX == sex || value.FLD_SEX == 0) && value.FLD_LEVEL == level && (value.FLD_ZX == zx || value.FLD_ZX == 0) && value.FLD_NJ == 0)
			{
				return value.FLD_PID;
			}
		}
		return 0;
	}

	public void GiftSystem(int danhMucQuaTang)
	{
		try
		{
			var num = 0;
			var num2 = 0;
			var num3 = 0;
			var text = string.Empty;
			var text2 = string.Empty;
			switch (danhMucQuaTang)
			{
				case 1:
					num = 4;
					num3 = World.FirstLoginGift;
					break;
				case 3:
					try
					{
						foreach (var value in World.DangCapBanThuong.Values)
						{
							if (Player_Level != value.DangCap)
							{
								continue;
							}
							if (value.VoHuan != 0)
							{
								Player_WuXun += value.VoHuan;
								HeThongNhacNho("Đạt được [" + value.VoHuan + "] điểm Võ Huân!", 10, "Thiên cơ các");
								UpdateMartialArtsAndStatus();
							}
							if (value.Set != 0)
							{
								var parcelVacancyPosition2 = GetParcelVacancyPosition();
								if (parcelVacancyPosition2 == -1)
								{
									HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
									break;
								}
								AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value.Set), parcelVacancyPosition2, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 0);
								HeThongNhacNho("Chúc mừng đại hiệp, đã nhận được, hãy mở ra xem!", 10, "Thiên cơ các");
							}
							if (value.NguyenBao != 0)
							{
								KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
								KiemSoatNguyenBao_SoLuong(value.NguyenBao, 1);
								Save_NguyenBaoData();
							}
							if (value.TienBac.Length != 0)
							{
								Player_Money += long.Parse(value.TienBac);
								HeThongNhacNho("Đại hiệp nhận được ngân lượng [" + value.TienBac + "]!", 10, "Thiên cơ các");
								TipsForGettingMoney(uint.Parse(value.TienBac));
								UpdateMoneyAndWeight();
							}
							if (value.GoiVatPham.Length == 0)
							{
								continue;
							}
							var array3 = value.GoiVatPham.Split(';');
							for (var i = 0; i < Convert.ToInt32(array3[1]); i++)
							{
								var parcelVacancyPosition3 = GetParcelVacancyPosition();
								if (parcelVacancyPosition3 == -1)
								{
									HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
									return;
								}
								AddItem_ThuocTinh_int(Convert.ToInt32(array3[0]), parcelVacancyPosition3, 1, Convert.ToInt32(array3[2]), Convert.ToInt32(array3[3]), Convert.ToInt32(array3[4]), Convert.ToInt32(array3[5]), Convert.ToInt32(array3[6]), Convert.ToInt32(array3[7]), Convert.ToInt32(array3[8]), Convert.ToInt32(array3[9]), Convert.ToInt32(array3[10]), 0);
							}
						}
						return;
					}
					catch (Exception ex)
					{
						LogHelper.WriteLine(LogLevel.Error, "DangCapBanThuong Thông báo error! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
						return;
					}
				case 4:
					num = World.TheLucChien_ThuongLoai;
					num2 = World.TheLucChien_ThuongSoLuong;
					text = World.TheLucChien_ThuongThuocTinh;
					num3 = World.TheLucChien_ThuongGoiVatPham;
					text2 = World.TheLucChien_ThuongVatPham;
					goto default;
				default:
					switch (num)
					{
						default:
							HeThongNhacNho("Chức năng này尚未 khai mở!", 10, "Thiên cơ các");
							return;
						case 0:
							Player_WuXun += num2;
							HeThongNhacNho("Đạt được [" + num2 + "] điểm Võ Huân!", 10, "Thiên cơ các");
							UpdateMartialArtsAndStatus();
							return;
						case 1:
							KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							CheckTheIngotPointData(num2, 1);
							Save_NguyenBaoData();
							return;
						case 2:
							Player_Money += num2;
							HeThongNhacNho("Thu được [" + num2 + "] lượng ngân lượng!", 10, "Thiên cơ các");
							TipsForGettingMoney((uint)num2);
							UpdateMoneyAndWeight();
							return;
						case 3:
							{
								var array2 = text.Split(';');
								CapNhat_HP_MP_SP();
								UpdateMartialArtsAndStatus();
								UpdateCharacterData(this);
								HeThongNhacNho("Thu được Sinh mệnh [" + Convert.ToInt32(array2[0]) + "], Công kích [" + Convert.ToInt32(array2[1]) + "], Phòng ngự [" + Convert.ToInt32(array2[2]) + "], Né tránh [" + Convert.ToInt32(array2[3]) + "], Nội công [" + Convert.ToInt32(array2[4]) + "], Trúng đích [" + Convert.ToInt32(array2[5]) + "]!", 10, "Thiên cơ các");
								return;
							}
						case 5:
							{
								var array = text2.Split(';');
								var num4 = 0;
								while (true)
								{
									if (num4 >= Convert.ToInt32(array[1]))
									{
										return;
									}
									var parcelVacancyPosition = GetParcelVacancyPosition();
									if (parcelVacancyPosition == -1)
									{
										break;
									}
									AddItem_ThuocTinh_int(Convert.ToInt32(array[0]), parcelVacancyPosition, 1, Convert.ToInt32(array[2]), Convert.ToInt32(array[3]), Convert.ToInt32(array[4]), Convert.ToInt32(array[5]), Convert.ToInt32(array[6]), Convert.ToInt32(array[7]), Convert.ToInt32(array[8]), Convert.ToInt32(array[9]), Convert.ToInt32(array[10]), 0);
									num4++;
								}
								HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
								return;
							}
						case 6:
							return;
						case 4:
							break;
					}
					break;
			}
			var parcelVacancyPosition4 = GetParcelVacancyPosition();
			if (parcelVacancyPosition4 == -1)
			{
				HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
			}
			else
			{
				AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(num3), parcelVacancyPosition4, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 0);
			}
		}
		catch (Exception ex2)
		{
			var text3 = string.Empty;
			switch (danhMucQuaTang)
			{
				case 1:
					text3 = "新手Online赠送";
					break;
				case 2:
					text3 = "ChuyenChuc生赠送";
					break;
				case 3:
					text3 = "DangCapBanThuong";
					break;
				case 4:
					text3 = "Thế lực chiến奖励";
					break;
			}
			LogHelper.WriteLine(LogLevel.Error, "Phát phần thưởng Thông báo error! [" + text3 + "] [" + AccountID + "]-[" + CharacterName + "] - " + ex2.Message);
		}
	}

	public void WeddingFeaturePack(int giaiDoan, string tanLangDanhTu, string tanNuongDanhTu, int danhDau)
	{
		var array = Converter.HexStringToByte("AA55460043057A17380026000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003B0955AA");
		Buffer.BlockCopy(BitConverter.GetBytes(giaiDoan), 0, array, 10, 4);
		Buffer.BlockCopy(Encoding.Default.GetBytes(tanLangDanhTu), 0, array, 18, Encoding.Default.GetBytes(tanLangDanhTu).Length);
		Buffer.BlockCopy(Encoding.Default.GetBytes(tanNuongDanhTu), 0, array, 33, Encoding.Default.GetBytes(tanNuongDanhTu).Length);
		Buffer.BlockCopy(BitConverter.GetBytes(danhDau), 0, array, 50, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void PvpDragonBattle(int a, int b, int c)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(a);
		sendingClass.Write4(b);
		sendingClass.Write4(c);
		Client?.SendPak(sendingClass, 8705, SessionID);
	}

	public void XemKhiCong(byte[] packetData, int packetSize)
	{
		try
		{
			var value = BitConverter.ToInt32(packetData, 14);
			var characterData = GetCharacterData(value);
			if (characterData == null)
			{
				return;
			}
			SendingClass sendingClass = new();
			sendingClass.Write4(1);
			sendingClass.Write2(value);
			sendingClass.Write2(0);
			sendingClass.Write4(characterData.Player_Qigong_point);
			for (var i = 0; i < 15; i++)
			{
				if (i < 12)
				{
					sendingClass.Write2(characterData.KhiCong[i].KhiCongID);
					if (characterData.KhiCong[i].KhiCongID != 0)
					{
						if (characterData.KhiCong[i].KhiCong_SoLuong != 0)
						{
							var maxKhiCongTren1KhiCong = World.MaxKhiCong_Tren1KhiCong;
							var num = characterData.KhiCong[i].KhiCong_SoLuong + characterData.FLD_TrangBi_ThemVao_KhiCong + characterData.FLD_NhanVat_ThemVao_KhiCong + characterData.FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + characterData.NhanVat_WX_BUFF_KhiCong + (int)characterData.NhanGiaTri_TangRieng_CuaKhiCong(i) + characterData.FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
							if (num > maxKhiCongTren1KhiCong)
							{
								num = maxKhiCongTren1KhiCong;
							}
							sendingClass.Write2(num);
						}
						else
						{
							sendingClass.Write2(0);
						}
					}
					else
					{
						sendingClass.Write2(0);
					}
				}
				else
				{
					sendingClass.Write4(0);
				}
			}
			if (characterData.Player_Job_level >= 6)
			{
				foreach (var value2 in characterData.ThangThienKhiCong.Values)
				{
					sendingClass.Write2(value2.KhiCongID);
					if (value2.KhiCong_SoLuong != 0)
					{
						var maxKhiCongTren1KhiCong2 = World.MaxKhiCong_Tren1KhiCong;
						var num2 = value2.KhiCong_SoLuong + characterData.FLD_TrangBi_ThemVao_KhiCong + characterData.FLD_NhanVat_ThemVao_KhiCong + characterData.FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 + characterData.NhanVat_WX_BUFF_KhiCong + (int)characterData.NhanGiaTri_TangRieng_CuaKhiCong(value2.KhiCongID) + characterData.FLD_DuocPham_ThemVao_KhiCong + BanThuong_ThemVao_KC + Player_KhiCong_Guild;
						if (num2 > maxKhiCongTren1KhiCong2)
						{
							num2 = maxKhiCongTren1KhiCong2;
						}
						sendingClass.Write2(num2);
					}
					else
					{
						sendingClass.Write2(0);
					}
				}
				for (var j = 0; j < 15 - characterData.ThangThienKhiCong.Count; j++)
				{
					sendingClass.Write2(0);
					sendingClass.Write2(0);
				}
				for (var k = 0; k < 30; k++)
				{
					sendingClass.Write4(0);
				}
			}

			Client?.SendPak(sendingClass, 9241, SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi soi đồ !!!!!!!!!!!!!");
		}
	}

	public void ViewEquipment(byte[] packetData, int packetSize)
	{
		try
		{
			if (World.tmc_flag && MapID == 801)
			{
				HeThongNhacNho("Không thể sử dụng trong bản đồ này!!", 10, "Thiên cơ các");
				return;
			}
			var num = BitConverter.ToInt32(packetData, 10);
			var characterData = GetCharacterData(num);
			if (characterData == null || num == SessionID)
			{
				return;
			}
			SendingClass sendingClass = new();
			sendingClass.Write4(1);
			sendingClass.Write2(num);
			sendingClass.Write2(0);
			for (var i = 0; i < 15; i++)
			{
				sendingClass.Write(characterData.Item_Wear[i].GetByte());
			}
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(new byte[92]);
			sendingClass.Write(characterData.Item_Wear[15].GetByte());
			sendingClass.Write(new byte[92]);
			sendingClass.Write(characterData.Item_Wear[16].GetByte());
			sendingClass.Write(new byte[92]);
			sendingClass.Write(characterData.Item_Wear[17].GetByte());
			sendingClass.Write(new byte[92]);
			for (var j = 0; j < 6; j++)
			{
				if (BitConverter.ToInt32(characterData.ThietBiTab3[j].VatPhamSoLuong, 0) == 0)
				{
					characterData.ThietBiTab3[j].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				else
				{
					KiemTraVatPhamHeThong("ThietBiTab3", ref characterData.ThietBiTab3[j]);
				}
				sendingClass.Write(characterData.ThietBiTab3[j].GetByte(), 0, World.Item_Byte_Length_92);
			}

			Client?.SendPak(sendingClass, 4889, SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "View Equipment  error! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void ToDoi_DaoCuQuyTac_PhanPhoi(byte[] data, int length)
	{
		int num = BitConverter.ToInt16(data, 10);
		if (GetCharacterData(SessionID) == null)
		{
			return;
		}
		SendingClass sendingClass = new();
		sendingClass.Write2(1);
		sendingClass.Write2(num);
		if (!World.WToDoi.TryGetValue(TeamID, out var value))
		{
			return;
		}
		value.RuleDistribution = num;
		foreach (var value2 in value.PartyPlayers.Values)
		{
			value2.Client?.SendPak(sendingClass, 11008, value2.SessionID);
		}

	}

	public void ThietLap_PhoBan_DoKho(byte[] packetData, int packetSize)
	{
		int num = BitConverter.ToInt16(packetData, 4);
		int value = BitConverter.ToInt16(packetData, 10);
		if (GetCharacterData(num) == null)
		{
			return;
		}
		SendingClass sendingClass = new();
		sendingClass.Write(value);
		if (!World.WToDoi.TryGetValue(TeamID, out var value2))
		{
			return;
		}
		foreach (var value3 in value2.PartyPlayers.Values)
		{
			value3.Client?.SendPak(sendingClass, 8729, value3.SessionID);
		}
	}

	public void Threading(byte[] packetData, int packetSize)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552A002C01E1001C00030014DC143C0000000000000****************000000000000000000000000000000055AA");
			int num = packetData[24];
			int position = packetData[20];
			var vatPhamId = Item_In_Bag[num].VatPham_ID;
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value) || value.FLD_RESIDE2 != 1)
			{
				return;
			}
			var vatPhamId2 = new byte[4];
			var array2 = new byte[57];
			var itemGlobalId = Item_In_Bag[num].ItemGlobal_ID;
			var text = Item_In_Bag[num].GetVatPham_ID.ToString().Substring(1, 1);
			Buffer.BlockCopy(Item_In_Bag[num].VatPham_byte, 16, array2, 0, 57);
			var text2 = text;
			var text3 = text2;
			if (!(text3 == "2"))
			{
				if (text3 == "1")
				{
					vatPhamId2 = BitConverter.GetBytes(BitConverter.ToInt32(vatPhamId, 0) + ********);
				}
			}
			else
			{
				vatPhamId2 = BitConverter.GetBytes(BitConverter.ToInt32(vatPhamId, 0) - ********);
			}
			SubtractItem(num, 1);
			SubtractItem(position, 1);
			AddItems(itemGlobalId, vatPhamId2, num, BitConverter.GetBytes(1), array2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Sử dụng xe chỉ luồn kim error !!! [" + AccountID + "][" + CharacterName + "]  " + ex.Message);
		}
	}

	public void RegisterNgocLienHoan(byte[] data, int length)
	{
		var array = Converter.HexStringToByte("AA5532002C01C6002C0002000000E5EA0D00000000000000000000000000E6EA0D00000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(data, 10, array, 18, 12);
		Buffer.BlockCopy(data, 22, array, 34, 12);
		NgocLienHoan.Clear();
		for (var i = 0; i < 6; i++)
		{
			var array2 = new byte[4];
			try
			{
				Buffer.BlockCopy(data, i * 4 + 10, array2, 0, 4);
				if (BitConverter.ToInt32(array2, 0) != 0)
				{
					NgocLienHoan.Add(BitConverter.ToInt32(array2, 0));
				}
			}
			catch
			{
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		if (Client != null)
		{
			Client.Send_Map_Data(array, array.Length);
			HeThongNhacNho("Ngọc Liên Hoàn đăng ký thành công!", 10, "Thiên cơ các");
		}
	}

	public void EquipmentPlusUnlock(byte[] data, int length)
	{
		if (World.PhaiChang_MoRaThietBi_ThemChucNang_MoKhoaHayKhong == 0)
		{
			HeThongNhacNho("Trang bị thêm giải tỏa công năng尚未 khai mở, xin liên hệ với nhân viên phục vụ!", 10, "Thiên cơ các");
			return;
		}
		int num = data[10];
		int num2 = data[11];
		if (num2 < 0)
		{
			return;
		}
		KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
		switch (num)
		{
			case 1:
				{
					var array = Converter.HexStringToByte("AA5510002C01DF000200010********00000E98955AA");
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
					Client?.Send_Map_Data(array, array.Length);
					return;
				}
			case 3:
				if (World.KhoaThietBi_TonNguyenBao > 0 && FLD_RXPIONT < World.KhoaThietBi_TonNguyenBao * num2)
				{
					HeThongNhacNho("Point không đủ, khóa một kiện trang bị cần " + World.KhoaThietBi_TonNguyenBao + " Point!", 10, "Thiên cơ các");
				}
				break;
			case 4:
				if (World.MoKhoaThietBi_TonNguyenBao > 0 && FLD_RXPIONT < World.MoKhoaThietBi_TonNguyenBao * num2)
				{
					HeThongNhacNho("Point không đủ, mở khóa một kiện trang bị cần " + World.MoKhoaThietBi_TonNguyenBao + " Point!", 10, "Thiên cơ các");
					break;
				}
				if (WhetherTheSecurityCodeIsVerified)
				{
					UnlockEquipment(data, num2);
					break;
				}
				SendASecurityCodeMessage(8);
				return;
		}
		SendAndUnlockEquipmentData();
	}

	public void SendAndUnlockEquipmentData()
	{
		var array = Converter.HexStringToByte("AA5510002C01DF000200010********00000E98955AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void UnlockEquipment(byte[] data, int soLuong)
	{
		try
		{
			for (var i = 0; i < soLuong; i++)
			{
				int num = data[30 + i * 18];
				var array = new byte[56];
				Item_In_Bag[num].VatPham_KhoaLai = false;
				Item_In_Bag[num].Lock_Move = false;
				var vatPhamId = Item_In_Bag[num].VatPham_ID;
				var itemGlobalId = Item_In_Bag[num].ItemGlobal_ID;
				Buffer.BlockCopy(Item_In_Bag[num].VatPham_byte, 16, array, 0, World.VatPham_ThuocTinh_KichThuoc);
				SubtractItem(num, 1);
				AddItems_Lock(itemGlobalId, vatPhamId, num, BitConverter.GetBytes(1), array, khoaLai: false, 0);
			}
		}
		catch
		{
		}
	}

	public void EquipmentRepair(byte[] packetData, int packetSize)
	{
		var array = Converter.HexStringToByte("AA556A00000025315C00000000000A000000FEFFFFFF00000000B580FA0500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010A755AA");
		var array2 = new byte[2];
		var array3 = new byte[2];
		Buffer.BlockCopy(packetData, 10, array2, 0, 2);
		Buffer.BlockCopy(packetData, 14, array3, 0, 2);
		int num = BitConverter.ToInt16(array2, 0);
		int num2 = BitConverter.ToInt16(array3, 0);
		int num3 = BitConverter.ToInt16(packetData, 22);
		if (num == 1)
		{
			switch (num2)
			{
				case 13:
					{
						var num6 = 0;
						for (var k = 0; k < 6; k++)
						{
							if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_Wear[k].VatPham_ID, 0), out var value3) && (value3.FLD_RESIDE2 == 1 || value3.FLD_RESIDE2 == 2 || value3.FLD_RESIDE2 == 4 || value3.FLD_RESIDE2 == 5 || value3.FLD_RESIDE2 == 6) && value3.FLD_NJ > 0 && !Item_Wear[k].Lock_Move && !Item_Wear[k].Lock_Move && !Item_Wear[k].VatPham_KhoaLai && !Item_Wear[k].VatPham_KhoaLai && Item_Wear[k].FLD_FJ_NJ != value3.FLD_NJ)
							{
								num6 += value3.FLD_NJ - Item_Wear[k].FLD_FJ_NJ;
							}
						}
						if (num6 == 0)
						{
							RepairTips1(num, num2, 251, 0L);
							return;
						}
						Buffer.BlockCopy(array2, 0, array, 10, 2);
						Buffer.BlockCopy(array3, 0, array, 14, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 34, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(num6), 0, array, 42, 4);
						break;
					}
				case 12:
					{
						var num4 = 0;
						for (var i = 0; i < 6; i++)
						{
							if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_Wear[i].VatPham_ID, 0), out var value) && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 6) && value.FLD_NJ > 0 && !Item_Wear[i].Lock_Move && !Item_Wear[i].Lock_Move && !Item_Wear[i].VatPham_KhoaLai && !Item_Wear[i].VatPham_KhoaLai && Item_Wear[i].FLD_FJ_NJ != value.FLD_NJ)
							{
								num4 += value.FLD_NJ - Item_Wear[i].FLD_FJ_NJ;
							}
						}
						if (Player_WuXun < num4)
						{
							RepairTips1(num, num2, 253, Item_In_Bag[num3].GetVatPham_ID);
							HeThongNhacNho("Võ Huân không đủ " + num4 + ", không thể sửa chữa bảo vật!", 10, "Thiên cơ các");
							return;
						}
						for (var j = 0; j < 6; j++)
						{
							if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_Wear[j].VatPham_ID, 0), out var value2) && (value2.FLD_RESIDE2 == 1 || value2.FLD_RESIDE2 == 2 || value2.FLD_RESIDE2 == 4 || value2.FLD_RESIDE2 == 5 || value2.FLD_RESIDE2 == 6) && value2.FLD_NJ > 0 && !Item_Wear[j].Lock_Move && !Item_Wear[j].Lock_Move && !Item_Wear[j].VatPham_KhoaLai && !Item_Wear[j].VatPham_KhoaLai && Item_Wear[j].FLD_FJ_NJ != value2.FLD_NJ)
							{
								var num5 = value2.FLD_NJ - Item_Wear[j].FLD_FJ_NJ;
								if (BitConverter.ToInt32(Item_Wear[j].VatPham_ID, 0) != 0)
								{
									Item_Wear[j].FLD_FJ_NJ = value2.FLD_NJ;
									Player_WuXun -= num5;
								}
							}
						}
						Buffer.BlockCopy(array2, 0, array, 10, 2);
						Buffer.BlockCopy(array3, 0, array, 14, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(num4), 0, array, 42, 4);
						LoadCharacterWearItem();
						CalculateCharacterEquipmentData();
						UpdateMartialArtsAndStatus();
						break;
					}
			}
		}
		else
		{
			switch (num2)
			{
				case 10:
					try
					{
						if (Item_In_Bag[num3].Lock_Move || Item_In_Bag[num3].VatPham_KhoaLai)
						{
							return;
						}
						_hopThanhVatPhamTable.Clear();
						if (_hopThanhVatPhamTable.ContainsKey(1) || (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0), out var value6) && ((value6.FLD_RESIDE2 != 1 && value6.FLD_RESIDE2 != 2 && value6.FLD_RESIDE2 != 4 && value6.FLD_RESIDE2 != 5 && value6.FLD_RESIDE2 != 6 && value6.FLD_RESIDE2 != 7 && value6.FLD_RESIDE2 != 8 && value6.FLD_RESIDE2 != 10) || value6.FLD_NJ < 0)))
						{
							return;
						}
						if (Player_WuXun < value6.FLD_NJ - Item_In_Bag[num3].FLD_FJ_NJ)
						{
							HeThongNhacNho("Võ huân không đủ " + (value6.FLD_NJ - Item_In_Bag[num3].FLD_FJ_NJ) + ", không thể sửa chữa vật phẩm này.");
							return;
						}
						Buffer.BlockCopy(array2, 0, array, 10, 2);
						Buffer.BlockCopy(array3, 0, array, 14, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(num3), 0, array, 22, 4);
						Buffer.BlockCopy(Item_In_Bag[num3].VatPham_ID, 0, array, 26, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 34, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(value6.FLD_NJ - Item_In_Bag[num3].FLD_FJ_NJ), 0, array, 42, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[num3].FLD_FJ_NJ), 0, array, 90, 4);
						HcItimesClass hcItimesClass2 = new();
						hcItimesClass2.Position = num3;
						hcItimesClass2.VatPham = Item_In_Bag[num3].VatPham_byte;
						Item_In_Bag[num3].Lock_Move = true;
						_hopThanhVatPhamTable.Add(1, hcItimesClass2);
					}
					catch (Exception ex3)
					{
						LogHelper.WriteLine(LogLevel.Error, "Sửa chữa thiết bị_Release thiết bị error! [" + AccountID + "]-[" + CharacterName + "]" + ex3.Message);
						return;
					}
					break;
				case 11:
					try
					{
						if (_hopThanhVatPhamTable.TryGetValue(1, out var value5))
						{
							Item_In_Bag[value5.Position].Lock_Move = false;
							_hopThanhVatPhamTable.Clear();
							Buffer.BlockCopy(array2, 0, array, 10, 2);
							Buffer.BlockCopy(array3, 0, array, 14, 2);
							HeThong_HopThanh_MoKhoa();
						}
						Buffer.BlockCopy(array2, 0, array, 10, 2);
						Buffer.BlockCopy(array3, 0, array, 14, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(num3), 0, array, 22, 4);
						Buffer.BlockCopy(Item_In_Bag[num3].VatPham_ID, 0, array, 26, 4);
					}
					catch (Exception ex2)
					{
						LogHelper.WriteLine(LogLevel.Error, "Sửa chữa thiết bị_ Hủy error! [" + AccountID + "]-[" + CharacterName + "]" + ex2.Message);
						return;
					}
					break;
				case 12:
					try
					{
						if (_hopThanhVatPhamTable.Count <= 0)
						{
							return;
						}
						HcItimesClass hcItimesClass = null;
						if (_hopThanhVatPhamTable.ContainsKey(1))
						{
							hcItimesClass = _hopThanhVatPhamTable[1];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass.ItemGlobal_ID, 0))
						{
							return;
						}
						if (Item_In_Bag[hcItimesClass.Position].VatPham_KhoaLai)
						{
							HeThongNhacNho("Bảo vật bị phong ấn, không thể sửa chữa!", 10, "Thiên cơ các");
							return;
						}
						if (!World.ItemList.TryGetValue(BitConverter.ToInt32(hcItimesClass.VatPham_id, 0), out var _))
						{
							return;
						}
						var num7 = 1000;
						if (Player_WuXun < num7)
						{
							HeThongNhacNho("Võ Huân không đủ, đại hiệp cần có [" + num7 + "] điểm!", 20, "Thiên cơ các");
							return;
						}
						if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass.ItemGlobal_ID, 0))
						{
							Item_In_Bag[hcItimesClass.Position].FLD_Do_Ben_Vo_Huan(1000);
							Player_WuXun -= num7;
						}
						Item_In_Bag[hcItimesClass.Position].Lock_Move = false;
						Buffer.BlockCopy(array2, 0, array, 10, 2);
						Buffer.BlockCopy(array3, 0, array, 14, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(hcItimesClass.Position), 0, array, 22, 4);
						Buffer.BlockCopy(Item_In_Bag[hcItimesClass.Position].VatPham_byte, 8, array, 26, World.VatPham_ThuocTinh_KichThuoc);
						Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 34, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(num7), 0, array, 42, 4);
						_hopThanhVatPhamTable.Clear();
						HeThong_HopThanh_MoKhoa();
						Init_Item_In_Bag();
						CalculateCharacterEquipmentData();
						UpdateMartialArtsAndStatus();
						HeThongNhacNho("Đại hiệp bị trừ [" + num7 + "] điểm Võ Huân!", 10, "Thiên cơ các");
					}
					catch (Exception ex)
					{
						LogHelper.WriteLine(LogLevel.Error, "Sửa chữa thiết bị error! [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
						return;
					}
					break;
			}
		}
		if (num != 0)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
	}

	public void RepairTips1(int thaoTacD, int suaChuaId, int type, long vatPhamId)
	{
		var array = Converter.HexStringToByte("AA556A00000025315C00FFFFFFFF0A000000FFFFFFFF00000000B580FA0500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010A755AA");
		Buffer.BlockCopy(BitConverter.GetBytes(suaChuaId), 0, array, 14, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 18, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(vatPhamId), 0, array, 26, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public void Send_BieuTuong(int time)
	{
		var array = Converter.HexStringToByte("AA551600D70402311000D704000000000000010000001E00000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(time), 0, array, 23, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void PkSwitch(byte[] data, int length)
	{
		if (World.ServerChoPKHayKhong == 0)
		{
			HeThongNhacNho("Kênh này không cho phép PK!", 10, "Thiên cơ các");
			return;
		}
		if (Player_Level < 35)
		{
			HeThongNhacNho("Cấp độ dưới 35 không thể tham gia PK!", 10, "Thiên cơ các");
			return;
		}
		if (MapID == 2301)
		{
			if (World.EVEPVP != null && World.Eve90Progress == 3)
			{
				SwitchPkMode(2);
			}
		}
		else if (MapID == 42001)
		{
			if (World.CongThanhChien_BatDau != null)
			{
				SwitchPkMode(2);
			}
		}
		else if (CharacterPKMode == 0)
		{
			SwitchPkMode(1);
		}
		else if (CharacterPKMode == 1)
		{
			SwitchPkMode(2);
		}
		else if (CharacterPKMode == 2)
		{
			SwitchPkMode(0);
		}
		UpdateCharacterData(this);
		UpdateBroadcastCharacterData();
	}
	private static void Level_Cao_TrangBi_HoanDoi(Players player)
	{
		try
		{
			if (player == null)
			{
				return;
			}
			for (var i = 0; i < 96; i++)
			{
				var num = BitConverter.ToInt32(player.Item_In_Bag[i].VatPham_ID, 0);
				if (num == 0)
				{
					continue;
				}
				var itmeClass = World.ItemList[num];
				var num2 = 888;
				switch (itmeClass.FLD_RESIDE2)
				{
					case 1:
						num2 = 0;
						break;
					case 2:
						if (player.Item_Wear[1].FLD_LEVEL < itmeClass.FLD_LEVEL)
						{
							num2 = 1;
						}
						else if (player.Item_Wear[2].FLD_LEVEL < itmeClass.FLD_LEVEL)
						{
							num2 = 2;
						}
						break;
					case 4:
						num2 = 3;
						break;
					case 5:
						num2 = 4;
						break;
					case 6:
						num2 = 5;
						break;
					case 7:
						num2 = 6;
						break;
					case 8:
						if (player.Item_Wear[7].FLD_LEVEL < itmeClass.FLD_LEVEL)
						{
							num2 = 7;
						}
						else if (player.Item_Wear[8].FLD_LEVEL < itmeClass.FLD_LEVEL)
						{
							num2 = 8;
						}
						break;
					case 10:
						if (player.Item_Wear[9].FLD_LEVEL < itmeClass.FLD_LEVEL)
						{
							num2 = 9;
						}
						else if (player.Item_Wear[10].FLD_LEVEL < itmeClass.FLD_LEVEL)
						{
							num2 = 10;
						}
						break;
				}
				if (num2 != 888 && player.Item_Wear[num2].FLD_LEVEL < itmeClass.FLD_LEVEL && (player.Player_Job == itmeClass.FLD_RESIDE1 || itmeClass.FLD_RESIDE1 == 0) && (player.Player_Zx == itmeClass.FLD_ZX || itmeClass.FLD_ZX == 0) && (player.Player_Sex == itmeClass.FLD_SEX || itmeClass.FLD_SEX == 0))
				{
					var vatPhamByte = player.Item_Wear[num2].VatPham_byte;
					player.Item_Wear[num2].VatPham_byte = player.Item_In_Bag[i].VatPham_byte;
					player.Item_In_Bag[i].VatPham_byte = vatPhamByte;
					player.ChangeEquipmentLocation(1, i, 0, num2, player.Item_Wear[num2].VatPham_byte, BitConverter.ToInt32(player.Item_Wear[num2].VatPhamSoLuong, 0));
				}
			}
			player.UpdateCharacterData(player);
			player.UpdateBroadcastCharacterData();
			player.Update_HieuUng_TrangBi();
			player.CalculateCharacterEquipmentData();
			player.UpdateMartialArtsAndStatus();
			player.UpdateMoneyAndWeight();
			player.CapNhat_HP_MP_SP();
			Level_Cap_TrangBi_HoanDoi_VongTay(player);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Level_Cao_TrangBi_HoanDoi - " + ex.Message);
		}
	}

	private static void Level_Cap_TrangBi_HoanDoi_VongTay(Players player)
	{
		try
		{
			if (player == null)
			{
				return;
			}
			for (var i = 0; i < 96; i++)
			{
				var num = BitConverter.ToInt32(player.Item_In_Bag[i].VatPham_ID, 0);
				if (num != 0)
				{
					var itmeClass = World.ItemList[num];
					var num2 = 777;
					switch (itmeClass.FLD_RESIDE2)
					{
						case 2:
							num2 = ((player.Item_Wear[1].FLD_LEVEL < itmeClass.FLD_LEVEL) ? 1 : 2);
							break;
						case 8:
							num2 = ((player.Item_Wear[7].FLD_LEVEL < itmeClass.FLD_LEVEL) ? 8 : 7);
							break;
						case 10:
							num2 = ((player.Item_Wear[9].FLD_LEVEL < itmeClass.FLD_LEVEL) ? 10 : 9);
							break;
					}
					if (num2 != 777 && player.Item_Wear[num2].FLD_LEVEL < itmeClass.FLD_LEVEL)
					{
						var vatPhamByte = player.Item_Wear[num2].VatPham_byte;
						player.Item_Wear[num2].VatPham_byte = player.Item_In_Bag[i].VatPham_byte;
						player.Item_In_Bag[i].VatPham_byte = vatPhamByte;
						player.ChangeEquipmentLocation(1, i, 0, num2, player.Item_Wear[num2].VatPham_byte, BitConverter.ToInt32(player.Item_Wear[num2].VatPhamSoLuong, 0));
					}
				}
			}
			player.UpdateCharacterData(player);
			player.UpdateBroadcastCharacterData();
			player.Update_HieuUng_TrangBi();
			player.CalculateCharacterEquipmentData();
			player.UpdateMartialArtsAndStatus();
			player.UpdateMoneyAndWeight();
			player.CapNhat_HP_MP_SP();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Level_Cap_TrangBi_HoanDoi_VongTay - " + ex.Message);
		}
	}

}
