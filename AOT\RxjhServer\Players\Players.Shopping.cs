﻿using HeroYulgang.Constants;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    
	private void OpenStore(int storeId, int thaoTacD, int tab)
	{
		try
		{
			CurrentlyOperatingNPC = storeId;
			CurrentOperationType = thaoTacD;
			var storeIdByte = new byte[2];
			var actionIdByte = new byte[2];
			var tabByte = new byte[2];
			Buffer.BlockCopy(BitConverter.GetBytes(tab), 0, tabByte, 0, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(storeId), 0, storeIdByte, 0, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(thaoTacD), 0, actionIdByte, 0, 2);
			var response = Converter.HexStringToByte("AA551E0000009100100001000000010000000*********000000000000000000000055AA");
			HeThongNhacNho($"OpenStore {storeId} {thaoTacD}");
			switch (thaoTacD)
			{
				case 56:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					break;
				case 22:
					{
						if (Player_Level < 130)
						{
							HeThongNhacNho("Đẳng cấp dưới 130, không thể tiến vào đấu trường cao cấp!", 10, "Thiên cơ các");
							return;
						}
						if (World.Eve90Progress > 2)
						{
							HeThongNhacNho("Đấu trường cao cấp đang diễn ra tranh tài, xin chờ vòng tiếp theo!", 10, "Thiên cơ các");
							return;
						}
						if (World.evePlayers != null && World.evePlayers.Count >= 2)
						{
							HeThongNhacNho("Số lượng hiệp khách đã đủ, xin chờ vòng tiếp theo!", 10, "Thiên cơ các");
							return;
						}
						if (World.evePlayers.TryGetValue(SessionID, out var _))
						{
							HeThongNhacNho("Không cho phép tham gia lặp lại!", 10, "Thiên cơ các");
							return;
						}
						KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
						if (FLD_RXPIONT < World.PhiVaoCua_ToiThieu)
						{
							HeThongNhacNho("Điểm số không đủ [" + World.PhiVaoCua_ToiThieu + "], không thể tiến vào đấu trường cao cấp!", 10, "Thiên cơ các");
							return;
						}
						KiemSoatNguyenBao_SoLuong(World.PhiVaoCua_ToiThieu, 0);
						SwitchPkMode(0);
						World.evePlayers.Add(SessionID, this);
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
						Save_NguyenBaoData();
						break;
					}
				case 1:
					if (storeId == 0)
					{
						OpenWarehouse = false;
						HeThong_HopThanh_MoKhoa();
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
						CurrentlyOperatingNPC = 0;
						CurrentOperationType = 0;
						break;
					}
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					if (storeId == 6 && thaoTacD == 1)
					{
						World.conn.Transmit("GET_SERVER_LIST|" + AccountID + "|" + OriginalServerSerialNumber + "|" + OriginalServerIP + "|" + OriginalServerPort + "|" + OriginalServerID);
					}
					break;
				case 2:
					_hopThanhVatPhamTable?.Clear();
					NguyenBaoHopThanhMoRa = 0;
					OpenWarehouse = false;
					HeThong_HopThanh_MoKhoa();
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					CurrentlyOperatingNPC = 0;
					CurrentOperationType = 0;
					if ((storeId == 1 || storeId == 14 || storeId == 18 || storeId == 41 || storeId == 93 || storeId == 11 || storeId == 23 || storeId == 43) && CharacterName.Length != 0)
					{
						SavePersonalWarehouseAsync().GetAwaiter().GetResult();
						SaveComprehensiveWarehouseAsync().GetAwaiter().GetResult();
					}
					break;
				case 3:
					{
						var shopListAll = ShopClass.GetShopListAll(storeId);
						new List<ShopClass>();
						var list = ((tab != 0) ? ShopClass.GetShopList(storeId, tab - 101) : ShopClass.GetShopList(storeId, 0));
						SendingClass sendingClass2 = new();
						sendingClass2.Write4(thaoTacD);
						sendingClass2.Write4(thaoTacD);
						sendingClass2.Write4(storeId);
						sendingClass2.Write4(list.Count);
						sendingClass2.Write4(0);
						if (tab == 0)
						{
							var num = shopListAll.Count / 60;
							if (shopListAll.Count % 60 > 0)
							{
								num++;
							}
							sendingClass2.Write8(num);
						}
						else
						{
							sendingClass2.Write8(tab);
						}
						foreach (var item in list)
						{
							sendingClass2.Write8(item.FLD_PID);
							if (item.FLD_MAGICZh > 0)
							{
								sendingClass2.Write8(item.FLD_MAGICZh);
								if (item.FLD_MAGIC1 > 0)
								{
									sendingClass2.Write8(item.FLD_MAGIC1);
								}
								if (item.FLD_MAGIC2 > 0)
								{
									sendingClass2.Write8(item.FLD_MAGIC2);
								}
								if (item.FLD_MAGIC3 > 0)
								{
									sendingClass2.Write8(item.FLD_MAGIC3);
								}
								if (item.FLD_MAGIC4 > 0)
								{
									sendingClass2.Write8(item.FLD_MAGIC4);
								}
							}
							else
							{
								sendingClass2.Write8(0L);
							}
							sendingClass2.Write8(-1L);
						}

						Client?.SendPak(sendingClass2, 37120, SessionID);
						return;
					}
				case 5:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					OpenWarehouse = true;
					OpenPersonalWarehouse();
					OpenTheComprehensiveWarehouse();
					break;
				case 9:
					switch (BitConverter.ToInt16(storeIdByte, 0))
					{
						case 27:
							Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
							break;
						case 16:
							Buffer.BlockCopy(storeIdByte, 0, response, 18, 1);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
							break;
						case 15:
							Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
							break;
						case 33:
							Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
							break;
						case 28:
							Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
							break;
						case 74:
							Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
							if (Player_Zx != 1)
							{
							}
							break;
						case 34:
							Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
							break;
					}
					break;
				case 12:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(12), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(12), 0, response, 14, 2);
					GuiDi_TheLucChien_TinTuc1();
					break;
				case 14:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 15:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					if (World.CoMo_ThiTruongTraoDoiTienXu == 1 && MapID == 1201)
					{
					}
					break;
				case 17:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					if (World.CoMo_ThiTruongTraoDoiTienXu == 1 && MapID != 1201 && MapID != 801)
					{
					}
					break;
				case 38:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 23:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 122:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 62:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 63:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 65:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 66:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 67:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 68:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 69:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 70:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 71:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 72:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 73:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 74:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 75:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 76:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 77:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 78:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 79:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 80:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 81:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 82:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 92:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 94:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 101:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 102:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 103:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 105:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 110:
					{
						OpenWarehouse = true;
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
						SendingClass sendingClass = new();
						sendingClass.Write2(SessionID);
						Client?.SendPak(sendingClass, 16663, SessionID);
						break;
					}
				case 111:
					OpenWarehouse = false;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					break;
				case 117:
					KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					if (FLD_RXPIONT >= World.HeThong_MonChien_CanNguyenBao && Player_Money >= 50000000)
					{
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					}
					else
					{
						HeThongNhacNho("Đơn đăng ký bang chiến tối thiểu cần [" + World.HeThong_MonChien_CanNguyenBao + "] điểm và 50 triệu ngân lượng!", 10, "Thiên cơ các");
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					}
					break;
				case 173:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 176:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 177:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 137:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					if (WhetherMarried == 0 && GiaiTruQuanHe_Countdown > 0)
					{
						actionIdByte[0] = 2;
						_hopThanhVatPhamTable?.Clear();
						NguyenBaoHopThanhMoRa = 0;
						OpenWarehouse = false;
						HeThong_HopThanh_MoKhoa();
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
						CurrentlyOperatingNPC = 0;
						CurrentOperationType = 0;
						DissolveTheRelationshipBetweenMenAndWomen();
					}
					break;
				case 139:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					break;
				case 140:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					break;
				case 142:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					if (WhetherMarried == 1 && GiaiTruQuanHe_Countdown >= 0)
					{
						actionIdByte[0] = 2;
						_hopThanhVatPhamTable?.Clear();
						NguyenBaoHopThanhMoRa = 0;
						OpenWarehouse = false;
						HeThong_HopThanh_MoKhoa();
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
						CurrentlyOperatingNPC = 0;
						CurrentOperationType = 0;
						DissolveTheRelationshipBetweenMenAndWomen();
					}
					else if (WhetherMarried == 0 && GiaiTruQuanHe_Countdown >= 0)
					{
						actionIdByte[0] = 2;
						_hopThanhVatPhamTable?.Clear();
						NguyenBaoHopThanhMoRa = 0;
						OpenWarehouse = false;
						HeThong_HopThanh_MoKhoa();
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
						CurrentlyOperatingNPC = 0;
						CurrentOperationType = 0;
						DissolveTheRelationshipBetweenMenAndWomen();
					}
					break;
				case 145:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 146:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 147:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					if (WhetherMarried == 1 && GiaiTruQuanHe_Countdown > 0)
					{
						actionIdByte[0] = 2;
						_hopThanhVatPhamTable?.Clear();
						NguyenBaoHopThanhMoRa = 0;
						OpenWarehouse = false;
						HeThong_HopThanh_MoKhoa();
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
						Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
						CurrentlyOperatingNPC = 0;
						CurrentOperationType = 0;
						DissolveTheRelationshipBetweenMenAndWomen();
					}
					break;
				case 148:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 149:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 150:
					if (Player_Job_level < 6)
					{
						HeThongNhacNho("Đại hiệp chỉ có thể tham gia sau khi thăng thiên!", 10, "Thiên cơ các");
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					}
					else
					{
						Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					}
					break;
				case 153:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 154:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 156:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 157:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					// switch (RNG.Next(2, 10))
					// {
					// }
					break;
				case 158:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 199:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					break;
				case 191:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 213:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					break;
				case 209:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 243:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 238:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				case 324:
					OpenWarehouse = true;
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					_hopThanhVatPhamTable.Clear();
					break;
				default:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					break;
				case 304:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					break;
				case 305:
					Thread.Sleep(50);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					if (World.CongThanhChien_Progress != 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10235), 0, response, 14, 2);
					}
					else if (GuildName == "")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (GangCharacterLevel != 6)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (ThongBao_CongThanh == 1)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10234), 0, response, 14, 2);
					}
					else if (MonPhai_LienMinh_MinhChu != GuildName && MonPhai_LienMinh_MinhChu != "")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10231), 0, response, 14, 2);
					}
					break;
				case 306:
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					if (World.CongThanhChien_Progress != 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10235), 0, response, 14, 2);
					}
					else if (GuildName == "")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (GangCharacterLevel != 6)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (MonPhai_LienMinh_MinhChu == GuildName)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10232), 0, response, 14, 2);
					}
					else if (!(MonPhai_LienMinh_MinhChu == "") && MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10232), 0, response, 14, 2);
					}
					break;
				case 307:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					if (World.CongThanhChien_Progress != 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10235), 0, response, 14, 2);
					}
					else if (GuildName == "")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (GangCharacterLevel != 6)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10231), 0, response, 14, 2);
					}
					else if (MonPhai_LienMinh_MinhChu == "")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10231), 0, response, 14, 2);
					}
					break;
				case 308:
					Thread.Sleep(50);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					if (World.CongThanhChien_Progress != 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10235), 0, response, 14, 2);
					}
					else if (GuildName == "")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (GangCharacterLevel != 6)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10230), 0, response, 14, 2);
					}
					else if (!(World.NguoiChiemGiu_Den_Tenma == MonPhai_LienMinh_MinhChu) && MonPhai_LienMinh_MinhChu != GuildName)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10231), 0, response, 14, 2);
					}
					break;
				case 309:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					if (World.CongThanhChien_Progress != 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10235), 0, response, 14, 2);
					}
					break;
				case 310:
					OpenWarehouse = true;
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(0), 0, response, 16, 2);
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					break;
				case 311:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					if (World.CongThanhChien_Progress != 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10235), 0, response, 14, 2);
						break;
					}
					if (GangCharacterLevel != 6)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10231), 0, response, 14, 2);
					}
					if (World.NguoiChiemGiu_Den_Tenma != GuildName)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10231), 0, response, 14, 2);
					}
					break;
				case 312:
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(actionIdByte, 0, response, 14, 2);
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					if (World.CongThanhChien_Progress != 0)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(10235), 0, response, 14, 2);
					}
					else if (MonPhai_LienMinh_MinhChu == "")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(0), 0, response, 14, 2);
					}
					else if (World.NguoiChiemGiu_Den_Tenma != MonPhai_LienMinh_MinhChu)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(0), 0, response, 14, 2);
					}
					break;
				case 315:
					Buffer.BlockCopy(storeIdByte, 0, response, 18, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, response, 14, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, response, 4, 2);
					Client?.Send_Map_Data(response, response.Length);
					if (World.CongThanhChien_Progress < 2)
					{
						Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(10251), 0, response, 14, 2);
						break;
					}
					if (MonPhai_LienMinh_MinhChu == "")
					{
						HeThongNhacNho("Mời đại hiệp đăng ký Công Thành Chiến trước!", 10, "Thiên cơ các");
						return;
					}
					if (MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
					{
						Mobile(-437f, 89f, 15f, 42001, 0);
					}
					else
					{
						Mobile(-430f, -660f, 15f, 42001, 0);
					}
					//GuiDi_CongThanhChien_ThoiGian_ConLai((int)CongThanhChien.KhiTienLenTrinh_KetThuc_ThoiGian.Subtract(DateTime.Now).TotalSeconds);
					SwitchPkMode(1);
					return;
				case 300:
					OpenWarehouse = true;
					Buffer.BlockCopy(actionIdByte, 0, response, 10, 2);
					Buffer.BlockCopy(BitConverter.GetBytes(245), 0, response, 14, 1);
					Buffer.BlockCopy(BitConverter.GetBytes(255), 0, response, 15, 1);
					Buffer.BlockCopy(BitConverter.GetBytes(255), 0, response, 16, 1);
					Buffer.BlockCopy(BitConverter.GetBytes(255), 0, response, 17, 1);
					Buffer.BlockCopy(BitConverter.GetBytes(0), 0, response, 18, 2);
					_hopThanhVatPhamTable.Clear();
					break;
			}
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, response, 4, 2);
			Client?.Send_Map_Data(response, response.Length);
			if (thaoTacD == 221)
			{
				SendAttendancePacket();
			}
			if (thaoTacD == 220)
			{
				SendAttendance14DayPacket();
			}
		}
		catch
		{
		}
	}
	
	public void OpenStore(byte[] packetData, int packetSize)
	{
		try
		{
			if (NhanVat_HP > 0 && !PlayerTuVong && !Exiting)
			{
				PacketModification(packetData, packetSize);
				var storeId = new byte[2];
				var actionId = new byte[2];
				var tabId = new byte[2];
				var workingIndex = new byte[2];
				Buffer.BlockCopy(packetData, 10, actionId, 0, 2);
				Buffer.BlockCopy(packetData, 18, storeId, 0, 2);
				Buffer.BlockCopy(packetData, 32, workingIndex, 0, 2);
				Buffer.BlockCopy(packetData, 30, tabId, 0, 2);
				int tab = BitConverter.ToInt16(tabId, 0);
				WorkingItemIndex = BitConverter.ToInt16(workingIndex, 0);
				OpenStore(BitConverter.ToInt16(storeId, 0), BitConverter.ToInt16(actionId, 0), tab);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở cửa hàng error [" + AccountID + "][" + CharacterName + "]  " + ex.Message);
		}
	}

	public void BuyAndSellThings(byte[] packetData, int packetSize)
	{
		var num = 0;
		var playerMoney = Player_Money;
		try
		{
			if (Exiting)
			{
				return;
			}
			PacketModification(packetData, packetSize);
			int type = packetData[10];
			int num3 = packetData[68];
			var itemId = BitConverter.ToInt32(packetData, 18);
			var amount = BitConverter.ToInt32(packetData, 26);
			if (amount > 0 && amount <= 9999)
			{
				num = 1;
				if (Exiting)
				{
					return;
				}
				var array = new byte[56];
				if (World.ItemList.TryGetValue(itemId, out var value))
				{
					if (value.FLD_QUESTITEM != 1)
					{
						if (value.FLD_RESIDE2 == 17 || itemId == ItemDef.Item.NhietHuyetThach || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ********** || itemId == ItemDef.Item.NhietHuyetThach)
						{
							amount = 1;
						}
						if (itemId >= 1000001170 && itemId <= 1000001175)
						{
							HeThongNhacNho("Không thể bán Thần Thú tại cửa hàng!!", 10, "Thiên cơ các");
							return;
						}
						// BUy
						if (type == 1)
						{
							if (amount < 1)
							{
								return;
							}
							var shopClass = ShopClass.Getwp(itemId);
							if (shopClass == null)
							{
								// HeThongNhacNho("Phiên bản MGAME không cho phép mua bảo vật này!", 10, "Thiên cơ các");
								return;
							}
							var fLdMoney = shopClass.FLD_MONEY;
							var num6 = shopClass.FLD_MONEY * amount;
							if (num6 < 0)
							{
								return;
							}
							if (shopClass.CanVoHuan != 0 && Player_WuXun < shopClass.CanVoHuan)
							{
								PurchaseItemReminder(13);
								return;
							}
							if (shopClass.FLD_Coin != 0 && BronzeCoin < shopClass.FLD_Coin * amount)
							{
								PurchaseItemReminder(13);
								return;
							}

							if (Player_Money < num6)
							{
								PurchaseItemReminder(13);
								return;
							}
							if (value.FLD_WEIGHT * amount + CharacterCurrentWeight >= TheTotalWeightOfTheCharacter)
							{
								PurchaseItemReminder(11);
								return;
							}
							num = 2;
							var flag = false;
							var parcelVacancy = GetParcelVacancy(this);
							if (parcelVacancy == -1)
							{
								PurchaseItemReminder(14);
								return;
							}
							num = 3;
							var num7 = shopClass.FLD_MAGIC0;
							var fLdMagic = shopClass.FLD_MAGIC1;
							if (num7 == 0)
							{
								switch (itemId)
								{
									case ItemDef.Item.KimCuongThachRandom:
										itemId = ItemDef.Item.KimCuongThach;
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num12 = 0; num12 < amount - 1; num12++)
											{
												var parcelVacancy12 = GetParcelVacancy(this);
												var value12 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy12, 1, value12, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.KimCuongThachRandom, 1);
										break;
									case ItemDef.Item.HanNgocThachRandom:
										itemId = ItemDef.Item.HanNgocThach;
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var k = 0; k < amount - 1; k++)
											{
												var parcelVacancy4 = GetParcelVacancy(this);
												var value4 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy4, 1, value4, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.HanNgocThach, 1);
										break;
									case ItemDef.Item.NhietHuyetThach:
										num7 = World.GetValue(ItemDef.Item.NhietHuyetThach, 1);
										break;
									case ItemDef.Item.HanNgocThach:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num15 = 0; num15 < amount - 1; num15++)
											{
												var parcelVacancy15 = GetParcelVacancy(this);
												var value15 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy15, 1, value15, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.HanNgocThach, 1);
										break;
									case ItemDef.Item.KimCuongThach:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var l = 0; l < amount - 1; l++)
											{
												var parcelVacancy5 = GetParcelVacancy(this);
												var value5 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy5, 1, value5, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.KimCuongThach, 1);
										break;
									case ItemDef.Item.KimCuongThachSieuCap:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num10 = 0; num10 < amount - 1; num10++)
											{
												var parcelVacancy10 = GetParcelVacancy(this);
												var value10 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy10, 1, value10, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(itemId, 1);
										break;
									case ItemDef.Item.KimCuongThachHonNguyen:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num11 = 0; num11 < amount - 1; num11++)
											{
												var parcelVacancy11 = GetParcelVacancy(this);
												var value11 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy11, 1, value11, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(itemId, 1);
										break;
									case 1000001620:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num8 = 0; num8 < amount - 1; num8++)
											{
												var parcelVacancy8 = GetParcelVacancy(this);
												var value8 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy8, 1, value8, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(itemId, 1);
										break;
									case ItemDef.Item.KimCuongThachCaoCap:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num13 = 0; num13 < amount - 1; num13++)
											{
												var parcelVacancy13 = GetParcelVacancy(this);
												var value13 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy13, 1, value13, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.KimCuongThachCaoCap, 1);
										break;
									case ItemDef.Item.HanNgocThachCaoCap:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var m = 0; m < amount - 1; m++)
											{
												var parcelVacancy6 = GetParcelVacancy(this);
												var value6 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy6, 1, value6, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.HanNgocThachCaoCap, 1);
										break;
									case ItemDef.Item.KimCuongThach_DaKich:
										itemId = ItemDef.Item.KimCuongThach;
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var n = 0; n < amount - 1; n++)
											{
												var parcelVacancy7 = GetParcelVacancy(this);
												var value7 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy7, 1, value7, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.KimCuongThach_DaKich, 1);
										break;
									case ItemDef.Item.KimCuongThach_VoCong:
										itemId = ItemDef.Item.KimCuongThach;
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num14 = 0; num14 < amount - 1; num14++)
											{
												var parcelVacancy14 = GetParcelVacancy(this);
												var value14 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy14, 1, value14, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(ItemDef.Item.KimCuongThach_VoCong, 1);
										break;
									case ItemDef.Item.ThuocTinhThachRandom:
										itemId = ItemDef.Item.ThuocTinhThach;
										num7 = World.GetValue(ItemDef.Item.ThuocTinhThach, 1);
										break;
									case 800000030:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var num9 = 0; num9 < amount - 1; num9++)
											{
												var parcelVacancy9 = GetParcelVacancy(this);
												var value9 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy9, 1, value9, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(itemId, 1);
										break;
									case 800000034:
										if (amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var j = 0; j < amount - 1; j++)
											{
												var parcelVacancy3 = GetParcelVacancy(this);
												var value3 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy3, 1, value3, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(itemId, 1);
										break;
									case 800000035:
										itemId = 800000031;
										shopClass.FLD_MAGIC0 = World.GetValue(800000035, 1);
										break;
									case 800000036:
										num7 = RNG.Next(200005, 200015);
										break;
									case 800000037:
										num7 = RNG.Next(200010, 200030);
										break;
									case ItemDef.TapHonThach.HaCapTapHonChau:
										shopClass.FLD_MAGIC0 = 1000;
										shopClass.FLD_MAGIC1 = 50;
										break;
									case ItemDef.TapHonThach.TrungCapTapHonCha:
										shopClass.FLD_MAGIC0 = 1000;
										shopClass.FLD_MAGIC1 = 150;
										break;
									case ItemDef.TapHonThach.ThuongCapTapHonChau:
										shopClass.FLD_MAGIC0 = 1000;
										shopClass.FLD_MAGIC1 = 699;
										break;
									case ItemDef.TapHonThach.TuLinhTapHonChau:
										shopClass.FLD_MAGIC0 = 1000;
										shopClass.FLD_MAGIC1 = 2499;
										break;
									case ItemDef.Item.HanNgocThachSieuCap:
										if ((itemId == ItemDef.Item.KimCuongThach || itemId == ItemDef.Item.HanNgocThach || itemId == ItemDef.Item.KimCuongThachRandom || itemId == ItemDef.Item.HanNgocThachRandom || itemId == ItemDef.Item.KimCuongThachCaoCap || itemId == ItemDef.Item.HanNgocThachCaoCap || itemId == ItemDef.Item.KimCuongThach_DaKich || itemId == ItemDef.Item.KimCuongThach_VoCong || itemId == ItemDef.Item.KimCuongThachSieuCap || itemId == ItemDef.Item.HanNgocThachSieuCap || itemId == ItemDef.Item.KimCuongThachHonNguyen || itemId == ItemDef.Item.HanNgocThachHonNguyen || itemId == 1000001620) && amount > 1)
										{
											if (amount > GetParcelVacancyNumber())
											{
												PurchaseItemReminder(14);
												return;
											}
											flag = true;
											for (var i = 0; i < amount - 1; i++)
											{
												var parcelVacancy2 = GetParcelVacancy(this);
												var value2 = World.GetValue(itemId, 1);
												AddItem_ThuocTinh_int(itemId, parcelVacancy2, 1, value2, 0, 0, 0, 0, 0, 0, 0, 0, 0);
											}
										}
										num7 = World.GetValue(itemId, 1);
										break;
								}
							}
							num = 4;
							var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
							Buffer.BlockCopy(BitConverter.GetBytes(num7), 0, array, 0, 4);
							Buffer.BlockCopy(BitConverter.GetBytes(fLdMagic), 0, array, 4, 4);
							Buffer.BlockCopy(BitConverter.GetBytes(shopClass.FLD_MAGIC2), 0, array, 8, 4);
							Buffer.BlockCopy(BitConverter.GetBytes(shopClass.FLD_MAGIC3), 0, array, 12, 4);
							Buffer.BlockCopy(BitConverter.GetBytes(shopClass.FLD_MAGIC4), 0, array, 16, 4);
							num = 5;
							long num16;
							switch (itemId)
							{
								default:
									num16 = ((shopClass.FLD_MONEY != 0L) ? (shopClass.FLD_MONEY * amount) : (value.FLD_SALE_MONEY * (long)amount));
									if (NhanVatThienVaAc <= -1 && NhanVatThienVaAc > -500)
									{
										num16 += (int)(num16 * 0.3);
									}
									else if (NhanVatThienVaAc <= -500 && NhanVatThienVaAc > -5000)
									{
										num16 += (int)(num16 * 0.5);
									}
									else if (NhanVatThienVaAc <= -5000 && NhanVatThienVaAc > -10000)
									{
										num16 *= 2;
									}
									else if (NhanVatThienVaAc <= -10000 && NhanVatThienVaAc > -30000)
									{
										num16 *= 4;
									}
									else if (NhanVatThienVaAc <= -30000)
									{
										num16 *= 4;
									}
									if (num16 < 0 || num16 > 20000000000L)
									{
										HeThongNhacNho("Giới hạn không vượt quá " + 20000000000L + " tỷ ngân lượng!", 10, "Thiên cơ các");
										return;
									}
									if (shopClass.CanVoHuan != 0)
									{
										Player_WuXun -= shopClass.CanVoHuan;
										UpdateMartialArtsAndStatus();
										WuxunConsumptionTips(-shopClass.CanVoHuan);
									}
									if (Player_Money < num16)
									{
										PurchaseItemReminder(13);
										HeThongNhacNho("Bạn còn thiếu: " + (num16 - (double)Player_Money) + " Lượng.");
										return;
									}
									if (shopClass.FLD_Coin > 0)
									{
										var coinItem = FindItemByItemID(World.BronzeCurrency);
										if (coinItem == null)
										{
											PurchaseItemReminder(13);
											return;
										}
										coinItem.Lock_Move = true;
										if (coinItem.GetVatPhamSoLuong < shopClass.FLD_Coin * amount)
										{
											PurchaseItemReminder(13);
											return;
										}
										if (coinItem.GetVatPhamSoLuong - shopClass.FLD_Coin * amount == 0)
										{
											coinItem.VatPham_byte = new byte[World.Item_Db_Byte_Length];
										}
										else
										{
											coinItem.VatPhamSoLuong = BitConverter.GetBytes(coinItem.GetVatPhamSoLuong - shopClass.FLD_Coin * amount);
										}

										coinItem.Lock_Move = false;
										Init_Item_In_Bag();
									}
									Player_Money -= num16;
									break;
								case ItemConstants.CHIEN_PHIEU:
									if (Player_Money < 100200000)
									{
										PurchaseItemReminder(13);
										return;
									}
									num16 = 100200000L;
									Player_Money -= 100200000L;
									CharacterCurrentWeight += value.FLD_WEIGHT * amount;
									break;
								case *********:
									if (Player_Money < 5010000000L)
									{
										PurchaseItemReminder(13);
										return;
									}
									num16 = 5050000000L;
									Player_Money -= 5050000000L;
									CharacterCurrentWeight += value.FLD_WEIGHT * amount;
									break;
								case *********:
									if (Player_Money < 10020000000L)
									{
										PurchaseItemReminder(13);
										return;
									}
									num16 = 10*********L;
									Player_Money -= 10*********L;
									CharacterCurrentWeight += value.FLD_WEIGHT * amount;
									break;
							}
							var bytes2 = BitConverter.GetBytes(num16);
							if (flag)
							{
								amount = 1;
								parcelVacancy = GetParcelVacancy(this);
							}
							BuyAndSellItems(parcelVacancy, type, bytes2, BitConverter.GetBytes(amount), bytes, BitConverter.GetBytes(itemId), array, shopClass.FLD_DAYS, shopClass.FLD_BD);
							var playerMoney2 = Player_Money;
							if (World.GhiLogMuaNPC == 1)
							{
								var text = "]" + AccountID + "] [" + CharacterName + "] Gia:[" + num16 + "] Gold truoc:[" + playerMoney + "] Gold sau:[" + playerMoney2 + "] Item:[" + itemId + "] Ten DB:[" + value.ItmeNAME + "] =MUA=:[] SL:[" + amount + "] --1:[" + num7 + "] --2:[" + fLdMagic + "] --3:[" + shopClass.FLD_MAGIC2 + "] --4:[" + shopClass.FLD_MAGIC3 + "] --5:[" + shopClass.FLD_MAGIC4 + "]";
								// logo.Log_Mua_NPC(text, UserName);
							}
							GameDb.StoreRecord(AccountID, CharacterName, itemId, amount, 1, num16);
							//RxjhClass.StoreRecord(AccountID, CharacterName, num4, value.ItmeNAME, "Mua", num5, num16, num7, fLdMagic, shopClass.FLD_MAGIC2, shopClass.FLD_MAGIC3, shopClass.FLD_MAGIC4);
						}
						else
						{
							if (amount < 1)
							{
								return;
							}
							num = 7;
							var xVatPhamLoai = Item_In_Bag[num3];
							if (Item_In_Bag[num3].VatPham_KhoaLai || BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) == 0 || BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) == 0)
							{
								return;
							}
							if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != itemId)
							{
								LogHelper.WriteLine(LogLevel.Debug, "MuaBan Điều BUG 22 [" + itemId + "][" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "]  ");
								return;
							}
							num = 8;
							if (!World.ItemList.TryGetValue(BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0), out var value16))
							{
								return;
							}
							if (value16.FLD_LOCK == 1)
							{
								HeThongNhacNho("Bảo vật này cấm bán!", 10, "Thiên cơ các");
								return;
							}
							long num17;
							switch (itemId)
							{
								default:
									if (value.FLD_TYPE == 6 || amount < 1 || BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != itemId || xVatPhamLoai.GetVatPhamSoLuong < amount)
									{
										HeThongNhacNho("Số lượng [" + amount + "] có vấn đề!!", 10, "Thiên cơ các");
										return;
									}
									num17 = value.FLD_RECYCLE_MONEY * amount;
									if (FLD_NhanVat_ThemVao_GiaBanTiLePhanTram <= 0.0)
									{
										FLD_NhanVat_ThemVao_GiaBanTiLePhanTram = 0.0;
									}
									else
									{
										num17 = (long)(num17 * (1.0 + FLD_NhanVat_ThemVao_GiaBanTiLePhanTram));
									}
									if (num17 < 0 || num17 > 2000000000)
									{
										HeThongNhacNho("Số ngân lượng bán được [" + num17 + "] lượng!!", 10, "Thiên cơ các");
										return;
									}
									if (Player_Money + num17 > World.Money_Max)
									{
										HeThongNhacNho("Số ngân lượng mang theo tối đa là [" + World.Money_Max / ********* + "] lượng!", 10, "Thiên cơ các");
										return;
									}
									Player_Money += num17;
									break;
								case ItemConstants.CHIEN_PHIEU:
									if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != ItemConstants.CHIEN_PHIEU || xVatPhamLoai.GetVatPhamSoLuong < amount)
									{
										HeThongNhacNho("Bảo vật này gặp sai sót [" + ItemConstants.CHIEN_PHIEU + "]!!", 10, "Thiên cơ các");
										return;
									}
									num17 = *********L;
									if (Player_Money + ********* > World.Money_Max)
									{
										HeThongNhacNho("Số ngân lượng mang theo tối đa là [" + World.Money_Max / ********* + "] lượng!", 10, "Thiên cơ các");
										return;
									}
									Player_Money += num17;
									break;
								case *********:
									if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != ********* || xVatPhamLoai.GetVatPhamSoLuong < amount)
									{
										HeThongNhacNho("Bảo vật này gặp sai sót [" + ********* + "]!!", 10, "Thiên cơ các");
										return;
									}
									num17 = 5000000000L;
									if (Player_Money + 5000000000L > World.Money_Max)
									{
										HeThongNhacNho("Số ngân lượng mang theo tối đa là [" + World.Money_Max / ********* + "] lượng!", 10, "Thiên cơ các");
										return;
									}
									Player_Money += num17;
									break;
								case *********:
									if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) != ********* || xVatPhamLoai.GetVatPhamSoLuong < amount)
									{
										HeThongNhacNho("Bảo vật này gặp sai sót [" + ********* + "]!!", 10, "Thiên cơ các");
										return;
									}
									num17 = *********00L;
									if (Player_Money + *********00L > World.Money_Max)
									{
										HeThongNhacNho("Số ngân lượng mang theo tối đa là [" + World.Money_Max / ********* + "] lượng!", 10, "Thiên cơ các");
										return;
									}
									Player_Money += num17;
									break;
							}
							var playerMoney3 = Player_Money;
							switch (value.FLD_RESIDE2)
							{
								case 4:
									{
										HashSet<int> hashSet = new() { 8, 9, 11, 12, 13 };
										HashSet<int> hashSet2 = new()
								{
									*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
									*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
									*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
									*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
									*********, *********, *********, *********, *********, *********, *********, *********, *********, *********,
									*********, *********, *********, *********, *********
								};
										if (hashSet.Contains(value.FLD_RESIDE1) && hashSet2.Contains(value.FLD_PID))
										{
											HeThongNhacNho("Thần binh phong ấn của anh hùng không thể bán!", 10, "Thiên cơ các");
											return;
										}
										break;
									}
								case 1:
								case 2:
								case 5:
								case 6:
								case 7:
								case 8:
								case 10:
								case 14:
								case 15:

									//RxjhClass.StoreRecord(AccountID, CharacterName, num4, value.ItmeNAME, "出售", num5, num17, Item_In_Bag[num3].FLD_MAGIC0, Item_In_Bag[num3].FLD_MAGIC1, Item_In_Bag[num3].FLD_MAGIC2, Item_In_Bag[num3].FLD_MAGIC3, Item_In_Bag[num3].FLD_MAGIC4);
									GameDb.StoreRecord(AccountID, CharacterName, itemId, amount, 1, num17);
									break;
							}
							check_bug_gold_tang_bat_thuong = true;
							var bytes3 = BitConverter.GetBytes(num17);
							BuyAndSellItems(num3, type, bytes3, BitConverter.GetBytes(amount), null, BitConverter.GetBytes(itemId), array, 0, 0);
							if (World.GhiLogBanNPC == 1)
							{
								var text2 = "]" + AccountID + "] [" + CharacterName + "] Gia:[" + num17 + "] Gold truoc:[" + playerMoney + "] Gold sau:[" + playerMoney3 + "] Item:[" + itemId + "] Ten DB:[" + value.ItmeNAME + "] =BAN=:[] SL:[" + amount + "] magic 0:[" + Item_In_Bag[num3].FLD_MAGIC0 + "] magic 1:[" + Item_In_Bag[num3].FLD_MAGIC1 + "] magic 2:[" + Item_In_Bag[num3].FLD_MAGIC2 + "] magic 3:[" + Item_In_Bag[num3].FLD_MAGIC3 + "] magic 4:[" + Item_In_Bag[num3].FLD_MAGIC4 + "]";
								// logo.Log_Ban_NPC(text2, UserName);
							}
						}
						UpdateMoneyAndWeight();
					}
					else
					{
						HeThongNhacNho("Bảo vật này thuộc nhiệm vụ, đổi lại trong QUESTITEM == 0!!", 10, "Thiên cơ các");
					}
				}
				else
				{
					LogHelper.WriteLine(LogLevel.Error, "Mua hoặc Bán lỗi vì không có vật phẩm [" + itemId + "][" + amount + "]");
				}
			}
			else
			{
				HeThongNhacNho("Số lượng bằng 0, không thể mua bảo vật!!", 10, "Thiên cơ các");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "MuaBan Điều error [" + AccountID + "][" + CharacterName + "]  [" + BitConverter.ToInt32(packetData, 18) + "]  [" + num + "]  " + ex.Message);
		}
	}

	public void Time_Countdown_Yellow(int type, int time)
	{
		var array = Converter.HexStringToByte("AA550E005B00BB18080001000100B004000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 10, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(time), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void WuxunConsumptionTips(int soLuong)
	{
		var array = Converter.HexStringToByte("AA5516008A02D218080013000000F0D8FFFF000000000000DA1F55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public void BuyAndSellItems(int position, int muaBan, byte[] price, byte[] vatPhamSoLuong, byte[] itemGlobalId, byte[] vatPhamId, byte[] vatPhamThuocTinh, int ngaySuDung, int khoaLai)
	{
		try
		{
			byte[] array = Converter.HexStringToByte("AA558E00000393008000000000000000000000000000000000000*********00000000000000000000000000000000000000000000000000000000000000000000000*********00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(muaBan), 0, array, 10, 1);
			Buffer.BlockCopy(BitConverter.GetBytes(muaBan), 0, array, 14, 1);
			Buffer.BlockCopy(vatPhamId, 0, array, 18, 4);
			Buffer.BlockCopy(vatPhamSoLuong, 0, array, 26, 4);
			Buffer.BlockCopy(price, 0, array, 34, 8);
			Buffer.BlockCopy(vatPhamId, 0, array, 50, 4);
			Buffer.BlockCopy(vatPhamSoLuong, 0, array, 58, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array, 74, World.VatPham_ThuocTinh_KichThuoc);
			var array2 = new byte[World.Item_Db_Byte_Length];
			var num = (uint)BitConverter.ToInt32(vatPhamSoLuong, 0);
			if (num < 1 || !World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
			{
				return;
			}
			var fLdSide = value.FLD_SIDE;
			if (muaBan == 1)
			{
				if (fLdSide != 0)
				{
					var array3 = new byte[4];
					Buffer.BlockCopy(vatPhamThuocTinh, 0, array3, 0, 4);
					var characterItemType = GetCharacterBagItemByItemId(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(array3, 0));
					if (characterItemType != null)
					{
						position = characterItemType.VatPhamViTri;
						vatPhamSoLuong = BitConverter.GetBytes(num + BitConverter.ToInt32(characterItemType.VatPhamSoLuong, 0));
					}
				}
				else
				{
					num = 1u;
					vatPhamSoLuong = BitConverter.GetBytes(1);
				}
				Buffer.BlockCopy(itemGlobalId, 0, array, 42, 8);
				Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
				Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
				Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, 20);
				Buffer.BlockCopy(vatPhamSoLuong, 0, array2, 12, 4);
				Buffer.BlockCopy(BitConverter.GetBytes((int)num), 0, array, 26, 4);
				Buffer.BlockCopy(vatPhamSoLuong, 0, array, 57, 4);
				Item_In_Bag[position].VatPham_byte = array2;
				if (khoaLai > 0)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array2, 72, 1);
					Buffer.BlockCopy(BitConverter.GetBytes(BitConverter.ToInt32(vatPhamId, 0) + 20000), 0, array, 23, 4);
				}
				if (ngaySuDung > 0)
				{
					DateTime value2 = new(1970, 1, 1, 8, 0, 0);
					Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.Subtract(value2).TotalSeconds), 0, array2, 52, 4);
					Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.AddDays(ngaySuDung).Subtract(value2).TotalSeconds), 0, array2, 56, 4);
				}
			}
			else
			{
				var xVatPhamLoai = Item_In_Bag[position];
				if (BitConverter.ToInt32(xVatPhamLoai.VatPham_ID, 0) == 0 || BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) == 0 || BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) < num || num < 1)
				{
					return;
				}
				if (fLdSide != 0)
				{
					var num2 = BitConverter.ToInt32(xVatPhamLoai.VatPhamSoLuong, 0) - (int)num;
					if (num2 <= 0)
					{
						Item_In_Bag[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						Buffer.BlockCopy(BitConverter.GetBytes((int)num), 0, array, 26, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(num2), 0, array, 58, 4);
					}
					else
					{
						Buffer.BlockCopy(xVatPhamLoai.ItemGlobal_ID, 0, array2, 0, 8);
						Buffer.BlockCopy(xVatPhamLoai.VatPham_ID, 0, array2, 8, 4);
						Buffer.BlockCopy(xVatPhamLoai.VatPham_ThuocTinh, 0, array2, 16, World.VatPham_ThuocTinh_KichThuoc);
						Buffer.BlockCopy(BitConverter.GetBytes(num2), 0, array2, 12, 4);
						Buffer.BlockCopy(xVatPhamLoai.ItemGlobal_ID, 0, array, 42, 8);
						Buffer.BlockCopy(BitConverter.GetBytes((int)num), 0, array, 26, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(num2), 0, array, 58, 4);
						Item_In_Bag[position].VatPham_byte = array2;
					}
				}
				else
				{
					Item_In_Bag[position].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
			}
			Buffer.BlockCopy(BitConverter.GetBytes(position), 0, array, 68, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "MuaBanVatPham error [" + AccountID + "][" + CharacterName + "]  " + ex.Message);
		}
		Init_Item_In_Bag();
	}

	public void BUY_ITEM_FAIL()
	{
		try
		{
			var text = "AA55120018018C020C0088020000000000004528000055AA";
			var array = Converter.HexStringToByte(text);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "BUyItemfail error");
		}
	}

	public void BUY_ITEM_SUCCESS()
	{
		try
		{
			var text = "AA5523002D0188021500000000000000000000000000000000000000000000000000000000000055AA";
			var array = Converter.HexStringToByte(text);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "BuyItemSuccess error");
		}
	}

	public void BUY_ITEM_REJECT()
	{
		try
		{
			var text = "AA55E4002D019D02D6000*********0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
			var array = Converter.HexStringToByte(text);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "BUY_ITEM_REJECT ");
		}
	}


	public void PAYMENT_SHOPPING_CART(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var dst = new byte[4];
			Buffer.BlockCopy(packetData, 10, dst, 0, 1);
			BUY_ITEM_REJECT();
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "PAYMENT_SHOPPING_CART");
		}
	}


	public void Update_Show_FLD_RXPIONT_and_FLD_RXPIONTX()
	{
		var text = "AA5520002C018D02100001000000E00*********00004E000000000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_RXPIONT), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_RXPIONTX), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(12345), 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}


	public async void KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop()
	{
		try
		{
			var account = await AccountDb.FindAccount(AccountID);
			FLD_RXPIONT = account.fld_rxpiont ?? 0;
			FLD_RXPIONTX = account.fld_rxpiontx ?? 0;
			FLD_Coin = account.fld_coin ?? 0;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop errro" + ex.StackTrace);
		}
		//var dBToDataTable = DBA.GetDBToDataTable($"select  FLD_SEX,FLD_RXPIONT,FLD_RXPIONTX,FLD_VIP,FLD_VIPTIM,FLD_COIN  from  [TBL_ACCOUNT]  where  FLD_ID=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, AccountID) }, "rxjhaccount");

		//if (FLD_RXPIONT <= 0)
		//{
		//	FLD_RXPIONT = 0;
		//}
		//if (FLD_Coin < 0)
		//{
		//	FLD_Coin = 0;
		//}
		//if (World.KiemTra_NguyenBao == 1 && FLD_RXPIONT >= World.GioiHan_TongSoNguyenBao_1TaiKhoan)
		//{
		//	LogHelper.WriteLine(LogLevel.Error, "Người chơi [" + Userid + "]-[" + UserName + "] có CASH vượt ngưỡng [FLD_RXPIONT Tổng：" + FLD_RXPIONT + "] - [ Max là： " + World.GioiHan_TongSoNguyenBao_1TaiKhoan + "]");
		//	switch (World.HoatDong_PhatHienPhoi)
		//	{
		//	case 2:
		//		Title(91, Userid, "Cash Max");
		//		break;
		//	case 1:
		//		FLD_RXPIONT = 0;
		//		Save_NguyenBaoData();
		//		break;
		//	}
		//}
	}

	public void CloseTheStore(int storeId)
	{
		var array = new byte[2];
		var array2 = new byte[2];
		Buffer.BlockCopy(BitConverter.GetBytes(storeId), 0, array, 0, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array2, 0, 2);
		var array3 = Converter.HexStringToByte("AA551E0000009100100001000000010000000*********000000000000000000000055AA");
		OpenWarehouse = false;
		HeThong_HopThanh_MoKhoa();
		Buffer.BlockCopy(array, 0, array3, 18, 2);
		Buffer.BlockCopy(array2, 0, array3, 10, 2);
		Buffer.BlockCopy(array2, 0, array3, 14, 2);
		CurrentlyOperatingNPC = 0;
		if (storeId == 1 || storeId == 14 || storeId == 18 || storeId == 41 || storeId == 93 || storeId == 11 || storeId == 23 || storeId == 43 || storeId == 108 || storeId == 37 || storeId == 86)
		{
			SavePersonalWarehouseAsync().GetAwaiter().GetResult();
			SaveComprehensiveWarehouseAsync().GetAwaiter().GetResult();
		}
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
		Client?.SendMultiplePackage(array3, array3.Length);
	}


	public void 发送副本复活剩余次数()
	{
		var array = Converter.HexStringToByte("AA553200750543012C0000000000000000003DD09A3B00000000720002000A000000000000000100000098EEA67D000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(副本复活剩余次数), 0, array, 30, 4);
		Client?.Send(array, array.Length);
	}
	public int FindItemPositionByGlobalID(long toInt64)
	{
		for (var i = 0; i < Item_In_Bag.Length; i++)
		{
			if (Item_In_Bag[i].GetItemGlobal_ID == toInt64)
			{
				return i;
			}
		}
		return -1;
	}
	public void KiemSoatGold_SoLuong(long gold, int type)
	{
		if (gold <= 0)
			return;
		if (type == 0)
		{
			Player_Money -= gold;
		}
		else
		{
			Player_Money += gold;
		}
		UpdateMoneyAndWeight();
	}
	public int times;
	public void HeartbeatDetection(byte[] PacketData, int PacketSize)
	{
		try
		{
			var timeCheck = (int)DateTime.Now.Subtract(XTtime).TotalMilliseconds;
			// LogHelper.WriteLine(LogLevel.Error, $"NhipTim {timeCheck}");
			if (timeCheck < 9500)
			{
				times++;
				if (times > NguongSo_NhipTim && this.Client.ToString() != "127.0.0.1")
				{
					HeThongNhacNho("Trò chơi vận hành tốc độ dị thường, vui lòng đăng nhập lại.", 6, "Thông báo");
					Client.Dispose();
					LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 09]" + timeCheck);
				}
			}
			XTtime = DateTime.Now;
		}
		catch
		{
		}
	}

}
