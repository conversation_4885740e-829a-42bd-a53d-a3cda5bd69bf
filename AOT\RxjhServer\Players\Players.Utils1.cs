﻿using HeroYulgang.Constants;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.AOI;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

namespace RxjhServer;

public partial class Players
{
   
	private void HopThanh_KyNgocThach(byte[] packetData, int packetSize)
	{
		var num = BitConverter.ToInt32(packetData, 14);
		var num2 = BitConverter.ToInt32(packetData, 22);
		switch (num)
		{
			case 1000000773:
				{
					var num9 = 0;
					for (var l = 0; l < 96; l++)
					{
						if (BitConverter.ToInt32(Item_In_Bag[l].VatPham_ID, 0) == 1000000772 && BitConverter.ToInt32(Item_In_Bag[l].VatPhamSoLuong, 0) >= 2 * num2)
						{
							num9 = 1;
							break;
						}
					}
					if (num9 == 0)
					{
						PropCombinationTips(3, num, num2);
						break;
					}
					var parcelVacancy3 = GetParcelVacancy(this);
					if (parcelVacancy3 == -1)
					{
						PropCombinationTips(4, num, num2);
						break;
					}
					for (var m = 0; m < 96; m++)
					{
						if (BitConverter.ToInt32(Item_In_Bag[m].VatPham_ID, 0) == 1000000772)
						{
							SubtractItem(m, 2 * num2);
						}
					}
					IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(num), parcelVacancy3, BitConverter.GetBytes(num2), new byte[56]);
					PropCombinationTips(1, num, num2);
					break;
				}
			case 1000000772:
				{
					var num8 = 0;
					var packagedItems = FindItemByItemID(1000000771);
					if (packagedItems != null && BitConverter.ToInt32(packagedItems.VatPhamSoLuong, 0) >= 3 * num2)
					{
						num8 = 1;
					}
					if (num8 == 0)
					{
						PropCombinationTips(3, num, num2);
						break;
					}
					var parcelVacancy2 = GetParcelVacancy(this);
					if (parcelVacancy2 == -1)
					{
						PropCombinationTips(4, num, num2);
						break;
					}
					SubtractItem(packagedItems.VatPhamViTri, 3 * num2);
					IncreaseItem2(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(num), parcelVacancy2, BitConverter.GetBytes(num2), new byte[56]);
					PropCombinationTips(1, num, num2);
					break;
				}
			case ItemDef.Item.TrungCapKyNgocThach:
				{
					var num3 = 0;
					var num4 = 0;
					for (var i = 0; i < 96; i++)
					{
						if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == ItemDef.Item.SoCapKyNgocThach)
						{
							num3 += BitConverter.ToInt32(Item_In_Bag[i].VatPhamSoLuong, 0);
						}
						else if (BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0) == 1000000771 && BitConverter.ToInt32(Item_In_Bag[i].VatPhamSoLuong, 0) >= 10 * num2)
						{
							num4 = 1;
						}
					}
					if (num3 >= 5 * num2 && num4 != 0)
					{
						if (GetParcelVacancyNumber() < num2)
						{
							PropCombinationTips(4, num, num2);
							break;
						}
						var num5 = 5 * num2;
						for (var j = 0; j < 96; j++)
						{
							if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == ItemDef.Item.SoCapKyNgocThach)
							{
								if (num5 > 0)
								{
									SubtractItem(j, 1);
									num5--;
								}
							}
							else if (BitConverter.ToInt32(Item_In_Bag[j].VatPham_ID, 0) == 1000000771)
							{
								SubtractItem(j, 10 * num2);
							}
						}
						for (var k = 0; k < num2; k++)
						{
							var parcelVacancy = GetParcelVacancy(this);
							var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
							var array = new byte[56];
							var num6 = RNG.Next(1, 100);
							var num7 = 0;
							num7 = ((num6 > 25) ? ((num6 > 25 && num6 <= 30) ? RNG.Next(27, 27) : ((num6 > 30 && num6 <= 45) ? RNG.Next(28, 35) : ((num6 > 45 && num6 <= 50) ? RNG.Next(36, 36) : ((num6 > 50 && num6 <= 65) ? RNG.Next(37, 40) : ((num6 > 65 && num6 <= 70) ? RNG.Next(41, 41) : ((num6 <= 70 || num6 > 90) ? RNG.Next(51, 51) : RNG.Next(42, 50))))))) : RNG.Next(23, 26));
							Buffer.BlockCopy(BitConverter.GetBytes(num7), 0, array, 0, 4);
							IncreaseItem2(bytes, BitConverter.GetBytes(num), parcelVacancy, BitConverter.GetBytes(1), array);
							Init_Item_In_Bag();
						}
						PropCombinationTips(1, num, num2);
					}
					else
					{
						PropCombinationTips(3, num, num2);
					}
					break;
				}
		}
	}

	private void PropCombinationTips(int nhacNhoId, int vatPhamId, int vatPhamSoLuong)
	{
		SendingClass sendingClass = new();
		sendingClass.Write2(11);
		sendingClass.Write2(nhacNhoId);
		sendingClass.Write8(vatPhamId);
		sendingClass.Write4(vatPhamSoLuong);
		Client?.SendPak(sendingClass, 793, SessionID);
	}


	public void Keo_Bua_Bao_OanTuTi(byte[] packetData, int packetSize)
	{
		try
		{
			int num = BitConverter.ToInt16(packetData, 10);
			int num2 = BitConverter.ToInt16(packetData, 14);
			switch (num)
			{
				case 1:
					{
						Random random = new(DateTime.Now.Millisecond);
						var id2 = random.Next(10001, 10101);
						if (num2 != 1)
						{
							RockScissorsClothTips(num, 1, id2, 0);
						}
						if (num2 == 1)
						{
							KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							if (FLD_RXPIONT >= World.XemBoiToan_PhiTieuHao)
							{
								KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
								KiemSoatNguyenBao_SoLuong(World.XemBoiToan_PhiTieuHao, 0);
								Save_NguyenBaoData();
								HeThongNhacNho("Xem bói thành công, nhận bí pháp gia trì, tiêu hao [" + World.XemBoiToan_PhiTieuHao + "] điểm!");
							}
							else
							{
								HeThongNhacNho("Không đủ " + World.XemBoiToan_PhiTieuHao + " điểm, đại hiệp không thể xem bói!");
							}
						}
						break;
					}
				case 2:
					{
						var id = new Random(DateTime.Now.Millisecond).Next(1, 101);
						RockScissorsClothTips(num, 1, id, 0);
						break;
					}
				case 3:
					{
						int num3 = BitConverter.ToInt16(packetData, 14);
						if (num3 != 0)
						{
							var array = new int[3, 3]
							{
						{ 0, 2, 1 },
						{ 1, 0, 2 },
						{ 2, 1, 0 }
							};
							var array2 = new int[3] { 0, 1, 2 };
							var num4 = new Random(DateTime.Now.Millisecond).Next(0, 3);
							var num5 = array2[array[num3 - 1, num4]];
							Console.WriteLine(Cc(num3 - 1) + ":" + Cc(num4) + "  " + Bb(num5));
							var d = 0;
							switch (num5)
							{
								case 2:
									SoLanThangCuocOanTuTi = 0;
									d = 200;
									break;
								case 1:
									SoLanThangCuocOanTuTi++;
									d = SoLanThangCuocOanTuTi;
									break;
							}
							RockScissorsClothTips(num, 1, num4 + 1, d);
						}
						break;
					}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Kéo, giấy, đá error: " + AccountID + "|" + CharacterName + "  " + ex.Message);
		}
	}

	public void QuaySoMayMan_CuongHoaThach_SieuCap()
	{
		try
		{
			var parcelVacancy = GetParcelVacancy(this);
			if (parcelVacancy != -1)
			{
				var num = RNG.Next(1, 100);
				var value = ItemDef.Item.CuongHoaThachSieuCap;
				if (num < 40)
				{
					Random random = new(DateTime.Now.Millisecond);
					var num2 = random.Next(5, 8);
					switch (num2)
					{
						case 5:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(5), new byte[56]);
							break;
						case 6:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(6), new byte[56]);
							break;
						case 7:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(7), new byte[56]);
							break;
						case 8:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(8), new byte[56]);
							break;
					}
					HeThongNhacNho("Nhận được [" + num2 + "] viên Cường hóa thạch siêu cấp, bảo vật hiếm có!", 7, "Thiên cơ các");
				}
				else if (num >= 40 && num <= 85)
				{
					Random random2 = new(DateTime.Now.Millisecond);
					var num3 = random2.Next(9, 11);
					switch (num3)
					{
						case 9:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(9), new byte[56]);
							break;
						case 10:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(11), new byte[56]);
							break;
						case 11:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(11), new byte[56]);
							break;
					}
					HeThongNhacNho("Nhận được [" + num3 + "] viên Cường hóa thạch siêu cấp, bảo vật hiếm có!", 7, "Thiên cơ các");
				}
				else
				{
					Random random3 = new(DateTime.Now.Millisecond);
					var num4 = random3.Next(12, 15);
					switch (num4)
					{
						case 12:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(13), new byte[56]);
							break;
						case 13:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(13), new byte[56]);
							break;
						case 14:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(14), new byte[56]);
							break;
						case 15:
							AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value), parcelVacancy, BitConverter.GetBytes(15), new byte[56]);
							break;
					}
					HeThongNhacNho("Nhận được [" + num4 + "] viên Cường hóa thạch siêu cấp, bảo vật hiếm có!", 7, "Thiên cơ các");
				}
			}
			else
			{
				HeThongNhacNho("Đại hiệp cần dọn trống một ô trong hành trang để chứa bảo vật!!");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Quay Số Lỗi CHT SC error: " + AccountID + "|" + CharacterName + "  " + ex.Message);
		}
	}

	public void QuaySoMayMan()
	{
		try
		{
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Quay Số Lỗi error: " + AccountID + "|" + CharacterName + "  " + ex.Message);
		}
	}

	public void RockScissorsClothTips(int playerId, int trangThaiId, int id, int d2)
	{
		var array = Converter.HexStringToByte("AA550E00EC0274170800010001007427000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(playerId), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(trangThaiId), 0, array, 12, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(id), 0, array, 14, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(d2), 0, array, 16, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

	public bool ApplicationConditionsForTestingCeremony(int banDo, int veVaoCua)
	{
		if (banDo == 0)
		{
			CoupleTips(40, CharacterName, FLD_Couple);
			HeThongNhacNho("Chỉ phu quân chính thức mới được ghi danh tổ chức hôn lễ!");
			return false;
		}
		if (FLD_Couple.Length == 0)
		{
			CoupleTips(16, "", "");
			return false;
		}
		if (FLD_Couple.Length == 0)
		{
			CoupleTips(16, "", "");
			return false;
		}
		if (GiaiTruQuanHe_Countdown != 0)
		{
			CoupleTips(53, CharacterName, FLD_Couple);
			return false;
		}
		if (WhetherMarried == 1)
		{
			CoupleTips(16, CharacterName, FLD_Couple);
			return false;
		}
		var characterData = GetCharacterData(FLD_Couple);
		if (characterData == null)
		{
			CoupleTips(18, CharacterName, FLD_Couple);
			return false;
		}
		if (characterData.MapID != banDo)
		{
			CoupleTips(64, CharacterName, FLD_Couple);
			return false;
		}
		if (veVaoCua == 1000000333)
		{
			if (BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 16900090)
			{
				CoupleTips(30, CharacterName, FLD_Couple);
				CoupleTips(0, CharacterName, FLD_Couple);
				return false;
			}
			if (BitConverter.ToInt32(characterData.Item_Wear[11].VatPham_ID, 0) != 26900078)
			{
				characterData.CoupleTips(30, characterData.CharacterName, characterData.FLD_Couple);
				characterData.CoupleTips(0, characterData.CharacterName, characterData.FLD_Couple);
				return false;
			}
		}
		if (veVaoCua == 1000000334)
		{
			if (BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 16900298)
			{
				CoupleTips(30, CharacterName, FLD_Couple);
				CoupleTips(0, CharacterName, FLD_Couple);
				return false;
			}
			if (BitConverter.ToInt32(characterData.Item_Wear[11].VatPham_ID, 0) != 26900283)
			{
				characterData.CoupleTips(30, characterData.CharacterName, characterData.FLD_Couple);
				characterData.CoupleTips(0, characterData.CharacterName, characterData.FLD_Couple);
				return false;
			}
		}
		if (veVaoCua == 1000000335)
		{
			if (BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 16900300)
			{
				CoupleTips(30, CharacterName, FLD_Couple);
				CoupleTips(0, CharacterName, FLD_Couple);
				return false;
			}
			if (BitConverter.ToInt32(characterData.Item_Wear[11].VatPham_ID, 0) != 26900284)
			{
				characterData.CoupleTips(30, characterData.CharacterName, characterData.FLD_Couple);
				characterData.CoupleTips(0, characterData.CharacterName, characterData.FLD_Couple);
				return false;
			}
		}
		if (GetParcelVacancyNumber() < 3)
		{
			CoupleTips(50, characterData.CharacterName, characterData.FLD_Couple);
			return false;
		}
		if (characterData.GetParcelVacancyNumber() < 3)
		{
			HeThongNhacNho("Cô nương chưa đứng đúng vị trí nghi thức!");
			characterData.CoupleTips(50, characterData.CharacterName, characterData.FLD_Couple);
			return false;
		}
		if (FindItemByItemID(1000000416) == null)
		{
			CoupleTips(45, CharacterName, FLD_Couple);
			return false;
		}
		if (characterData.FindItemByItemID(1000000416) != null)
		{
			return true;
		}
		HeThongNhacNho("Phu quân cần mang Nhẫn Đôi để tiến hành hôn lễ!");
		characterData.CoupleTips(45, characterData.CharacterName, characterData.FLD_Couple);
		return false;
	}

	public bool CheckTheEntryConditionsOfTheWeddingBanquetHall(int banDo, int veVaoCua)
	{
		if (Player_Sex == 2)
		{
			CoupleTips(40, "", "");
			HeThongNhacNho("Hợp đồng thuê sảnh cưới do nhân viên giang hồ thực hiện!");
			return false;
		}
		if (FLD_Couple.Length == 0)
		{
			CoupleTips(16, "", "");
			return false;
		}
		if (GiaiTruQuanHe_Countdown != 0)
		{
			CoupleTips(53, CharacterName, FLD_Couple);
			return false;
		}
		if (WhetherMarried == 1)
		{
			CoupleTips(16, CharacterName, FLD_Couple);
			return false;
		}
		var characterData = GetCharacterData(FLD_Couple);
		if (characterData == null)
		{
			CoupleTips(18, CharacterName, FLD_Couple);
			return false;
		}
		if (FindItemByItemID(1000000416) == null)
		{
			CoupleTips(45, CharacterName, FLD_Couple);
			return false;
		}
		if (characterData.FindItemByItemID(1000000416) == null)
		{
			CoupleTips(45, CharacterName, FLD_Couple);
			characterData.CoupleTips(45, CharacterName, characterData.FLD_Couple);
			return false;
		}
		if (FindItemByItemID(veVaoCua) == null)
		{
			CoupleTips(52, CharacterName, FLD_Couple);
			return false;
		}
		if (FLD_loveDegreeLevel > 4)
		{
			CoupleTips(72, CharacterName, FLD_Couple);
			return false;
		}
		if (veVaoCua == 1000000333 && World.IsTheDragonHallInUse)
		{
			CoupleTips(49, CharacterName, FLD_Couple);
			return false;
		}
		if (veVaoCua == 1000000334 && World.IsHuaMarriageHallInUse)
		{
			CoupleTips(49, CharacterName, FLD_Couple);
			return false;
		}
		if (veVaoCua == 1000000335 && World.WhetherTheSacramentalHallIsInUse)
		{
			CoupleTips(49, CharacterName, FLD_Couple);
			return false;
		}
		if (veVaoCua == 1000000333)
		{
			if (BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 16900090)
			{
				CoupleTips(48, CharacterName, FLD_Couple);
				CoupleTips(0, CharacterName, FLD_Couple);
				return false;
			}
			if (BitConverter.ToInt32(characterData.Item_Wear[11].VatPham_ID, 0) != 26900078)
			{
				characterData.CoupleTips(48, characterData.CharacterName, characterData.FLD_Couple);
				characterData.CoupleTips(0, characterData.CharacterName, characterData.FLD_Couple);
				return false;
			}
		}
		if (veVaoCua == 1000000334)
		{
			if (BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 16900298)
			{
				CoupleTips(48, CharacterName, FLD_Couple);
				CoupleTips(0, CharacterName, FLD_Couple);
				return false;
			}
			if (BitConverter.ToInt32(characterData.Item_Wear[11].VatPham_ID, 0) != 26900283)
			{
				characterData.CoupleTips(48, characterData.CharacterName, characterData.FLD_Couple);
				characterData.CoupleTips(0, characterData.CharacterName, characterData.FLD_Couple);
				return false;
			}
		}
		if (veVaoCua == 1000000335)
		{
			if (BitConverter.ToInt32(Item_Wear[11].VatPham_ID, 0) != 16900300)
			{
				CoupleTips(48, CharacterName, FLD_Couple);
				CoupleTips(0, CharacterName, FLD_Couple);
				return false;
			}
			if (BitConverter.ToInt32(characterData.Item_Wear[11].VatPham_ID, 0) != 26900284)
			{
				characterData.CoupleTips(48, characterData.CharacterName, characterData.FLD_Couple);
				characterData.CoupleTips(0, characterData.CharacterName, characterData.FLD_Couple);
				HeThongNhacNho("Phu quân [" + characterData.CharacterName + "] cần khoác áo lễ chú rể!");
				return false;
			}
		}
		return true;
	}

	public void PrepTimeEndEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)_preparationTime.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			if (WeddingMap == 9101)
			{
				if (!World.IsTheDragonHallInUse)
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						var num = RNG.Next(1, 30);
						if (value.MapID == WeddingMap)
						{
							var num2 = RNG.Next(1, 30);
							value.Mobile(num, num2, 15f, 101, 0);
						}
					}
				}
			}
			else if (WeddingMap == 9001)
			{
				if (!World.IsHuaMarriageHallInUse)
				{
					foreach (var value2 in World.allConnectedChars.Values)
					{
						var num3 = RNG.Next(1, 30);
						if (value2.MapID == WeddingMap)
						{
							var num4 = RNG.Next(1, 30);
							value2.Mobile(num3, num4, 15f, 101, 0);
						}
					}
				}
			}
			else if (WeddingMap == 9201 && !World.WhetherTheSacramentalHallIsInUse)
			{
				foreach (var value3 in World.allConnectedChars.Values)
				{
					var num5 = RNG.Next(1, 30);
					if (value3.MapID == WeddingMap)
					{
						var num6 = RNG.Next(1, 30);
						value3.Mobile(num5, num6, 15f, 101, 0);
					}
				}
			}
			if (PreliminaryApplicationCeremonyTimer != null)
			{
				PreliminaryApplicationCeremonyTimer.Enabled = false;
				PreliminaryApplicationCeremonyTimer.Close();
				PreliminaryApplicationCeremonyTimer.Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi nhận trình phát phạm vi đánh giá ! Người dùng: [" + AccountID + "]-[" + CharacterName + "]" + ex);
		}
	}

	public void BoiCanh_TrangTri(byte[] packetData, int packetSize)
	{
		try
		{
			var array = new byte[4];
			var array2 = new byte[4];
			var array3 = new byte[4];
			var dst = new byte[4];
			var array4 = new byte[4];
			var array5 = new byte[4];
			Buffer.BlockCopy(packetData, 10, array, 0, 4);
			Buffer.BlockCopy(packetData, 14, array3, 0, 4);
			Buffer.BlockCopy(packetData, 18, dst, 0, 4);
			Buffer.BlockCopy(packetData, 26, array2, 0, 4);
			Buffer.BlockCopy(packetData, 30, array4, 0, 4);
			Buffer.BlockCopy(packetData, 34, array5, 0, 4);
			if (BitConverter.ToInt32(array, 0) == 0)
			{
				if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array3, 0)].VatPham_ID, 0) == ********** || BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array3, 0)].VatPham_ID, 0) == **********)
				{
					Buffer.BlockCopy(array, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0), 4);
					Buffer.BlockCopy(array2, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 4, 4);
					Buffer.BlockCopy(array4, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 8, 4);
					Buffer.BlockCopy(array5, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 12, 4);
					var array6 = Converter.HexStringToByte("AA5520006000721710000000000003000000CCCCFFFFFF5000FF0000000000000000000055AA");
					Buffer.BlockCopy(array, 0, array6, 10, 4);
					Buffer.BlockCopy(array2, 0, array6, 14, 4);
					Buffer.BlockCopy(array4, 0, array6, 18, 4);
					Buffer.BlockCopy(array5, 0, array6, 22, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array6, 4, 2);
					Client?.SendMultiplePackage(array6, array6.Length);
					Send_Nhieu_Packet_PhamVi_HienTai(array6, array6.Length);
					SubtractItem(BitConverter.ToInt32(array3, 0), 1);
				}
			}
			else if (BitConverter.ToInt32(array, 0) == 1)
			{
				if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array3, 0)].VatPham_ID, 0) == 1008000191 || BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array3, 0)].VatPham_ID, 0) == 1008000201)
				{
					Buffer.BlockCopy(array, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0), 4);
					Buffer.BlockCopy(array2, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 4, 4);
					Buffer.BlockCopy(array4, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 8, 4);
					Buffer.BlockCopy(array5, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 12, 4);
					var array7 = Converter.HexStringToByte("AA5520006000721710000000000003000000CCCCFFFFFF5000FF0000000000000000000055AA");
					Buffer.BlockCopy(array, 0, array7, 10, 4);
					Buffer.BlockCopy(array2, 0, array7, 14, 4);
					Buffer.BlockCopy(array4, 0, array7, 18, 4);
					Buffer.BlockCopy(array5, 0, array7, 22, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array7, 4, 2);
					Client?.SendMultiplePackage(array7, array7.Length);
					Send_Nhieu_Packet_PhamVi_HienTai(array7, array7.Length);
					SubtractItem(BitConverter.ToInt32(array3, 0), 1);
				}
			}
			else if (BitConverter.ToInt32(array, 0) != 2 || BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array3, 0)].VatPham_ID, 0) == 1008000192 || BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array3, 0)].VatPham_ID, 0) == 1008000202)
			{
				Buffer.BlockCopy(array, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0), 4);
				Buffer.BlockCopy(array2, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 4, 4);
				Buffer.BlockCopy(array4, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 8, 4);
				Buffer.BlockCopy(array5, 0, CharacterNameTemplate, 16 * BitConverter.ToInt32(array, 0) + 12, 4);
				var array8 = Converter.HexStringToByte("AA5520006000721710000000000003000000CCCCFFFFFF5000FF0000000000000000000055AA");
				Buffer.BlockCopy(array, 0, array8, 10, 4);
				Buffer.BlockCopy(array2, 0, array8, 14, 4);
				Buffer.BlockCopy(array4, 0, array8, 18, 4);
				Buffer.BlockCopy(array5, 0, array8, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array8, 4, 2);
				Client?.SendMultiplePackage(array8, array8.Length);
				Send_Nhieu_Packet_PhamVi_HienTai(array8, array8.Length);
				SubtractItem(BitConverter.ToInt32(array3, 0), 1);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Danh Tu Kieu Dang! error: [" + AccountID + "]-[" + Client.ToString() + "]" + ex.Message);
		}
	}

	public void PacketModification(byte[] data, int length)
	{
		var num = 0;
		try
		{
			if (SessionID == 0)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 11]");
			}
			else
			{
				var array = new byte[4];
				Buffer.BlockCopy(data, 4, array, 0, 2);
				var num2 = BitConverter.ToInt32(array, 0);
				if (num2 != SessionID && num2 != CharacterBeastFullServiceID && World.PacketTitle == 1)
				{
					BanAccount(69, AccountID, "Phi pháp sửa chữa PacketTitle");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Niêm phong package bị lỗi tại Num: [" + num + "]-[" + AccountID + "][" + CharacterName + "][" + Client.ToString() + "] - " + ex.Message);
		}
	}

	public void UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory()
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(60);
		sendingClass.Write4(0);
		for (var i = 0; i < 16; i++)
		{
			if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[i].VatPhamSoLuong, 0) == 0)
			{
				CharacterBeast.ThuCung_Thanh_TrangBi[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
			}
			else
			{
				KiemTraVatPhamHeThong("SpiritBeastEquipmentBarPackage", ref CharacterBeast.ThuCung_Thanh_TrangBi[i]);
			}
			sendingClass.Write(CharacterBeast.ThuCung_Thanh_TrangBi[i].GetByte(), 0, World.Item_Byte_Length_92);
		}

		Client?.SendPak(sendingClass, 28928, CharacterBeastFullServiceID);
	}

	public void CapNhat_ChiSo_TrangSuc(Item vatPham)
	{
		if (World.ItemList.TryGetValue((int)vatPham.GetVatPham_ID, out var value) && vatPham.FLD_FJ_TienHoa == 0)
		{
			if (vatPham.FLD_MAGIC1 != value.FLD_MAGIC1)
			{
				vatPham.FLD_MAGIC1 = value.FLD_MAGIC1;
				HeThongNhacNho("Cập nhật chỉ số trang sức thành công (dòng 1)!!!", 23, "");
			}
			if (vatPham.FLD_MAGIC2 != value.FLD_MAGIC2)
			{
				vatPham.FLD_MAGIC2 = value.FLD_MAGIC2;
				HeThongNhacNho("Cập nhật chỉ số trang sức thành công (dòng 2)!!!", 23, "");
			}
			if (vatPham.FLD_MAGIC3 != value.FLD_MAGIC3)
			{
				vatPham.FLD_MAGIC3 = value.FLD_MAGIC3;
				HeThongNhacNho("Cập nhật chỉ số trang sức thành công (dòng 3)!!!", 23, "");
			}
			if (vatPham.FLD_MAGIC4 != value.FLD_MAGIC4)
			{
				vatPham.FLD_MAGIC4 = value.FLD_MAGIC4;
				HeThongNhacNho("Cập nhật chỉ số trang sức thành công (dòng 4)!!!", 23, "");
			}
		}
	}

	public void ControlOptionsItem(Item vatPham)
	{
		if (World.ItemList.TryGetValue((int)vatPham.GetVatPham_ID, out var value) && (value.FLD_RESIDE2 == 7 || value.FLD_RESIDE2 == 8 || value.FLD_RESIDE2 == 10))
		{
			CapNhat_ChiSo_TrangSuc(vatPham);
		}
	}

	public void UpdateNTC_Bag()
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write(171);
			for (var i = 0; i < 6; i++)
			{
				if (BitConverter.ToInt32(Item_NTC[i].VatPhamSoLuong, 0) == 0)
				{
					Item_NTC[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				else
				{
					KiemTraVatPhamHeThong("UpdateNTCBag", ref Item_NTC[i]);
				}
				sendingClass.Write(Item_NTC[i].GetByte(), 0, World.Item_Byte_Length_92);
			}

			Client?.SendPak(sendingClass, 28928, SessionID);
			var num = 0;
			for (var j = 0; j < 6; j++)
			{
				if (BitConverter.ToInt32(Item_NTC[j].VatPham_ID, 0) != 0)
				{
					if (num == 3)
					{
						break;
					}
					if (Item_NTC[j].FLD_MAGIC1 != Item_NTC[j].FLD_MAGIC0 || Item_NTC[j].FLD_MAGIC0 == 0)
					{
						num++;
						NTC_ThaoTac_Bag(1, j, BitConverter.ToInt32(Item_NTC[j].VatPham_ID, 0), Item_NTC[j].FLD_MAGIC1, Item_NTC[j].FLD_MAGIC0);
					}
				}
			}
			UpdateMartialArtsAndStatus();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"UpdateNTC {ex.StackTrace}");
		}
	}

	public void NTC_ThaoTac_Bag(int type, int vitri, int vatPhamId, int magic1, int magic0)
	{
		SendingClass sendingClass = new();
		sendingClass.Write4(type);
		sendingClass.Write4(vitri);
		sendingClass.Write4(vatPhamId);
		sendingClass.Write4(0);
		sendingClass.Write4(magic1);
		sendingClass.Write4(magic0);
		Client?.SendPak(sendingClass, 57368, SessionID);
	}

	public void DyeHair(byte[] packetData, int packetSize)
	{
		try
		{
			if (MapID == 801)
			{
				return;
			}
			PacketModification(packetData, packetSize);
			var array = new byte[4];
			var array2 = new byte[2];
			Buffer.BlockCopy(packetData, 10, array, 0, 1);
			Buffer.BlockCopy(packetData, 12, array2, 0, 2);
			if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) == 1008000056 || BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) == 1008000015)
			{
				var array3 = Converter.HexStringToByte("AA5527000A00111018000100030041440300000000000FDC143C000000000000D5EF00000000000000000055AA");
				var array4 = Converter.HexStringToByte("AA5512000000121004000000D5EF000000000000000055AA");
				Buffer.BlockCopy(array, 0, array3, 12, 1);
				Buffer.BlockCopy(array2, 0, array3, 32, 2);
				Buffer.BlockCopy(array2, 0, array4, 12, 2);
				Buffer.BlockCopy(Item_In_Bag[BitConverter.ToInt32(array, 0)].ItemGlobal_ID, 0, array3, 14, 8);
				Buffer.BlockCopy(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0, array3, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
				Client?.Send_Map_Data(array3, array3.Length);
				Client?.Send_Map_Data(array4, array4.Length);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 10, 2);
				SendCurrentRangeBroadcastData(array4, array4.Length);
				NewCharacterTemplate.MauToc = BitConverter.ToInt16(array2, 0);
				SubtractItem(BitConverter.ToInt32(array, 0), 1);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Nhuộm tóc error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void HairDressing(byte[] packetData, int packetSize)
	{
		try
		{
			PacketModification(packetData, packetSize);
			var array = new byte[4];
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 10, array, 0, 1);
			Buffer.BlockCopy(packetData, 14, array2, 0, 2);
			if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0) == **********)
			{
				var array3 = Converter.HexStringToByte("AA5527002C01081618000100080041440300000000000FDC143C0000000000009B0000000000000000000055AA");
				var array4 = Converter.HexStringToByte("AA5512002A01091604002A019999000000000000000055AA");
				Buffer.BlockCopy(array, 0, array3, 12, 1);
				Buffer.BlockCopy(array2, 0, array3, 32, 1);
				Buffer.BlockCopy(Item_In_Bag[BitConverter.ToInt32(array, 0)].ItemGlobal_ID, 0, array3, 14, 8);
				Buffer.BlockCopy(Item_In_Bag[BitConverter.ToInt32(array, 0)].VatPham_ID, 0, array3, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array3, 4, 2);
				Buffer.BlockCopy(array2, 0, array4, 12, 1);
				Buffer.BlockCopy(array2, 0, array4, 13, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 4, 2);
				Client?.Send_Map_Data(array3, array3.Length);
				Client?.Send_Map_Data(array4, array4.Length);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array4, 10, 2);
				SendCurrentRangeBroadcastData(array4, array4.Length);
				NewCharacterTemplate.KieuToc = array2[0];
				SubtractItem(BitConverter.ToInt32(array, 0), 1);
				LoadCharacterWearItem();
				UpdateMartialArtsAndStatus();
				UpdateCharacterData(this);
				UpdateBroadcastCharacterData();
				UpdateEquipmentEffects();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Nhuộm tóc error 111: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}


	public void OpenSymbol(byte[] packetData, int packetSize)
	{
		try
		{
			if (PublicDrugs.Count >= 2)
			{
				return;
			}
			PacketModification(packetData, packetSize);
			int num = packetData[20];
			var array = new byte[4];
			Buffer.BlockCopy(packetData, 12, array, 0, 4);
			var itmeid = Converter.getItmeid(Converter.ToString(array));
			switch (itmeid)
			{
				case **********:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == ********** && !KiemTra_Phu())
					{
						DateTime value9 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan9 = DateTime.Now.AddDays(10.0).Subtract(value9);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai9 = new();
						xCongHuuDuocPhamLoai9.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai9.ThoiGian = (uint)timeSpan9.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai9.DuocPhamID, xCongHuuDuocPhamLoai9);
						SetPublicPills(xCongHuuDuocPhamLoai9);
						SubtractItem(num, 1);
					}
					break;
				case 1008000059:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000059 && !KiemTra_Phu())
					{
						DateTime value2 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan2 = DateTime.Now.AddDays(10.0).Subtract(value2);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai2 = new();
						xCongHuuDuocPhamLoai2.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai2.ThoiGian = (uint)timeSpan2.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai2.DuocPhamID, xCongHuuDuocPhamLoai2);
						SetPublicPills(xCongHuuDuocPhamLoai2);
						SubtractItem(num, 1);
					}
					break;
				case 1008000060:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000060 && !KiemTra_Phu())
					{
						DateTime value11 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan11 = DateTime.Now.AddDays(10.0).Subtract(value11);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai11 = new();
						xCongHuuDuocPhamLoai11.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai11.ThoiGian = (uint)timeSpan11.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai11.DuocPhamID, xCongHuuDuocPhamLoai11);
						SetPublicPills(xCongHuuDuocPhamLoai11);
						SubtractItem(num, 1);
					}
					break;
				case 1008000061:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000061 && !KiemTra_Phu())
					{
						DateTime value15 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan15 = DateTime.Now.AddDays(2.0).Subtract(value15);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai15 = new();
						xCongHuuDuocPhamLoai15.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai15.ThoiGian = (uint)timeSpan15.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai15.DuocPhamID, xCongHuuDuocPhamLoai15);
						SetPublicPills(xCongHuuDuocPhamLoai15);
						SubtractItem(num, 1);
						AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(800000005), num, BitConverter.GetBytes(1), new byte[56]);
					}
					break;
				case 1008000062:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000059 && !KiemTra_Phu())
					{
						DateTime value5 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan5 = DateTime.Now.AddDays(2.0).Subtract(value5);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai5 = new();
						xCongHuuDuocPhamLoai5.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai5.ThoiGian = (uint)timeSpan5.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai5.DuocPhamID, xCongHuuDuocPhamLoai5);
						SetPublicPills(xCongHuuDuocPhamLoai5);
						SubtractItem(num, 1);
					}
					break;
				case 1008000063:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000063 && !KiemTra_Phu())
					{
						DateTime value14 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan14 = DateTime.Now.AddDays(2.0).Subtract(value14);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai14 = new();
						xCongHuuDuocPhamLoai14.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai14.ThoiGian = (uint)timeSpan14.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai14.DuocPhamID, xCongHuuDuocPhamLoai14);
						SetPublicPills(xCongHuuDuocPhamLoai14);
						SubtractItem(num, 1);
					}
					break;
				case 1008000027:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000027 && !KiemTra_Phu())
					{
						DateTime value6 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan6 = DateTime.Now.AddDays(15.0).Subtract(value6);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai6 = new();
						xCongHuuDuocPhamLoai6.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai6.ThoiGian = (uint)timeSpan6.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai6.DuocPhamID, xCongHuuDuocPhamLoai6);
						SetPublicPills(xCongHuuDuocPhamLoai6);
						SubtractItem(num, 1);
					}
					break;
				case 1008000028:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000028 && !KiemTra_Phu())
					{
						DateTime value17 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan17 = DateTime.Now.AddDays(15.0).Subtract(value17);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai17 = new();
						xCongHuuDuocPhamLoai17.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai17.ThoiGian = (uint)timeSpan17.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai17.DuocPhamID, xCongHuuDuocPhamLoai17);
						SetPublicPills(xCongHuuDuocPhamLoai17);
						SubtractItem(num, 1);
						AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(800000005), num, BitConverter.GetBytes(1), new byte[56]);
					}
					break;
				case 1008000029:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000029 && !KiemTra_Phu())
					{
						DateTime value12 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan12 = DateTime.Now.AddDays(15.0).Subtract(value12);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai12 = new();
						xCongHuuDuocPhamLoai12.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai12.ThoiGian = (uint)timeSpan12.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai12.DuocPhamID, xCongHuuDuocPhamLoai12);
						SetPublicPills(xCongHuuDuocPhamLoai12);
						SubtractItem(num, 1);
						AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(800000005), num, BitConverter.GetBytes(1), new byte[56]);
					}
					break;
				case 1008000141:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000141 && !KiemTra_Phu2())
					{
						DateTime value8 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan8 = DateTime.Now.AddDays(10.0).Subtract(value8);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai8 = new();
						xCongHuuDuocPhamLoai8.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai8.ThoiGian = (uint)timeSpan8.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai8.DuocPhamID, xCongHuuDuocPhamLoai8);
						SetPublicPills(xCongHuuDuocPhamLoai8);
						SubtractItem(num, 1);
					}
					break;
				case 1008000140:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000140 && !KiemTra_Phu2())
					{
						DateTime value3 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan3 = DateTime.Now.AddDays(1.0).Subtract(value3);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai3 = new();
						xCongHuuDuocPhamLoai3.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai3.ThoiGian = (uint)timeSpan3.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai3.DuocPhamID, xCongHuuDuocPhamLoai3);
						SetPublicPills(xCongHuuDuocPhamLoai3);
						SubtractItem(num, 1);
					}
					break;
				case 1008000124:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000124 && !KiemTra_Phu2())
					{
						DateTime value16 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan16 = DateTime.Now.AddDays(15.0).Subtract(value16);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai16 = new();
						xCongHuuDuocPhamLoai16.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai16.ThoiGian = (uint)timeSpan16.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai16.DuocPhamID, xCongHuuDuocPhamLoai16);
						SetPublicPills(xCongHuuDuocPhamLoai16);
						SubtractItem(num, 1);
					}
					break;
				case 1008000318:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000318 && !KiemTra_Phu2())
					{
						DateTime value13 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan13 = DateTime.Now.AddDays(1.0).Subtract(value13);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai13 = new();
						xCongHuuDuocPhamLoai13.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai13.ThoiGian = (uint)timeSpan13.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai13.DuocPhamID, xCongHuuDuocPhamLoai13);
						SetPublicPills(xCongHuuDuocPhamLoai13);
						SubtractItem(num, 1);
					}
					break;
				case 1008000312:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000312 && !KiemTra_Phu2())
					{
						DateTime value10 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan10 = DateTime.Now.AddDays(30.0).Subtract(value10);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai10 = new();
						xCongHuuDuocPhamLoai10.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai10.ThoiGian = (uint)timeSpan10.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai10.DuocPhamID, xCongHuuDuocPhamLoai10);
						SetPublicPills(xCongHuuDuocPhamLoai10);
						SubtractItem(num, 1);
					}
					break;
				case 1008000311:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000311 && !KiemTra_Phu2())
					{
						DateTime value7 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan7 = DateTime.Now.AddDays(30.0).Subtract(value7);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai7 = new();
						xCongHuuDuocPhamLoai7.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai7.ThoiGian = (uint)timeSpan7.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai7.DuocPhamID, xCongHuuDuocPhamLoai7);
						SetPublicPills(xCongHuuDuocPhamLoai7);
						SubtractItem(num, 1);
					}
					break;
				case 1008001584:
					ItemUse(1, num, 1);
					SaveMemberData();
					break;
				case 1008000877:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000877 && !KiemTra_Phu2())
					{
						DateTime value4 = new(1970, 1, 1, 8, 0, 0);
						var timeSpan4 = DateTime.Now.AddDays(1.0).Subtract(value4);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai4 = new();
						xCongHuuDuocPhamLoai4.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai4.ThoiGian = (uint)timeSpan4.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai4.DuocPhamID, xCongHuuDuocPhamLoai4);
						SetPublicPills(xCongHuuDuocPhamLoai4);
						SubtractItem(num, 1);
					}
					break;
				case 1008000320:
					if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 1008000320 && !KiemTra_Phu2())
					{
						DateTime value = new(1970, 1, 1, 8, 0, 0);
						var timeSpan = DateTime.Now.AddDays(10.0).Subtract(value);
						X_Cong_Huu_Duoc_Pham_Loai xCongHuuDuocPhamLoai = new();
						xCongHuuDuocPhamLoai.DuocPhamID = itmeid;
						xCongHuuDuocPhamLoai.ThoiGian = (uint)timeSpan.TotalSeconds;
						PublicDrugs.Add(xCongHuuDuocPhamLoai.DuocPhamID, xCongHuuDuocPhamLoai);
						SetPublicPills(xCongHuuDuocPhamLoai);
						SubtractItem(num, 1);
					}
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Mở biểu tượng error: [" + AccountID + "]-[" + CharacterName + "]" + ex.Message);
		}
	}

	public void SetAdditionalStatusItems()
	{
		if (AdditionalStatusItemsNew == null || AdditionalStatusItemsNew.Length == 1)
		{
			return;
		}
		for (var i = 0; i < 15 && AdditionalStatusItemsNew.Length >= (i + 1) * 16; i++)
		{
			var array = new byte[4];
			var array2 = new byte[4];
			var array3 = new byte[4];
			var array4 = new byte[4];
			Buffer.BlockCopy(AdditionalStatusItemsNew, i * 16, array, 0, 4);
			if (BitConverter.ToInt32(array, 0) <= 0)
			{
				continue;
			}
			Buffer.BlockCopy(AdditionalStatusItemsNew, i * 16 + 4, array2, 0, 4);
			if (BitConverter.ToInt32(array2, 0) > 0)
			{
				Buffer.BlockCopy(AdditionalStatusItemsNew, i * 16 + 8, array3, 0, 4);
				if (BitConverter.ToInt32(array3, 0) > 0)
				{
					Buffer.BlockCopy(AdditionalStatusItemsNew, i * 16 + 12, array4, 0, 4);
					SetAdditionalStatusItems(BitConverter.ToInt32(array, 0), BitConverter.ToInt32(array2, 0), BitConverter.ToInt32(array3, 0), BitConverter.ToInt32(array4, 0));
				}
			}
		}
	}

	public void SetAdditionalStatusItems(int fldPid, int thoiGian, int soLuong, int soLuongLoaiHinh)
	{
		try
		{
			switch (fldPid)
			{
				case 1:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value14 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value14);
						switch (soLuongLoaiHinh)
						{
							case 2:
								addFLD_ThemVaoTiLePhanTram_Attack(0.01 * soLuong);
								break;
							case 1:
								FLD_NhanVat_ThemVao_CongKich += soLuong;
								break;
						}
						break;
					}
				case 2:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value10 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value10);
						switch (soLuongLoaiHinh)
						{
							case 2:
								addFLD_ThemVaoTiLePhanTram_PhongNgu(0.01 * soLuong);
								break;
							case 1:
								FLD_NhanVat_ThemVao_PhongNgu += soLuong;
								break;
						}
						break;
					}
				case 3:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value9 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value9);
						switch (soLuongLoaiHinh)
						{
							case 2:
								FLD_ThemVaoTiLePhanTram_HPCaoNhat += 0.01 * soLuong;
								break;
							case 1:
								CharactersToAddMax_HP += soLuong;
								break;
						}
						break;
					}
				case 4:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value2 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value2);
						switch (soLuongLoaiHinh)
						{
							case 2:
								FLD_ThemVaoTiLePhanTram_MPCaoNhat += 0.01 * soLuong;
								break;
							case 1:
								CharactersToAddMax_MP += soLuong;
								break;
						}
						break;
					}
				case 5:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value13 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value13);
						switch (soLuongLoaiHinh)
						{
							case 2:
								FLD_ThemVaoTiLePhanTram_TrungDich += 0.01 * soLuong;
								break;
							case 1:
								FLD_NhanVat_ThemVao_TrungDich += soLuong;
								break;
						}
						break;
					}
				case 6:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value15 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value15);
						switch (soLuongLoaiHinh)
						{
							case 2:
								FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh += 0.01 * soLuong;
								break;
							case 1:
								FLD_NhanVat_ThemVao_NeTranh += soLuong;
								break;
						}
						break;
					}
				case 7:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value6 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value6);
						if (soLuongLoaiHinh == 2)
						{
							FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram += 0.01 * soLuong;
						}
						break;
					}
				case 8:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value4 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value4);
						switch (soLuongLoaiHinh)
						{
							case 2:
								FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram += 0.01 * soLuong;
								break;
							case 1:
								FLD_NhanVat_ThemVao_NeTranh += soLuong;
								break;
						}
						break;
					}
				case 9:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value11 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value11);
						if (soLuongLoaiHinh == 2)
						{
							FLD_NhanVat_ThemVao_PhanTramKinhNghiem += 0.01 * soLuong;
						}
						break;
					}
				case 10:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value7 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value7);
						if (soLuongLoaiHinh == 2)
						{
							FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram += 0.01 * soLuong;
						}
						break;
					}
				case 11:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value3 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value3);
						break;
					}
				case 12:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value12 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value12);
						if (soLuongLoaiHinh == 2)
						{
							FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram += 0.01 * soLuong;
						}
						break;
					}
				case 13:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value8 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value8);
						if (soLuongLoaiHinh == 2)
						{
							FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram += 0.01 * soLuong;
						}
						break;
					}
				case 14:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value5 = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value5);
						FLD_NhanVat_ThemVao_KhiCong += soLuong;
						UpdateKhiCong();
						break;
					}
				case 15:
					{
						if (GetAddStateNew(fldPid))
						{
							AppendStatusNewList[fldPid].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_New_Loai value = new(this, fldPid, thoiGian, soLuong, soLuongLoaiHinh);
						AppendStatusNewList.Add(fldPid, value);
						if (soLuongLoaiHinh == 2)
						{
							FLD_NhanVat_ThemVao_PhanTramTraiNghiem += 0.01 * soLuong;
						}
						break;
					}
			}
			StateEffectsNew(fldPid, 1, thoiGian, soLuong, soLuongLoaiHinh);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Set ThemVao TrangThai VatPham 2 error：[" + fldPid + "]" + ex.ToString());
		}
	}

	public string Sublengthzero(int zerol)
	{
		var text = "";
		for (var i = 0; i < zerol; i++)
		{
			text += "0";
		}
		return text;
	}

	public void LyHon_TuBo_KetHon()
	{
		if (GiaiTruQuanHe_Countdown > 0)
		{
			return;
		}
		var array = Converter.HexStringToByte("AA558E002C017C1780000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000568055AA");
		CoupleTips(10, CharacterName, FLD_Couple);
		var characterData = GetCharacterData(FLD_Couple);
		if (characterData != null)
		{
			characterData.FLD_canYouSendFlowers = false;
			characterData.WhetherMarried = 0;
			characterData.CoupleTips(10, characterData.CharacterName, characterData.FLD_Couple);
			characterData.FLD_Couple = string.Empty;
			characterData.FLD_Couple_Love = 0;
			characterData.VoCongMoi[2, 16] = null;
			characterData.VoCongMoi[2, 17] = null;
			characterData.NhanCuoiKhacChu = string.Empty;
			characterData.GiaiTruQuanHe_Countdown = 0;
			characterData.UpdateMartialArtsAndStatus();
			characterData.UpdateCharacterData(characterData);
			characterData.UpdateBroadcastCharacterData();
			Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array, 4, 2);
			characterData.Client?.SendMultiplePackage(array, array.Length);
		}
		else
		{
			GameDb.UpdateDivorce(FLD_Couple);
			//RxjhClass.LyHonTrangThai(FLD_Couple);
		}
		WhetherMarried = 0;
		FLD_canYouSendFlowers = false;
		GiaiTruQuanHe_Countdown = 0;
		NhanCuoiKhacChu = string.Empty;
		FLD_Couple = string.Empty;
		FLD_Couple_Love = 0;
		VoCongMoi[2, 16] = null;
		VoCongMoi[2, 17] = null;
		NhanCuoiKhacChu = string.Empty;
		UpdateMartialArtsAndStatus();
		UpdateCharacterData(this);
		UpdateBroadcastCharacterData();
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
	}

	private void CheckGiftCode(string code)
	{
		try
		{
			using var enumerator = World.GiftCode.GetEnumerator();
			while (enumerator.MoveNext())
			{
				if (!(enumerator.Current.GiftCode == code))
				{
					continue;
				}
				if (enumerator.Current.Type > 99 && code != AccountID)
				{
					HeThongNhacNho("Mật mã quà tặng không thuộc về tài khoản này!", 22, "Thiên cơ các");
					return;
				}
				World.GiftCode.Remove(enumerator.Current);
				//DBA.ExeSqlCommand($"DELETE GiftCode WHERE GiftCode='{code}'", "PublicDb").GetAwaiter().GetResult();
				if (!PublicDb.DeleteGiftCode(code))
				{
					HeThongNhacNho("Có lỗi xảy ra với máy chủ! Vui lòng thử lại sau!", 10, "Thiên cơ các");
					return;
				}
				var array = Get_GiftCodeRewards(enumerator.Current.Type).Split(';');
				for (var i = 0; i < array.Length; i++)
				{
					var parcelVacancy = GetParcelVacancy(this);
					if (parcelVacancy != -1)
					{
						var array2 = array[i].Split(',');
						if (array2.Length == 12)
						{
							AddItem_ThuocTinh_int(int.Parse(array2[0]), parcelVacancy, int.Parse(array2[1]), int.Parse(array2[2]), int.Parse(array2[3]), int.Parse(array2[4]), int.Parse(array2[5]), int.Parse(array2[6]), int.Parse(array2[7]), int.Parse(array2[8]), int.Parse(array2[9]), int.Parse(array2[10]), int.Parse(array2[11]));
							continue;
						}
						HeThongNhacNho("LỖI! Phần thưởng bảo vật: LOẠI: [" + enumerator.Current.Type + "] - VỊ TRÍ: [" + i + "]!");
					}
					else
					{
						HeThongNhacNho("Hành trang đã đầy, không còn chỗ trống!!!");
					}
				}
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						value.HeThongNhacNho("Chúc mừng [" + CharacterName + "] nhập Gift Code [" + code + "] thành công!", 22, "Thiên cơ các");
					}
					return;
				}
			}
			HeThongNhacNho("Mật mã quà tặng: " + code + " không tồn tại trong giang hồ!", 22, "Thiên cơ các");
		}
		catch
		{
			HeThongNhacNho("Mật mã quà tặng đại hiệp vừa nhập là: " + code);
			HeThongNhacNho("Đã xảy ra lỗi, đại hiệp hãy liên lạc với Võ Lâm Minh Chủ!");
		}
	}

	private string Get_GiftCodeRewards(int type)
	{
		try
		{
			using (var enumerator = World.GiftCodeRewards.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.Type == type)
					{
						return enumerator.Current.Rewards;
					}
				}
			}
			HeThongNhacNho("ERROR! Loaòi giftcode không xaìc ðiònh: [" + type + "]");
			return "";
		}
		catch
		{
			HeThongNhacNho("LỖI! Nhận phần thưởng từ mật mã quà tặng thất bại!");
			return "";
		}
	}

	public void UpdateNpcDatacc(int npCid, int npCid2, Players playe)
	{
		var array = Converter.HexStringToByte("AA554C00000067003E00010000007D297D2911270100000032000000320000000080A24300007041004088440000804000000000000000000080A243000070410040884400000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(playe.SessionID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(npCid), 0, array, 14, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(npCid), 0, array, 16, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(npCid2), 0, array, 18, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(200), 0, array, 24, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(200), 0, array, 28, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(playe.PosX), 0, array, 32, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(playe.PosY), 0, array, 40, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(playe.PosZ), 0, array, 36, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 48, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 52, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(playe.PosX), 0, array, 56, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(playe.PosY), 0, array, 64, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(playe.PosZ), 0, array, 60, 4);
		playe.Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public static string FilterSpecial(string str)
	{
		if (str.Length == 0)
		{
			return str;
		}
		str = str.Replace("'", string.Empty);
		str = str.Replace("<", string.Empty);
		str = str.Replace(">", string.Empty);
		str = str.Replace("%", string.Empty);
		str = str.Replace("'delete", string.Empty);
		str = str.Replace("''", string.Empty);
		str = str.Replace("\"\"", string.Empty);
		str = str.Replace(",", string.Empty);
		str = str.Replace(".", string.Empty);
		str = str.Replace(">=", string.Empty);
		str = str.Replace("=<", string.Empty);
		str = str.Replace("-", string.Empty);
		str = str.Replace("_", string.Empty);
		str = str.Replace(";", string.Empty);
		str = str.Replace("||", string.Empty);
		str = str.Replace("[", string.Empty);
		str = str.Replace("]", string.Empty);
		str = str.Replace("&", string.Empty);
		str = str.Replace("#", string.Empty);
		str = str.Replace("/", string.Empty);
		str = str.Replace("-", string.Empty);
		str = str.Replace("|", string.Empty);
		str = str.Replace("?", string.Empty);
		str = str.Replace(">?", string.Empty);
		str = str.Replace("?<", string.Empty);
		str = str.Replace("      ", string.Empty);
		return str;
	}

	public void CloseTheTradingWindow()
	{
		var array = Converter.HexStringToByte("AA55420000009800340006000000060000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void Auto_cho_pet_an()
	{
		if (CharacterBeast != null && CharacterBeast.FLD_ZCD <= 1400)
		{
			CharacterBeast.FLD_ZCD = 2000;
		}
	}

	public void Trai_Tim_CPVP_Official()
	{
		if (AppendStatusList != null)
		{
			if (GetAddState(94))
			{
				AppendStatusList[94].ThoiGianKetThucSuKien();
			}
			if (GetAddState(95))
			{
				AppendStatusList[95].ThoiGianKetThucSuKien();
			}
			if (GetAddState(96))
			{
				AppendStatusList[96].ThoiGianKetThucSuKien();
			}
			if (GetAddState(200))
			{
				AppendStatusList[200].ThoiGianKetThucSuKien();
			}
		}
		else
		{
			AppendStatusList = new ThreadSafeDictionary<int, StatusEffect>();
		}
		var num = 200;
		StatusEffect(BitConverter.GetBytes(num), 1, 1800000);
		StatusEffect xThemVaoTrangThaiLoai = new(this, 1800000, num, 0);
		AppendStatusList.Add(xThemVaoTrangThaiLoai.FLD_PID, xThemVaoTrangThaiLoai);
		UpdateCharacterData(this);
		UpdateBroadcastCharacterData();
	}

	public void NhanVat_HoiSinh_BatTu()
	{
		var num = (KhuVuc_HoiSinh_Ngoai_TheLucChien ? 1000 : ((!KhuVuc_HoiSinh_Trong_TheLucChien) ? 5000 : 3000));
		if (AppendStatusList.ContainsKey(1000001508))
		{
			AppendStatusList[1000001508].ThoiGianKetThucSuKien();
		}
		StatusEffect value = new(this, num, 1000001508, 0);
		AppendStatusList.Add(1000001508, value);
		StatusEffect(BitConverter.GetBytes(1000001508), 1, num);
		NhanVat_BatTu = 1;
		UpdateBroadcastCharacterData();
		UpdateCharacterData(this);
	}
	
	public byte[] DROP_ITEM_ADD(int itemPid, int magic0, int magic1, int magic2, int magic3, int magic4, string itemname, Players players)
	{
		if (World.DrugRecord == 1)
		{
			LogHelper.WriteLine(0, "DROP-ITEM-ADD");
		}
		try
		{
			var dBItmeId = RxjhClass.CreateItemSeries();
			var array = new byte[(World.Newversion >= 14) ? 76 : 73];
			var bytes = BitConverter.GetBytes(dBItmeId);
			var array2 = new byte[56];
			Random random = new(World.GetRandomSeed());
			var num = random.NextDouble() * 25.0;
			if (World.ItemList.TryGetValue(itemPid, out var value))
			{
				if (value.FLD_QUESTITEM == 1)
				{
					if (players != null)
					{
						var parcelVacancy = players.GetParcelVacancy(players);
						if (parcelVacancy != -1)
						{
							players.AddItems(bytes, BitConverter.GetBytes(itemPid), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
						}
					}
					return null;
				}
				try
				{
					foreach (var itme in World.GroundItemList.Values)
					{
						var dropClass = World.Drop.FindAll((DropClass x) => x.FLD_PID == BitConverter.ToInt32(itme.Item.VatPham_ID, 0)).FirstOrDefault();
						if (dropClass != null && World.CoHayKo_Drop_CoMoThongBao != 0 && dropClass.CoMoThongBao > 0 && !players.Client.TreoMay)
						{
							var text = "Đại hiệp [" + players.CharacterName + "] may mắn đoạt được bảo vật [" + dropClass.FLD_NAME + "] tại [Kênh " + World.ServerID + "], ngay nơi đất thiêng [" + X_Toa_Do_Class.getmapname(players.MapID) + "] ở tọa độ [" + (int)players.PosX + "," + (int)players.PosY + "]. Giang hồ xôn xao, trân bảo lại tìm được chủ nhân!";
							World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text);
							// logo.Log_Lenh_Add_Drop_Item(text);
						}
					}
					Buffer.BlockCopy(BitConverter.GetBytes(magic0), 0, array2, 0, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(magic1), 0, array2, 4, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(magic2), 0, array2, 8, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(magic3), 0, array2, 12, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(magic4), 0, array2, 16, 4);
					Buffer.BlockCopy(bytes, 0, array, 0, 4);
					Buffer.BlockCopy(array2, 0, array, 16, 20);
					Buffer.BlockCopy(BitConverter.GetBytes(itemPid), 0, array, 8, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 12, 4);
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "DROP-ITEM-ADD lỗi 1: " + players.SessionID + "|" + players.CharacterName + " " + ex.Message);
					return null;
				}
				GroundItem xMatDatVatPhamLoai;
				try
				{
					xMatDatVatPhamLoai = new GroundItem(array, players.PosX + (float)num, players.PosY + (float)num, players.PosZ, players.MapID, null, 0);
					if (xMatDatVatPhamLoai == null)
					{
						LogHelper.WriteLine(LogLevel.Error, "DROP-ITEM-ADD lỗi 2: " + players.SessionID + "|" + players.CharacterName);
						return null;
					}
					if (!World.GroundItemList.ContainsKey(dBItmeId))
					{
						World.GroundItemList.Add(dBItmeId, xMatDatVatPhamLoai);
						xMatDatVatPhamLoai.AddToAOI();

					}
				}
				catch (Exception ex2)
				{
					LogHelper.WriteLine(LogLevel.Error, "DROP-ITEM-ADD lỗi 3: " + players.SessionID + "|" + players.CharacterName + " " + ex2.Message);
					return null;
				}
				try
				{
					if (World.GroundItemList.ContainsKey(dBItmeId))
					{
						xMatDatVatPhamLoai.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
					}
					return array;
				}
				catch (Exception ex3)
				{
					LogHelper.WriteLine(LogLevel.Error, "DROP-ITEM-ADD lỗi 4: " + players.SessionID + "|" + players.CharacterName + " " + ex3.Message);
					return null;
				}
			}
			return null;
		}
		catch (Exception ex4)
		{
			LogHelper.WriteLine(LogLevel.Error, "DROP-ITEM-ADD lỗi 5: " + players.SessionID + "|" + players.CharacterName + " " + ex4.Message);
			return null;
		}
	}

	public void MoveAll(object sender, ElapsedEventArgs e)
	{
		var num = 0;
		try
		{
			// if ((int)DateTime.Now.Subtract(XThmtime).TotalMilliseconds >= World.ThoiLuong_PhatHienNhipTim)
			// {
			// 	ThoiGian_Speed = 0;
			// 	Times = 0;
			// 	_yxsl = 0;
			// 	XThmtime = DateTime.Now;
			// 	if (TeamingStage == 1 && World.WToDoi.TryGetValue(TeamID, out var value) && value.DoiTruong.SessionID != SessionID)
			// 	{
			// 		var array = Converter.HexStringToByte("AA5514002D0134000600010001002D01000000000000000055AA");
			// 		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			// 		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 14, 2);
			// 		ProcessTeamRequest(array, array.Length);
			// 	}
			// }
			if ((int)DateTime.Now.Subtract(TMJCtime).TotalMilliseconds >= 3)
			{
				TMJCtime = DateTime.Now;
				if (MapID == 42001 && !AppendStatusList.ContainsKey(1008002170))
				{
					Mobile(560f, 1550f, 15f, 101, 0);
				}
			}
			// if (Player_AutoPartyString != "" && DateTime.Now.Subtract(time_party).TotalSeconds >= 3.0)
			// {
			// 	var array2 = Player_AutoPartyString.Split(',');
			// 	for (var i = 0; i < array2.Length; i++)
			// 	{
			// 		try
			// 		{
			// 			Regex regex = new("[a-zA-Z0-9]");
			// 			if (!regex.Match(array2[i]).Success)
			// 			{
			// 				HeThongNhacNho("Danh xưng nhân vật không hợp lệ!!", 20, "Thiên cơ các");
			// 				continue;
			// 			}
			// 			time_party = DateTime.Now;
			// 			SendTeam(array2[i]);
			// 		}
			// 		catch
			// 		{
			// 			LogHelper.WriteLine(LogLevel.Error, "Auto Party Error [" + CharacterName + "] Invite [" + array2[i] + "]");
			// 		}
			// 	}
			// }
			if ((int)DateTime.Now.Subtract(CWhmtime).TotalMinutes >= 5)
			{
				CWhmtime = DateTime.Now;
				if (CharacterBeast != null)
				{
					CharacterBeast.FLD_ZCD -= 100 + CharacterBeast.FLD_LEVEL * 2;
					num = 2;
					if (CharacterBeast.FLD_ZCD <= 100)
					{
						CharacterBeast.FLD_ZCD = 0;
						if (CharacterBeast.ThuBay == 1)
						{
							PetAction(9);
						}
						PetAction(6);
					}
					UpdateSpiritBeastHP_MP_SP();
				}
			}
			if ((int)DateTime.Now.Subtract(SThmtime).TotalMinutes >= 5 && MasterData.TID != -1)
			{
				var players = World.KiemTra_Ten_NguoiChoi(MasterData.STNAME);
				if (players != null)
				{
					var num2 = 2000;
					if (MasterData.STLEVEL == 4)
					{
						num2 = 2000;
					}
					else if (MasterData.STLEVEL == 3)
					{
						num2 = 2000;
					}
					else if (MasterData.STLEVEL == 2)
					{
						num2 = 2000;
					}
					else if (MasterData.STLEVEL == 1)
					{
						num2 = ((MasterData.STYHD < 20000) ? 50 : 0);
					}
					MasterData.TLEVEL = Player_Level;
					MasterData.STYHD += num2;
					ApprenticeUpdateMentoringSystem();
					for (var j = 0; j < 3; j++)
					{
						if (players.ApprenticeData[j].TID == MasterData.TID)
						{
							players.ApprenticeData[j].TLEVEL = Player_Level;
							players.ApprenticeData[j].STYHD += num2;
							players.MasterUpdatesTheMentoringSystem(j);
							break;
						}
					}
					SThmtime = DateTime.Now;
					HeThongNhacNho("Trực tuyến [" + 5 + "] đóng góp 2000 điểm mỗi khắc!");
				}
			}
			num = 3;
			if (World.PhaiChang_MoRaTreoMay_BanThuong == 1)
			{
				if (NhanVatThienVaAc < 0 && (int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
				{
					PKhmtime = DateTime.Now;
					NhanVatThienVaAc += 170;
					UpdateMartialArtsAndStatus();
					FLD_canYouSendFlowers = true;
					CheckMaritalStatus();
				}
				var num3 = 0;
				if (Player_Job_level >= 2 && Player_Job_level <= 5 && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT))
				{
					if ((int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						PKhmtime = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, dưới TT1 [+" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (Player_Job_level == 6 && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT))
				{
					if ((int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						PKhmtime = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 1;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, TT1 [+" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (Player_Job_level == 7 && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT))
				{
					if ((int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						PKhmtime = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 2;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, TT2 [+" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (Player_Job_level == 8 && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT))
				{
					if ((int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						PKhmtime = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 3;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, TT3 [+" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (Player_Job_level == 9 && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT))
				{
					if ((int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						PKhmtime = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 4;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, TT4 [+" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (Player_Job_level == 10 && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT))
				{
					if ((int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						PKhmtime = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 5;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, TT5 [+" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (Player_Job_level == 11 && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET) && !TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT))
				{
					if ((int)DateTime.Now.Subtract(PKhmtime).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						PKhmtime = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 6;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, TT6 [+" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_NHO_300))
				{
					if ((int)DateTime.Now.Subtract(Nhan_Vo_Huan_Online).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						Nhan_Vo_Huan_Online = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 10;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, sử dụng Võ Hoàng Đơn (nhỏ) nhận thêm [" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_VUA_500))
				{
					if ((int)DateTime.Now.Subtract(Nhan_Vo_Huan_Online).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						Nhan_Vo_Huan_Online = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 20;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, sử dụng Võ Hoàng Đơn (vừa) nhận thêm [" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_YEU_CAU))
				{
					if ((int)DateTime.Now.Subtract(Nhan_Vo_Huan_Online).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						Nhan_Vo_Huan_Online = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 30;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, sử dụng Võ Hoàng Đơn (lớn) nhận thêm [" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET))
				{
					if ((int)DateTime.Now.Subtract(Nhan_Vo_Huan_Online).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
					{
						Nhan_Vo_Huan_Online = DateTime.Now;
						num3 = World.TreoMayBanThuong_VoHuan + 30;
						HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, sử dụng Võ Hoàng Đơn (lớn) nhận thêm [" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
						Player_WuXun += num3;
						UpdateMartialArtsAndStatus();
						FLD_canYouSendFlowers = true;
						CheckMaritalStatus();
					}
				}
				else if (TitleDrug.ContainsKey(ItemConstants.VO_HOANG_DON_DAC_BIET_EVENT) && (int)DateTime.Now.Subtract(Nhan_Vo_Huan_Online).TotalMinutes >= World.TreoMayBanThuong_ThoiGianChuKyLapLai)
				{
					Nhan_Vo_Huan_Online = DateTime.Now;
					num3 = World.TreoMayBanThuong_VoHuan + 30;
					HeThongNhacNho("Trực tuyến [" + World.TreoMayBanThuong_ThoiGianChuKyLapLai + "] khắc, sử dụng Võ Hoàng Đơn (lớn) nhận thêm [" + num3 + "] Võ Huân!", 10, "Thiên cơ các");
					Player_WuXun += num3;
					UpdateMartialArtsAndStatus();
					FLD_canYouSendFlowers = true;
					CheckMaritalStatus();
				}
			}
			if (MapID == 1301 || MapID == 32001 || MapID == 32002)
			{
				var num4 = (int)DateTime.Now.Subtract(FBtime).TotalMinutes;
				if (num4 >= 3)
				{
					ActivityMapRemainingTime -= num4;
					FBtime = DateTime.Now;
				}
				if (ActivityMapRemainingTime <= 0)
				{
					HeThongNhacNho("Đại hiệp cần mua lệnh bài Phong Thần Khẩu tại NPC Bình Thập Chỉ để nhận miễn phí!!", 10, "Thiên cơ các");
					Mobile(420f, 1740f, 15f, 101, 0);
					ActivityMapRemainingTime = 0;
				}
			}
			if (MapID == 30000 || MapID == 30100 || MapID == 30200 || MapID == 30300)
			{
				num = 11;
				var num5 = (int)DateTime.Now.Subtract(FBtime).TotalMinutes;
				if (num5 >= 3)
				{
					num = 112;
					RemainingTimeOfTrainingMap -= num5;
					FBtime = DateTime.Now;
				}
				num = 113;
				if (RemainingTimeOfTrainingMap <= 0)
				{
					num = 114;
					HeThongNhacNho("Sự kiện chưa khai mở, đại hiệp tự động trở về Huyền Bột Phái!", 10, "Thiên cơ các");
					Mobile(420f, 1740f, 15f, 101, 0);
					RemainingTimeOfTrainingMap = 0;
				}
			}
			num = 12;
			if (Client == null)
			{
				if (CheckToaDo != null)
				{
					CheckToaDo.Close();
					CheckToaDo.Dispose();
					CheckToaDo = null;
				}
				return;
			}
			num = 13;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Move All error Lỗi: [" + num + "]-[" + AccountID + "]-[" + CharacterName + "]-[" + SessionID + "]-[" + ex.ToString());
			var players2 = World.KiemTra_Ten_NguoiChoi(CharacterName);
			if (players2 != null && players2.Client != null)
			{
				players2.Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 49]" + ex.ToString());
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 49]");
			}
		}
	}

	public void Cleaner(int id)
	{

		PublicDrugs.Remove(id);
		if (FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
		{
			FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
		}
		if (FLD_NhanVat_ThemVao_PhanTramTraiNghiem < 0.0)
		{
			FLD_NhanVat_ThemVao_PhanTramTraiNghiem = 0.0;
		}
		if (FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram < 0.0)
		{
			FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.0;
		}
		if (FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram < 0.0)
		{
			FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram = 0.0;
		}
		if (FLD_NhanVat_ThemVao_GiaBanTiLePhanTram < 0.0)
		{
			FLD_NhanVat_ThemVao_GiaBanTiLePhanTram = 0.0;
		}
		if (FLD_NhanVat_ThemVao_HapHonTiLe_TiLePhanTram < 0.0)
		{
			FLD_NhanVat_ThemVao_HapHonTiLe_TiLePhanTram = 0.0;
		}
		if (FLD_NhanVat_ThemVao_VoHuanThuHoach_SoLuongTiLePhanTram < 0.0)
		{
			FLD_NhanVat_ThemVao_VoHuanThuHoach_SoLuongTiLePhanTram = 0.0;
		}
		if (!(FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram >= 0.0))
		{
			FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram = 0.0;
		}
	}

	public void CheckMaritalStatus()
	{
		try
		{
			if (GiaiTruQuanHe_Countdown <= 0)
			{
				return;
			}
			GiaiTruQuanHe_Countdown -= World.TreoMayBanThuong_ThoiGianChuKyLapLai;
			if (GiaiTruQuanHe_Countdown > 0)
			{
				return;
			}
			var array = Converter.HexStringToByte("AA558E002C017C1780000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000568055AA");
			CoupleTips(10, CharacterName, FLD_Couple);
			var characterData = GetCharacterData(FLD_Couple);
			if (characterData != null)
			{
				characterData.FLD_canYouSendFlowers = false;
				characterData.WhetherMarried = 0;
				characterData.CoupleTips(10, characterData.CharacterName, characterData.FLD_Couple);
				characterData.FLD_Couple = string.Empty;
				characterData.FLD_Couple_Love = 0;
				characterData.VoCongMoi[2, 16] = null;
				characterData.VoCongMoi[2, 17] = null;
				characterData.NhanCuoiKhacChu = string.Empty;
				characterData.GiaiTruQuanHe_Countdown = 0;
				characterData.UpdateMartialArtsAndStatus();
				characterData.UpdateCharacterData(characterData);
				characterData.UpdateBroadcastCharacterData();
				Buffer.BlockCopy(BitConverter.GetBytes(characterData.SessionID), 0, array, 4, 2);
				characterData.Client?.SendMultiplePackage(array, array.Length);
			}
			else
			{
				GameDb.UpdateDivorce(FLD_Couple);
				//RxjhClass.LyHonTrangThai(FLD_Couple);
			}
			WhetherMarried = 0;
			FLD_canYouSendFlowers = false;
			GiaiTruQuanHe_Countdown = 0;
			NhanCuoiKhacChu = string.Empty;
			FLD_Couple = string.Empty;
			FLD_Couple_Love = 0;
			VoCongMoi[2, 16] = null;
			VoCongMoi[2, 17] = null;
			NhanCuoiKhacChu = string.Empty;
			UpdateMartialArtsAndStatus();
			UpdateCharacterData(this);
			UpdateBroadcastCharacterData();
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.SendMultiplePackage(array, array.Length);
		}
		catch
		{
		}
	}

	public bool MoveAllto(int far, X_Toa_Do_Class toaDo)
	{
		if (toaDo.Rxjh_Map != MapID)
		{
			return false;
		}
		var num = toaDo.Rxjh_X - PosX;
		var num2 = toaDo.Rxjh_Y - PosY;
		return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far;
	}

	public void TeleporterMove(byte[] packetData, int packetSize)
	{
		var array = new byte[4];
		Buffer.BlockCopy(packetData, 26, array, 0, 4);
		var num = BitConverter.ToInt32(array, 0);
		switch (num)
		{
			case 2:
				Mobile(0f, 80f, 15f, MapID + 1, 0);
				return;
			case 1:
				Mobile(0f, -80f, 15f, MapID - 1, 0);
				return;
			case 43001:
				Mobile(26f, -400f, 15f, MapID, 0);
				return;
		}
		foreach (var item in World.Mover)
		{
			if (item.MAP != MapID || item.ToMAP != num)
			{
				continue;
			}
			if (GMMode != 0 || (GMMode == 0 && item.ToMAP == World.HuyenBotPhai) || item.ToMAP == World.TamTaQuan || item.ToMAP == World.LieuChinhQuan || item.ToMAP == World.LieuThienPhu || item.ToMAP == World.NamMinhHieu || item.ToMAP == World.ThanVoMon || item.ToMAP == World.TungNguyetQuan || item.ToMAP == World.BachVoQuan || item.ToMAP == World.BacHaiBangCung || item.ToMAP == World.NamLam || item.ToMAP == World.HoHapCoc || item.ToMAP == World.XichThienGioi || item.ToMAP == World.ThienDuSon || item.ToMAP == World.ThanhDiaKiemHoang || item.ToMAP == World.PhongThanKhau)
			{
				var num2 = item.X - PosX;
				var num3 = item.Y - PosY;
				if ((int)Math.Sqrt(num2 * (double)num2 + num3 * (double)num3) > 200.0)
				{
					continue;
				}
				if (FLD_VIP == 0)
				{
					var vIpBanDo = World.VIP_BanDo;
					var separator = new char[1] { ';' };
					var array2 = vIpBanDo.Split(separator);
					var array3 = array2;
					foreach (var text in array3)
					{
						if (X_Toa_Do_Class.getmapname(item.ToMAP) == text)
						{
							HeThongNhacNho("Bí tịch (Bản đồ) chỉ dành cho thành viên, kẻ ngoài không được phép bước vào!", 10, "Thiên cơ các");
							return;
						}
					}
				}
				var array4 = World.BanDoKhoa_Chat.Split(';');
				if (array4.Length >= 1)
				{
					for (var j = 0; j < array4.Length; j++)
					{
						if (int.Parse(array4[j]) == MapID)
						{
							return;
						}
					}
				}
				Mobile(item.ToX, item.ToY, item.ToZ, item.ToMAP, 0);
				return;
			}
			HeThongNhacNho("Bản đồ này sẽ khai mở theo lịch trình!", 20, "Thiên cơ các");
			return;
		}
		LogHelper.WriteLine(LogLevel.Error, "Điểm dịch chuyển TeleporterMove Lỗi !! [" + AccountID + "][" + CharacterName + "][" + PosX + "," + PosY + "][" + MapID + "] - " + num);
	}


	public bool CheckIfThePlayerIsInTheDuelZone(Players playe)
	{
		foreach (var item in World.KhuTapLuyenPK)
		{
			if (item.Rxjh_Map == playe.MapID)
			{
				var num = item.Rxjh_X - playe.PosX;
				var num2 = item.Rxjh_Y - playe.PosY;
				if (!((int)Math.Sqrt(num * (double)num + num2 * (double)num2) > (double)World.SanTapPhamVi_HieuQua))
				{
					return true;
				}
			}
		}
		return false;
	}

	public bool CheckIfThePlayerIsInTheBattleArea(Players playe)
	{
		try
		{
			foreach (var item in World.TheLucChien_KhuVuc)
			{
				if (item.Rxjh_Map == playe.MapID)
				{
					return true;
				}
			}
			return false;
		}
		catch
		{
			return false;
		}
	}

	public bool KIEM_TRA_NGUOI_CHOI_CO_TRONG_TLC(Players playe)
	{
		try
		{
			using (var enumerator = World.TheLucChien_KhuVuc.GetEnumerator())
			{
				X_Toa_Do_Class xToaDoClass = null;
				while (enumerator.MoveNext())
				{
					xToaDoClass = enumerator.Current;
					if (xToaDoClass.Rxjh_Map == playe.MapID && playe.PosX < 150.0 && playe.PosX > -150.0 && playe.PosY < 150.0 && !(playe.PosY <= -150.0))
					{
						return true;
					}
				}
			}
			return false;
		}
		catch
		{
			return false;
		}
	}

	public bool TriggerTheNoviceSafeZone(Players playe)
	{
		if (playe.MapID == 101 && !(playe.CharacterName == CharacterName))
		{
			if (playe.PosX > 0.0 && playe.PosY > 1400.0 && playe.PosX < 1316.0 && playe.PosY < 2356.0)
			{
				return true;
			}
			if (playe.PosX > 0.0 && playe.PosY < 2356.0 && playe.PosX < 1316.0)
			{
				return playe.PosY > 1400.0;
			}
			return false;
		}
		return false;
	}

	public bool TLC_Check_Player_Trong_Khu_Vuc_PK_Khong(Players playe)
	{
		try
		{
			foreach (var item in World.TheLucChien_KhuVuc)
			{
				if (item.Rxjh_Map == playe.MapID && playe.PosX < 664.0 && playe.PosX > -664.0 && playe.PosY < 559.0 && !(playe.PosY <= -559.0))
				{
					return true;
				}
			}
			return false;
		}
		catch
		{
			return false;
		}
	}

	public bool Guild_CheckIfThePlayerIsInTheHelpZone(Players playe)
	{
		try
		{
			foreach (var item in World.BangChien_KhuVuc)
			{
				if (item.Rxjh_Map == playe.MapID && playe.PosX < 664.0 && playe.PosX > -664.0 && playe.PosY < 559.0 && !(playe.PosY <= -559.0))
				{
					return true;
				}
			}
			return false;
		}
		catch
		{
			return false;
		}
	}

	public bool CheckMonsterDistance(NpcClass npc)
	{
		if (npc.Rxjh_Map != MapID)
		{
			return false;
		}
		var num = npc.Rxjh_X - PosX;
		var num2 = npc.Rxjh_Y - PosY;
		float num3 = (int)Math.Sqrt(num * (double)num + num2 * (double)num2);
		if (Player_Job == 4)
		{
			if (num3 <= 60.0)
			{
				return true;
			}
		}
		else if (Player_Job == 5)
		{
			if (num3 <= 60.0)
			{
				return true;
			}
		}
		else if (Player_Job == 7)
		{
			if (num3 <= 60.0)
			{
				return true;
			}
		}
		else if (num3 <= 30.0)
		{
			return true;
		}
		return false;
	}

	public void StopAllTimers()
	{
		try
		{
			if (AutomaticRecovery != null)
			{
				AutomaticRecovery.Enabled = false;
				AutomaticRecovery.Close();
				AutomaticRecovery.Dispose();
				AutomaticRecovery = null;
			}
		}
		catch (Exception)
		{
		}
	}

	public void Offline_KetHon_Gui_Hoa_Hong()
	{
		var num = 0;
		try
		{
			Item xVatPhamLoai = null;
			Item xVatPhamLoai2 = null;
			Item xVatPhamLoai3 = null;
			var num2 = 0;
			num = 1;
			if ((xVatPhamLoai = FindItemByItemID(1000000891)) != null || (xVatPhamLoai2 = FindItemByItemID(1000000892)) != null || (xVatPhamLoai3 = FindItemByItemID(1000000893)) != null)
			{
				num = 2;
				var players = World.KiemTra_Ten_NguoiChoi(FLD_Couple);
				num = 3;
				if (players != null && FLD_Couple == players.CharacterName && !players.AppendStatusList.ContainsKey(1000000891) && !players.AppendStatusList.ContainsKey(1000000892) && !players.AppendStatusList.ContainsKey(1000000893))
				{
					num = 4;
					if (xVatPhamLoai != null)
					{
						num2 = 1000000891;
					}
					else if (xVatPhamLoai2 != null)
					{
						num2 = 1000000892;
					}
					else if (xVatPhamLoai3 != null)
					{
						num2 = 1000000893;
					}
					for (var i = 0; i < 96; i++)
					{
						if (BitConverter.ToInt32(players.Item_In_Bag[i].VatPham_ID, 0) == num2)
						{
							players.SubtractItem(i, 1);
							break;
						}
					}
					if (players.Offline_TreoMay_Mode_ON_OFF == 0)
					{
						players.CoupleFlowerRequest(3, CharacterName, FLD_Couple, num2);
					}
					else
					{
						num = 5;
						var num3 = 0;
						switch (num2)
						{
							case 1000000893:
								num3 = RNG.Next(30, 60);
								break;
							case 1000000892:
								num3 = RNG.Next(20, 40);
								break;
							case 1000000891:
								num3 = RNG.Next(10, 30);
								break;
						}
						num = 6;
						if (players.CoupleInTeam)
						{
							num3 *= 2;
						}
						CapNhatXepHang_HoaHong(players, players, 1);
						FLD_Couple_Love += num3;
						players.FLD_Couple_Love += num3;
						if (FLD_Couple_Love >= 35000)
						{
							FLD_Couple_Love = 35000;
							FLD_loveDegreeLevel = 1;
						}
						else if (FLD_Couple_Love > 30000)
						{
							if (FLD_loveDegreeLevel == 2)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 1;
						}
						else if (FLD_Couple_Love > 21000)
						{
							if (FLD_loveDegreeLevel == 3)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 2;
						}
						else if (FLD_Couple_Love > 14700)
						{
							if (FLD_loveDegreeLevel == 4)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 3;
						}
						else if (FLD_Couple_Love > 10290)
						{
							if (FLD_loveDegreeLevel == 5)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 4;
						}
						else if (FLD_Couple_Love > 7203)
						{
							if (FLD_loveDegreeLevel == 6)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 5;
						}
						else if (FLD_Couple_Love > 5042)
						{
							if (FLD_loveDegreeLevel == 7)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 6;
						}
						else if (FLD_Couple_Love > 3025)
						{
							if (FLD_loveDegreeLevel == 8)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 7;
						}
						else if (FLD_Couple_Love > 1513)
						{
							if (FLD_loveDegreeLevel == 9)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 8;
						}
						else if (FLD_Couple_Love > 605)
						{
							if (FLD_loveDegreeLevel == 10)
							{
								CoupleTips(27, CharacterName, players.CharacterName);
							}
							FLD_loveDegreeLevel = 9;
						}
						else
						{
							FLD_loveDegreeLevel = 10;
						}
						if (players.FLD_Couple_Love >= 35000)
						{
							players.FLD_Couple_Love = 35000;
							players.FLD_loveDegreeLevel = 1;
						}
						else if (players.FLD_Couple_Love > 30000)
						{
							if (players.FLD_loveDegreeLevel == 2)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 1;
						}
						else if (players.FLD_Couple_Love > 21000)
						{
							if (players.FLD_loveDegreeLevel == 3)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 2;
						}
						else if (players.FLD_Couple_Love > 14700)
						{
							if (players.FLD_loveDegreeLevel == 4)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 3;
						}
						else if (players.FLD_Couple_Love > 10290)
						{
							if (players.FLD_loveDegreeLevel == 5)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 4;
						}
						else if (players.FLD_Couple_Love > 7203)
						{
							if (players.FLD_loveDegreeLevel == 6)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 5;
						}
						else if (players.FLD_Couple_Love > 5042)
						{
							if (players.FLD_loveDegreeLevel == 7)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 6;
						}
						else if (players.FLD_Couple_Love > 3025)
						{
							if (players.FLD_loveDegreeLevel == 8)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 7;
						}
						else if (players.FLD_Couple_Love > 1513)
						{
							if (players.FLD_loveDegreeLevel == 9)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 8;
						}
						else if (players.FLD_Couple_Love > 605)
						{
							if (players.FLD_loveDegreeLevel == 10)
							{
								players.CoupleTips(27, players.CharacterName, CharacterName);
							}
							players.FLD_loveDegreeLevel = 9;
						}
						else
						{
							players.FLD_loveDegreeLevel = 10;
						}
						CoupleTips(29, CharacterName, players.CharacterName);
						players.CoupleTips(29, players.CharacterName, CharacterName);
						if (AppendStatusList != null)
						{
							if (GetAddState(1000000891))
							{
								AppendStatusList[1000000891].ThoiGianKetThucSuKien();
							}
							if (GetAddState(1000000892))
							{
								AppendStatusList[1000000892].ThoiGianKetThucSuKien();
							}
							if (GetAddState(1000000893))
							{
								AppendStatusList[1000000893].ThoiGianKetThucSuKien();
							}
						}
						else
						{
							AppendStatusList = new ThreadSafeDictionary<int, StatusEffect>();
						}
						StatusEffect(BitConverter.GetBytes(num2), 1, 1800000);
						StatusEffect xThemVaoTrangThaiLoai = new(this, 1800000, num2, 0);
						AppendStatusList.Add(xThemVaoTrangThaiLoai.FLD_PID, xThemVaoTrangThaiLoai);
						switch (FLD_loveDegreeLevel)
						{
							case 1:
								CharactersToAddMax_HP += 150;
								FLD_NhanVat_ThemVao_CongKich += 15;
								FLD_NhanVat_ThemVao_PhongNgu += 15;
								FLD_NhanVat_ThemVao_KhiCong++;
								FLD_NhanVat_ThemVao_PhanTramKinhNghiem += 0.05;
								UpdateKhiCong();
								break;
							case 2:
								CharactersToAddMax_HP += 150;
								FLD_NhanVat_ThemVao_CongKich += 15;
								FLD_NhanVat_ThemVao_PhongNgu += 15;
								FLD_NhanVat_ThemVao_KhiCong++;
								UpdateKhiCong();
								break;
							case 3:
								CharactersToAddMax_HP += 150;
								FLD_NhanVat_ThemVao_CongKich += 15;
								FLD_NhanVat_ThemVao_PhongNgu += 15;
								break;
							case 4:
								CharactersToAddMax_HP += 150;
								FLD_NhanVat_ThemVao_CongKich += 10;
								FLD_NhanVat_ThemVao_PhongNgu += 10;
								break;
							case 5:
								CharactersToAddMax_HP += 150;
								FLD_NhanVat_ThemVao_CongKich += 10;
								FLD_NhanVat_ThemVao_PhongNgu += 5;
								break;
							case 6:
								CharactersToAddMax_HP += 150;
								FLD_NhanVat_ThemVao_CongKich += 5;
								FLD_NhanVat_ThemVao_PhongNgu += 5;
								break;
							case 7:
								CharactersToAddMax_HP += 150;
								FLD_NhanVat_ThemVao_CongKich += 5;
								break;
							case 8:
								CharactersToAddMax_HP += 150;
								break;
							case 9:
								CharactersToAddMax_HP += 100;
								break;
							case 10:
								CharactersToAddMax_HP += 50;
								break;
						}
						UpdateMartialArtsAndStatus();
						CapNhat_HP_MP_SP();
					}
				}
			}
			Time_Su_Dung_HoTro_ChucNang = DateTime.Now;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Offline KetHon Gui Bong Lỗi tại num: [" + num + "] - [" + AccountID + "][" + CharacterName + "] - " + ex.Message);
		}
	}

	public async void WuxunSystem(Players mythis, Players targetPlayer, int sid)
	{
		try
		{
			if (targetPlayer.Player_Level < World.VoHuanBaoVe_CapDo)
			{
				mythis.HeThongNhacNho("Giới hạn cấp độ là [" + World.VoHuanBaoVe_CapDo + "], không thể thu được Võ Huân!", 20, "Truyền Âm Các");
			}
			else if (mythis.GuildName.Length != 0 && targetPlayer.GuildName.Length != 0 && mythis.GuildName == targetPlayer.GuildName)
			{
				mythis.HeThongNhacNho("Đại hiệp hạ sát [" + targetPlayer.CharacterName + "] - thành viên cùng bang hội, không thể thu được Võ Huân!", 20, "Truyền Âm Các");
				targetPlayer.HeThongNhacNho("Đại hiệp bị [" + mythis.CharacterName + "] hạ sát, do cùng thế lực nên không bị trừ Võ Huân!", 20, "Truyền Âm Các");
			}
			else if (mythis.Player_Zx == targetPlayer.Player_Zx)
			{
				mythis.HeThongNhacNho("Đại hiệp hạ sát [" + targetPlayer.CharacterName + "] - thành viên cùng thế lực, không thể thu được Võ Huân!", 20, "Truyền Âm Các");
				targetPlayer.HeThongNhacNho("Đại hiệp bị [" + mythis.CharacterName + "] hạ sát, do cùng thế lực nên không bị trừ Võ Huân!", 20, "Truyền Âm Các");
			}
			else if (mythis.LanIp != targetPlayer.LanIp || World.CoHayKhong_Trung_IP_PK_Tinh_Diem_TLC != 0)
			{
				var array = World.TuVong_GiamBotVoHuan_SoLuong.Split(';');
				var array2 = World.HeThongTaiChe_SoLan.Split(';');
				var num = Convert.ToInt32(array[0]);
				var num2 = Convert.ToInt32(array[1]);
				var num3 = Convert.ToInt32(array[2]);
				var num4 = Convert.ToInt32(array[3]);
				var num5 = Convert.ToInt32(array[4]);
				var num6 = Convert.ToInt32(array[5]);
				var num7 = Convert.ToInt32(array2[0]);
				var num8 = Convert.ToInt32(array2[1]);
				var num9 = Convert.ToInt32(array2[2]);
				var num10 = Convert.ToInt32(array2[3]);
				var num11 = Convert.ToInt32(array2[4]);
				var num12 = Convert.ToInt32(array2[5]);
				int num13;
				int num14;
				int num15;
				if (targetPlayer.Player_Level >= World.VoHuanBaoVe_CapDo && targetPlayer.Player_Level < World.VoHuanBaoVe_CapDo + 10)
				{
					num13 = num - num7;
					var playerWuXun = targetPlayer.Player_WuXun;
					num14 = num7;
					num15 = num;
				}
				else if (targetPlayer.Player_Level >= World.VoHuanBaoVe_CapDo + 10 && targetPlayer.Player_Level < World.VoHuanBaoVe_CapDo + 20)
				{
					num13 = num2 - num8;
					var playerWuXun2 = targetPlayer.Player_WuXun;
					num14 = num8;
					num15 = num2;
				}
				else if (targetPlayer.Player_Level >= World.VoHuanBaoVe_CapDo + 20 && targetPlayer.Player_Level < World.VoHuanBaoVe_CapDo + 30)
				{
					num13 = num3 - num9;
					var playerWuXun3 = targetPlayer.Player_WuXun;
					num14 = num9;
					num15 = num3;
				}
				else if (targetPlayer.Player_Level >= World.VoHuanBaoVe_CapDo + 30 && targetPlayer.Player_Level < World.VoHuanBaoVe_CapDo + 40)
				{
					num13 = num4 - num10;
					var playerWuXun4 = targetPlayer.Player_WuXun;
					num14 = num10;
					num15 = num4;
				}
				else if (targetPlayer.Player_Level >= World.VoHuanBaoVe_CapDo + 40 && targetPlayer.Player_Level < World.VoHuanBaoVe_CapDo + 50)
				{
					num13 = num5 - num11;
					var playerWuXun5 = targetPlayer.Player_WuXun;
					num14 = num11;
					num15 = num5;
				}
				else
				{
					num13 = num6 - num12;
					var playerWuXun6 = targetPlayer.Player_WuXun;
					num14 = num12;
					num15 = num6;
				}
				if (targetPlayer.Player_WuXun <= 0)
				{
					mythis.HeThongNhacNho("Võ Huân của đối phương đang âm, không thể trừ thêm!", 7, "Truyền Âm Các");
				}
				else if (World.ServerChoPKHayKhong != 0)
				{
					if (num13 > 0)
					{
						var daterecieve = DateTime.Now.ToString("ddMMyyyy");
						var num16 = await GameDb.CheckDailyHonor(CharacterName, daterecieve);
						if (num16 < World.VoHuan_GioiHan_MoiNgay)
						{
							if (MapID != 801 && MapID != 40101)
							{
								mythis.Player_WuXun += num13;
								mythis.NhanVoHuan_MoiNgay += num13;
								GameDb.UpdateDailyHonor(CharacterName, daterecieve, num16 + num13);
							}
							else
							{
								mythis.HeThongNhacNho("Bản đồ này không thể nhận Võ Huân!", 20, "Truyền Âm Các");
							}
						}
						else
						{
							mythis.HeThongNhacNho("Đại hiệp đã đạt giới hạn [" + World.VoHuan_GioiHan_MoiNgay + "] Võ Huân mỗi ngày!!", 20, "Truyền Âm Các");
						}
					}
					else
					{
						mythis.HeThongNhacNho("Không đủ điều kiện để hấp thụ Võ Huân vào thời điểm này!!", 20, "Truyền Âm Các");
					}
				}
				if (MapID == 1301)
				{
					var num17 = (int)(num13 * 1.5);
					targetPlayer.HeThongNhacNho("Nhận được [" + num17 + "] điểm Võ Huân, Thiên Cơ Các thu về [" + num14 + "] điểm >>> tăng thêm 1.5 lần <<<", 7, "Truyền Âm Các");
				}
				else
				{
					HeThongNhacNho("Đại hiệp hạ sát [" + targetPlayer.CharacterName + "] nhận được " + num13 + " điểm Võ Huân, Thiên Cơ Các thu về " + num14 + " điểm Võ Huân!", 7, "Truyền Âm Các");
				}
				if (targetPlayer.MapID == 1301)
				{
					num15 = (int)(num15 * 1.5);
					targetPlayer.HeThongNhacNho("Nhận được [" + num15 + "], Thiên Cơ Các thu lại [" + num15 + "] điểm Võ Huân!", 7, "Truyền Âm Các");
				}
				if (targetPlayer.MapID != 801 && targetPlayer.MapID != 40101)
				{
					targetPlayer.Player_WuXun -= num15;
					targetPlayer.MatDi_VoHuan += num15;
					targetPlayer.HeThongNhacNho("Đại hiệp bị [" + mythis.CharacterName + "] hạ sát, bị trừ " + num15 + " điểm Võ Huân!", 7, "Truyền Âm Các");
					targetPlayer.UpdateMartialArtsAndStatus();
				}
				if (sid == 1)
				{
					mythis.UpdateMartialArtsAndStatus();
					targetPlayer.UpdateMartialArtsAndStatus();
				}
				if (await GameDb.FindPkLog(mythis.CharacterName, targetPlayer.CharacterName) != 0)
				{
					await GameDb.UpdatePkLog(mythis.CharacterName, targetPlayer.CharacterName, num15, mythis.GuildName, targetPlayer.GuildName);
					//DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_PKLog  SET  FLD_NUM=FLD_NUM+1,FLD_LASTTIME='{DateTime.Now}',FLD_WX=FLD_WX+{num15},FLD_KILLER_GUILD='{mythis.GuildName}',FLD_DEATH_GUILD='{targetPlayer.GuildName}'      WHERE      FLD_KILLER='{mythis.CharacterName}'      AND      FLD_DEATH      =      '{targetPlayer.CharacterName}'      ").GetAwaiter().GetResult();
				}
				else
				{
					await GameDb.InsertPkLog(mythis.CharacterName, targetPlayer.CharacterName, num15, mythis.GuildName, targetPlayer.GuildName);
					//DBA.ExeSqlCommand($"INSERT  INTO  TBL_XWWL_PKLog  (FLD_KILLER,FLD_DEATH,FLD_NUM,FLD_WX,FLD_KILLER_GUILD,FLD_DEATH_GUILD)  VALUES  ('{mythis.CharacterName}','{targetPlayer.CharacterName}',{1},{num15},'{mythis.GuildName}','{targetPlayer.GuildName}')").GetAwaiter().GetResult();
				}
			}
			else
			{
				//mythis.HeThongNhacNho("Đối phương có cùng quan hệ thế lực với đại hiệp, không thể nhận Võ Huân!!", 20, "Truyền Âm Các");
			}
		}
		catch
		{
		}
	}

	public void SpiritOrbBag(int id, double kinhNghiem)
	{
		var ngungThanBaoChauViTri = NgungThanBaoChau_ViTri;
		if (ngungThanBaoChauViTri == -1)
		{
			return;
		}
		if (id == 3)
		{
			var num = (int)(kinhNghiem / 8.0);
			if (num < 0)
			{
				num = 1;
			}
			if (PublicDrugs.ContainsKey(1008000311) || PublicDrugs.ContainsKey(1008000312))
			{
				num *= 2;
			}
			Item_In_Bag[ngungThanBaoChauViTri].FLD_MAGIC0 += num;
			if (Item_In_Bag[ngungThanBaoChauViTri].FLD_MAGIC0 >= Item_In_Bag[ngungThanBaoChauViTri].FLD_MAGIC1)
			{
				var bytes = BitConverter.GetBytes(1008000315);
				var bytes2 = BitConverter.GetBytes(1);
				var array = new byte[56];
				Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[ngungThanBaoChauViTri].FLD_MAGIC1), 0, array, 0, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[ngungThanBaoChauViTri].FLD_MAGIC1), 0, array, 4, 4);
				var bytes3 = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
				SubtractItem(ngungThanBaoChauViTri, 1);
				IncreaseItem2(bytes3, bytes, ngungThanBaoChauViTri, bytes2, array);
				NgungThanBaoChau_ViTri = -1;
				return;
			}
		}
		SendingClass sendingClass = new();
		sendingClass.Write4(id);
		sendingClass.Write4(ngungThanBaoChauViTri);
		sendingClass.Write4(BitConverter.ToInt32(Item_In_Bag[ngungThanBaoChauViTri].VatPham_ID, 0));
		sendingClass.Write4(0);
		sendingClass.Write4(Item_In_Bag[ngungThanBaoChauViTri].FLD_MAGIC1);
		sendingClass.Write4(Item_In_Bag[ngungThanBaoChauViTri].FLD_MAGIC0);
		Client?.SendPak(sendingClass, 57368, SessionID);
	}

	public void Disconnect_VaoLai_DaiChienHon()
	{
		if (World.DCH_Progress <= 3 || World.DCH_Progress >= 6)
		{
			return;
		}
		foreach (var value2 in World.DanhSachNhanVat_ThamGia_DCH.Values)
		{
			if (World.DanhSachNhanVat_ThamGia_DCH.TryGetValue(value2.SessionID, out var _) && !value2.Client.TreoMay && value2.MapID != 40101)
			{
				if (value2.Player_Zx == 1)
				{
					value2.Mobile(-215f, -715f, 15f, 40101, 1);
					value2.DCH_DOIXANH(value2, 0);
					value2.DCH_TrangThaiTangHinh = 0;
					value2.DCH_PhePhai = "CHINHPHAI";
				}
				else if (value2.Player_Zx == 2)
				{
					value2.Mobile(245f, 705f, 15f, 40101, 1);
					value2.DCH_DOIDO(value2, 0);
					value2.DCH_TrangThaiTangHinh = 0;
					value2.DCH_PhePhai = "TAPHAI";
				}
				value2.SwitchPkMode(1);
			}
		}
	}

	public void BugGold_Recovery(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (Player_Money - Old_Gold_One_Sec > World.GiaTri_Gold_AutoBan && !check_bug_gold_tang_bat_thuong && Offline_TreoMay_Mode_ON_OFF == 0)
			{
				var txt = "[" + AccountID + "][" + CharacterName + "]- InNPC:[" + InTheShop + "]- GoldTruoc:[" + Old_Gold_One_Sec + "]- GoldSau:[" + Player_Money + "]- GoldTang:[" + (Player_Money - Old_Gold_One_Sec) + "]";
				// logo.Log_Bug_Gold_Bat_Thuong(txt);
				BanAccount(55, AccountID, "[" + CharacterName + "]-Bug Gold");
				Client.Dispose();
			}
			else
			{
				check_bug_gold_tang_bat_thuong = false;
				Old_Gold_One_Sec = Player_Money;
				if (check_gold_batthuong_den_tu_Mua_Ban)
				{
					Old_Gold_One_Sec = Player_Money;
				}
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "[try catch] Lỗi [Check Bug Gold] !!!");
		}
	}



	public void LoginAccount_2_Recovery(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (FLD_VIP != World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han && DetectionLimit(Client.ToString()))
			{
				HeThongNhacNho("Đại hiệp bị ngắt kết nối do vượt quá số tài khoản cho phép!", 20, "Thiên cơ các");
				OpClient(1);
				Logout();
				Client.Dispose();
				Thread.Sleep(10);
				World.allConnectedChars.TryRemove(SessionID, out _);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "[try catch] Lỗi [Check Bug Gold] !!!");
		}
	}

	public void Time_OutShop_Ctrl_Y(object sender, ElapsedEventArgs e)
	{
		try
		{
			OutOfShop(SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "[try catch] Lỗi Out Shop Ctrl Y Sender !!!");
		}
	}

	private async void Check_Item_Het_Han_su_dung(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (World.TheLucChien_MoRa == 1 && DateTime.Now.Hour == World.Time_KichHoat_HopTraiNghiem_Gio && DateTime.Now.Minute == World.Time_KichHoat_HopTraiNghiem_Phut && (DateTime.Now.Second == World.Time_KichHoat_HopTraiNghiem_Giay || DateTime.Now.Second == World.Time_KichHoat_HopTraiNghiem_Giay + 1 || DateTime.Now.Second == World.Time_KichHoat_HopTraiNghiem_Giay + 2))
			{
				World.ThoiGian_KichHoat_MoHop_TraiNghiem_16x = 1;
			}
			if (World.TheLucChien_MoRa == 1 && DateTime.Now.Hour == World.Time_Huy_HopTraiNghiem_Gio && DateTime.Now.Minute == World.Time_Huy_HopTraiNghiem_Phut && (DateTime.Now.Second == World.Time_Huy_HopTraiNghiem_Giay || DateTime.Now.Second == World.Time_Huy_HopTraiNghiem_Giay + 1 || DateTime.Now.Second == World.Time_Huy_HopTraiNghiem_Giay + 2))
			{
				World.ThoiGian_KichHoat_MoHop_TraiNghiem_16x = 0;
			}
			if (World.ThoiGian_KichHoat_MoHop_TraiNghiem_16x != 0 || FLD_PVP_Piont != 1 || (!(DateTime.Now < DateTime.Today.AddHours(20.0).AddMinutes(30.0)) && !(DateTime.Now > DateTime.Today.AddHours(21.0).AddMinutes(30.0))))
			{
				return;
			}
			var character = await GameDb.FindCharacter(CharacterName, AccountID);
			var array = character.fld_wearitem;
			for (var i = 0; i < 47; i++)
			{
				var array2 = new byte[World.Item_Db_Byte_Length];
				if (i >= 16 && i < 31)
				{
					if (array.Length >= i * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
					{
						try
						{
							Buffer.BlockCopy(array, i * World.Item_Db_Byte_Length, array2, 0, World.Item_Db_Byte_Length);
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Debug, "  " + ex.ToString());
						}
					}
					Sub_Wear[i - 16] = new Item(array2, i - 16);
					var array3 = new byte[4];
					Buffer.BlockCopy(Sub_Wear[i - 16].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array3, 0, 4);
					var num = BitConverter.ToInt32(array3, 0);
					if (num > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num)).TotalSeconds >= 0.0)
					{
						HeThongNhacNho("Trang bị xuyên thấu chứa bảo vật quá hạn [" + Sub_Wear[i - 16].GetItemName() + "], Thiên Cơ Các đã xóa bỏ!!", 20, "Truyền Âm Các");
						Sub_Wear[i - 16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					if (World.AllItmelog != 1)
					{
						continue;
					}
					try
					{
						if (Sub_Wear[i - 16].DatDuocVatPhamViTriLoaiHinh() != 1 && Sub_Wear[i - 16].DatDuocVatPhamViTriLoaiHinh() != 2 && Sub_Wear[i - 16].DatDuocVatPhamViTriLoaiHinh() != 5 && Sub_Wear[i - 16].DatDuocVatPhamViTriLoaiHinh() != 6)
						{
							if (Sub_Wear[i - 16].DatDuocVatPhamViTriLoaiHinh() != 4 && Sub_Wear[i - 16].DatDuocVatPhamViTriLoaiHinh() == 12 && (Sub_Wear[i - 16].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Sub_Wear[i - 16].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Sub_Wear[i - 16].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Sub_Wear[i - 16].ThuocTinh4.ThuocTinhLoaiHinh == 7))
							{
								Sub_Wear[i - 16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
							}
						}
						else if (Sub_Wear[i - 16].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Sub_Wear[i - 16].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Sub_Wear[i - 16].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Sub_Wear[i - 16].ThuocTinh4.ThuocTinhLoaiHinh == 7)
						{
							Sub_Wear[i - 16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
					}
					catch (Exception ex2)
					{
						LogHelper.WriteLine(LogLevel.Error, ex2.ToString());
					}
				}
				else if (i >= 31 && i < 37)
				{
					if (array.Length >= i * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
					{
						try
						{
							Buffer.BlockCopy(array, i * World.Item_Db_Byte_Length, array2, 0, World.Item_Db_Byte_Length);
						}
						catch (Exception ex3)
						{
							LogHelper.WriteLine(LogLevel.Debug, "  " + ex3.ToString());
						}
					}
					ThietBiTab3[i - 31] = new Item(array2, i - 31);
					var array4 = new byte[4];
					Buffer.BlockCopy(ThietBiTab3[i - 31].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array4, 0, 4);
					var num2 = BitConverter.ToInt32(array4, 0);
					if (num2 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num2)).TotalSeconds >= 0.0)
					{
						HeThongNhacNho("Trang bị xuyên thấu chứa bảo vật quá hạn [" + ThietBiTab3[i - 31].GetItemName() + "], Thiên Cơ Các đã xóa bỏ!!", 20, "Truyền Âm Các");
						ThietBiTab3[i - 31].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
				}
				else if (i == 46)
				{
					if (array.Length >= i * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
					{
						try
						{
							Buffer.BlockCopy(array, i * World.Item_Db_Byte_Length, array2, 0, World.Item_Db_Byte_Length);
						}
						catch (Exception ex4)
						{
							LogHelper.WriteLine(LogLevel.Debug, " --- " + ex4.ToString());
						}
					}
					Item_Wear[16] = new Item(array2, 16);
					var array5 = new byte[4];
					Buffer.BlockCopy(Item_Wear[16].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array5, 0, 4);
					var num3 = BitConverter.ToInt32(array5, 0);
					if (num3 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num3)).TotalSeconds >= 0.0)
					{
						HeThongNhacNho("Trang bị trong ô trang bị có bảo vật quá hạn [" + Item_Wear[16].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Truyền Âm Các");
						Item_Wear[16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					if (World.AllItmelog != 1)
					{
						continue;
					}
					try
					{
						if (Item_Wear[16].DatDuocVatPhamViTriLoaiHinh() != 1 && Item_Wear[16].DatDuocVatPhamViTriLoaiHinh() != 2 && Item_Wear[16].DatDuocVatPhamViTriLoaiHinh() != 5 && Item_Wear[16].DatDuocVatPhamViTriLoaiHinh() != 6)
						{
							if (Item_Wear[16].DatDuocVatPhamViTriLoaiHinh() != 4 && Item_Wear[16].DatDuocVatPhamViTriLoaiHinh() == 12 && (Item_Wear[16].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_Wear[16].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_Wear[16].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_Wear[16].ThuocTinh4.ThuocTinhLoaiHinh == 7))
							{
								Item_Wear[16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
							}
						}
						else if (Item_Wear[16].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_Wear[16].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_Wear[16].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_Wear[16].ThuocTinh4.ThuocTinhLoaiHinh == 7)
						{
							Item_Wear[16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
					}
					catch (Exception ex5)
					{
						LogHelper.WriteLine(LogLevel.Error, ex5.ToString());
					}
				}
				else
				{
					if (i >= 16)
					{
						continue;
					}
					if (array.Length >= i * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
					{
						try
						{
							Buffer.BlockCopy(array, i * World.Item_Db_Byte_Length, array2, 0, World.Item_Db_Byte_Length);
						}
						catch (Exception ex6)
						{
							LogHelper.WriteLine(LogLevel.Debug, " --- " + ex6.ToString());
						}
					}
					Item_Wear[i] = new Item(array2, i);
					var array6 = new byte[4];
					Buffer.BlockCopy(Item_Wear[i].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array6, 0, 4);
					var num4 = BitConverter.ToInt32(array6, 0);
					if (num4 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num4)).TotalSeconds >= 0.0)
					{
						HeThongNhacNho("Trang bị xuyên thấu chứa bảo vật quá hạn [" + Item_Wear[i].GetItemName() + "], Thiên Cơ Các đã xóa bỏ!!", 20, "Truyền Âm Các");
						Item_Wear[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
					if (World.AllItmelog != 1)
					{
						continue;
					}
					try
					{
						if (Item_Wear[i].DatDuocVatPhamViTriLoaiHinh() != 1 && Item_Wear[i].DatDuocVatPhamViTriLoaiHinh() != 2 && Item_Wear[i].DatDuocVatPhamViTriLoaiHinh() != 5 && Item_Wear[i].DatDuocVatPhamViTriLoaiHinh() != 6)
						{
							if (Item_Wear[i].DatDuocVatPhamViTriLoaiHinh() != 4 && Item_Wear[i].DatDuocVatPhamViTriLoaiHinh() == 12 && (Item_Wear[i].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_Wear[i].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_Wear[i].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_Wear[i].ThuocTinh4.ThuocTinhLoaiHinh == 7))
							{
								Item_Wear[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
							}
						}
						else if (Item_Wear[i].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_Wear[i].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_Wear[i].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_Wear[i].ThuocTinh4.ThuocTinhLoaiHinh == 7)
						{
							Item_Wear[i].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
					}
					catch (Exception ex7)
					{
						LogHelper.WriteLine(LogLevel.Error, ex7.ToString());
					}
				}
			}
			var array7 = (byte[])character.fld_pinkbag_item;
			for (var j = 0; j < 24; j++)
			{
				var array8 = new byte[World.Item_Db_Byte_Length];
				if (array7.Length >= j * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						Buffer.BlockCopy(array7, j * World.Item_Db_Byte_Length, array8, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception ex8)
					{
						LogHelper.WriteLine(LogLevel.Debug, " --- " + ex8.ToString());
					}
				}
				EventBag[j] = new Item(array8, j);
				var array9 = new byte[4];
				Buffer.BlockCopy(EventBag[j].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array9, 0, 4);
				var num5 = BitConverter.ToInt32(array9, 0);
				if (num5 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num5)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Áo choàng hành trang chứa bảo vật quá hạn [" + EventBag[j].GetItemName() + "], Thiên Cơ Các đã xóa bỏ!!", 20, "Thiên cơ các");
					EventBag[j].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
			}
			var array10 = (byte[])character.fld_item;
			for (var k = 0; k < 96; k++)
			{
				var array11 = new byte[World.Item_Db_Byte_Length];
				if (array10.Length >= k * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						Buffer.BlockCopy(array10, k * World.Item_Db_Byte_Length, array11, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception ex9)
					{
						LogHelper.WriteLine(LogLevel.Debug, "Lỗi nè !! - " + ex9.ToString());
					}
				}
				Item_In_Bag[k] = new Item(array11, k);
				if (Item_In_Bag[k].GetVatPham_ID == **********)
				{
					PhaiChangMangTheoAoChang_HanhLy = true;
				}
				if (k < 36)
				{
				}
				var array12 = new byte[4];
				Buffer.BlockCopy(Item_In_Bag[k].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array12, 0, 4);
				var num6 = BitConverter.ToInt32(array12, 0);
				if (num6 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num6)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Ba lô có vật phẩm quá thời hạn [" + Item_In_Bag[k].GetItemName() + "], Thiên cơ các đã xóa bỏ !!", 20, "Thiên cơ các");
					Item_In_Bag[k].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				if (World.AllItmelog != 1)
				{
					continue;
				}
				try
				{
					if (Item_In_Bag[k].DatDuocVatPhamViTriLoaiHinh() != 1 && Item_In_Bag[k].DatDuocVatPhamViTriLoaiHinh() != 2 && Item_In_Bag[k].DatDuocVatPhamViTriLoaiHinh() != 5 && Item_In_Bag[k].DatDuocVatPhamViTriLoaiHinh() != 6)
					{
						if (Item_In_Bag[k].DatDuocVatPhamViTriLoaiHinh() != 4 && Item_In_Bag[k].DatDuocVatPhamViTriLoaiHinh() == 12 && (Item_In_Bag[k].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_In_Bag[k].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_In_Bag[k].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_In_Bag[k].ThuocTinh4.ThuocTinhLoaiHinh == 7))
						{
							LogHelper.WriteLine(LogLevel.Debug, "Đã tìm thấy bảo vệ WG VatPham 111 Equipment Bar Package [" + AccountID + "]-[" + CharacterName + "] Position[" + k + "]  编号[" + BitConverter.ToInt32(Item_In_Bag[k].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + Item_In_Bag[k].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(Item_In_Bag[k].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + Item_In_Bag[k].FLD_MAGIC0 + "," + Item_In_Bag[k].FLD_MAGIC1 + "," + Item_In_Bag[k].FLD_MAGIC2 + "," + Item_In_Bag[k].FLD_MAGIC3 + "," + Item_In_Bag[k].FLD_MAGIC4 + "]");
							Item_In_Bag[k].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
					}
					else if (Item_In_Bag[k].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_In_Bag[k].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_In_Bag[k].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_In_Bag[k].ThuocTinh4.ThuocTinhLoaiHinh == 7)
					{
						LogHelper.WriteLine(LogLevel.Debug, "Đã tìm thấy bảo vệ WG VatPham 222 Equipment Bar Package [" + AccountID + "]-[" + CharacterName + "] Position[" + k + "]  编号[" + BitConverter.ToInt32(Item_In_Bag[k].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + Item_In_Bag[k].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(Item_In_Bag[k].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + Item_In_Bag[k].FLD_MAGIC0 + "," + Item_In_Bag[k].FLD_MAGIC1 + "," + Item_In_Bag[k].FLD_MAGIC2 + "," + Item_In_Bag[k].FLD_MAGIC3 + "," + Item_In_Bag[k].FLD_MAGIC4 + "]");
						Item_In_Bag[k].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
				}
				catch (Exception ex10)
				{
					LogHelper.WriteLine(LogLevel.Error, ex10.ToString());
				}
			}
			var array13 = (byte[])character.fld_fashion_item;
			for (var l = 0; l < 66; l++)
			{
				var array14 = new byte[World.Item_Db_Byte_Length];
				if (array13.Length >= l * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						Buffer.BlockCopy(array13, l * World.Item_Db_Byte_Length, array14, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception ex11)
					{
						LogHelper.WriteLine(LogLevel.Debug, " --- " + ex11.ToString());
					}
				}
				AoChang_HanhLy[l] = new Item(array14, l);
				var array15 = new byte[4];
				Buffer.BlockCopy(AoChang_HanhLy[l].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array15, 0, 4);
				var num7 = BitConverter.ToInt32(array15, 0);
				if (num7 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num7)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Áo choàng hành trang chứa bảo vật quá hạn [" + AoChang_HanhLy[l].GetItemName() + "], Thiên Cơ Các đã xóa bỏ!!", 20, "Thiên cơ các");
					AoChang_HanhLy[l].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
			}
			//var userPublicWarehouse = RxjhClass.GetUserPublicWarehouse(AccountID);
			var userPublicWarehouse = await GameDb.GetAndCreatePublicWarehouse(AccountID);
			if (userPublicWarehouse == null && Client != null)
			{
				Client.Dispose();
			}
			var array16 = (byte[])userPublicWarehouse.fld_item;
			ComprehensiveWarehouseEquipmentDataVersion = (int)userPublicWarehouse.fld_zbver;
			var array17 = (byte[])userPublicWarehouse.fld_itime;
			try
			{
				ComprehensiveWarehouseMoney = long.Parse(userPublicWarehouse.fld_money.ToString());
			}
			catch
			{
				ComprehensiveWarehouseMoney = 0L;
			}
			for (var m = 0; m < 60; m++)
			{
				var array18 = new byte[World.Item_Db_Byte_Length];
				if (array16.Length >= m * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						Buffer.BlockCopy(array16, m * World.Item_Db_Byte_Length, array18, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception value)
					{
						Console.WriteLine(value);
					}
				}
				PublicWarehouse[m] = new Item(array18, m);
				if (PublicWarehouse[m].GetVatPham_ID == **********)
				{
					PhaiChangMangTheoAoChang_HanhLy = true;
				}
				var array19 = new byte[4];
				Buffer.BlockCopy(PublicWarehouse[m].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array19, 0, 4);
				var num8 = BitConverter.ToInt32(array19, 0);
				if (num8 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num8)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Kho công cộng chứa bảo vật quá hạn [" + PublicWarehouse[m].GetItemName() + "], Thiên Cơ Các đã xóa bỏ!!", 20, "Thiên cơ các");
					PublicWarehouse[m].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				if (World.AllItmelog != 1)
				{
					continue;
				}
				try
				{
					if (PublicWarehouse[m].DatDuocVatPhamViTriLoaiHinh() != 1 && PublicWarehouse[m].DatDuocVatPhamViTriLoaiHinh() != 2 && PublicWarehouse[m].DatDuocVatPhamViTriLoaiHinh() != 5 && PublicWarehouse[m].DatDuocVatPhamViTriLoaiHinh() != 6)
					{
						if (PublicWarehouse[m].DatDuocVatPhamViTriLoaiHinh() != 4 && PublicWarehouse[m].DatDuocVatPhamViTriLoaiHinh() == 12 && (PublicWarehouse[m].ThuocTinh1.ThuocTinhLoaiHinh == 7 || PublicWarehouse[m].ThuocTinh2.ThuocTinhLoaiHinh == 7 || PublicWarehouse[m].ThuocTinh3.ThuocTinhLoaiHinh == 7 || PublicWarehouse[m].ThuocTinh4.ThuocTinhLoaiHinh == 7))
						{
							PublicWarehouse[m].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
					}
					else if (PublicWarehouse[m].ThuocTinh1.ThuocTinhLoaiHinh == 7 || PublicWarehouse[m].ThuocTinh2.ThuocTinhLoaiHinh == 7 || PublicWarehouse[m].ThuocTinh3.ThuocTinhLoaiHinh == 7 || PublicWarehouse[m].ThuocTinh4.ThuocTinhLoaiHinh == 7)
					{
						PublicWarehouse[m].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
				}
				catch (Exception ex12)
				{
					LogHelper.WriteLine(LogLevel.Error, ex12.ToString());
				}
			}
			//var userWarehouse = RxjhClass.GetUserWarehouse(AccountID, CharacterName);
			var userWarehouse = await GameDb.GetAndCreateWarehouse(AccountID, CharacterName);
			if (userWarehouse == null && Client != null)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![Lấy dữ liệu kho online phạm sai lầm]");
			}
			if (userWarehouse == null && Client != null)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 66]");
			}
			var array20 = (byte[])userWarehouse.fld_item;
			try
			{
				PersonalWarehouseMoney = long.Parse(userWarehouse.fld_money.ToString());
			}
			catch
			{
				PersonalWarehouseMoney = 0L;
			}
			for (var n = 0; n < 60; n++)
			{
				var array21 = new byte[World.Item_Db_Byte_Length];
				if (array20.Length >= n * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						Buffer.BlockCopy(array20, n * World.Item_Db_Byte_Length, array21, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception value2)
					{
						Console.WriteLine(value2);
					}
				}
				PersonalWarehouse[n] = new Item(array21, n);
				if (PersonalWarehouse[n].GetVatPham_ID == **********)
				{
					PhaiChangMangTheoAoChang_HanhLy = true;
				}
				var array22 = new byte[4];
				Buffer.BlockCopy(PersonalWarehouse[n].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array22, 0, 4);
				var num9 = BitConverter.ToInt32(array22, 0);
				if (num9 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num9)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Kho cá nhân chứa bảo vật quá hạn [" + PersonalWarehouse[n].GetItemName() + "], Thiên Cơ Các đã xóa bỏ!!", 20, "Thiên cơ các");
					PersonalWarehouse[n].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				if (World.AllItmelog != 1)
				{
					continue;
				}
				try
				{
					if (PersonalWarehouse[n].DatDuocVatPhamViTriLoaiHinh() != 1 && PersonalWarehouse[n].DatDuocVatPhamViTriLoaiHinh() != 2 && PersonalWarehouse[n].DatDuocVatPhamViTriLoaiHinh() != 5 && PersonalWarehouse[n].DatDuocVatPhamViTriLoaiHinh() != 6)
					{
						if (PersonalWarehouse[n].DatDuocVatPhamViTriLoaiHinh() != 4 && PersonalWarehouse[n].DatDuocVatPhamViTriLoaiHinh() == 12 && (PersonalWarehouse[n].ThuocTinh1.ThuocTinhLoaiHinh == 7 || PersonalWarehouse[n].ThuocTinh2.ThuocTinhLoaiHinh == 7 || PersonalWarehouse[n].ThuocTinh3.ThuocTinhLoaiHinh == 7 || PersonalWarehouse[n].ThuocTinh4.ThuocTinhLoaiHinh == 7))
						{
							PersonalWarehouse[n].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
					}
					else if (PersonalWarehouse[n].ThuocTinh1.ThuocTinhLoaiHinh == 7 || PersonalWarehouse[n].ThuocTinh2.ThuocTinhLoaiHinh == 7 || PersonalWarehouse[n].ThuocTinh3.ThuocTinhLoaiHinh == 7 || PersonalWarehouse[n].ThuocTinh4.ThuocTinhLoaiHinh == 7)
					{
						PersonalWarehouse[n].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
				}
				catch (Exception ex13)
				{
					LogHelper.WriteLine(LogLevel.Error, ex13.ToString());
				}
			}
			FLD_PVP_Piont = 0;
			if (Offline_TreoMay_Mode_ON_OFF == 0)
			{
				Thread.Sleep(1000);
				DangXuat(null, 0);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi check item het han su dung !!! ================= ");
		}
	}

	public void TongHop_Event_Exp_Time(object sender, ElapsedEventArgs e)
	{
		if (World.CoHayKo_BatTat_Event_Exp_TanThu == 1 && NumberOfRebirths == 0)
		{
			if (Player_Level >= 1 && Player_Level < 35)
			{
				Event_xExp_Player_Job_leve_8(this, 999000249);
			}
			else if (Player_Level >= 35 && AppendStatusList != null && AppendStatusList.ContainsKey(999000249))
			{
				AppendStatusList[999000249].ThoiGianKetThucSuKien();
			}
			if (Player_Level >= 35 && Player_Level < 60)
			{
				Event_xExp_Player_Job_leve_9(this, 999000239);
			}
			else if (Player_Level >= 60 && AppendStatusList != null && AppendStatusList.ContainsKey(999000239))
			{
				AppendStatusList[999000239].ThoiGianKetThucSuKien();
			}
			if (Player_Level >= 60 && Player_Level < 80)
			{
				Event_xExp_Player_Job_leve_10(this, 999000234);
			}
			else if (Player_Level >= 80 && AppendStatusList != null && AppendStatusList.ContainsKey(999000234))
			{
				AppendStatusList[999000234].ThoiGianKetThucSuKien();
			}
		}
		if (World.Event_Bonus_Rate != 0.0)
		{
			Event_x4(this);
		}
		else if (AppendStatusList != null && AppendStatusList.ContainsKey(World.IdItem_Bonus))
		{
			AppendStatusList[World.IdItem_Bonus].ThoiGianKetThucSuKien();
			FLD_NhanVat_ThemVao_KinhNghiem_Bonus = 0.0;
			World.Event_Bonus_Rate = 0.0;
		}
		if (World.CoHayKo_ON_OFF_Event_Exp_CuoiTuan == 1)
		{
			Event_x3_Saturday(this);
		}
		else if (AppendStatusList != null && AppendStatusList.ContainsKey(World.IdItemX3))
		{
			AppendStatusList[World.IdItemX3].ThoiGianKetThucSuKien();
		}
		if (PublicDrugs != null && (PublicDrugs.ContainsKey(1008000311) || PublicDrugs.ContainsKey(1008000312)))
		{
			Event_xExp_CTP(this);
		}
		else if (AppendStatusList != null && AppendStatusList.ContainsKey(1000000920))
		{
			AppendStatusList[1000000920].ThoiGianKetThucSuKien();
			FLD_NhanVat_ThemVao_Exp_CTP = 0.0;
			if (PublicDrugs.ContainsKey(1008000312))
			{
				PublicDrugs.Remove(1008000312);
				NewDrugEffects(1008000312, 0, 0u, 0u);
				UpdateMartialArtsAndStatus();
				UpdateCharacterData(this);
				UpdateBroadcastCharacterData();
			}
			else if (PublicDrugs.ContainsKey(1008000311))
			{
				PublicDrugs.Remove(1008000311);
				NewDrugEffects(1008000311, 0, 0u, 0u);
				UpdateMartialArtsAndStatus();
				UpdateCharacterData(this);
				UpdateBroadcastCharacterData();
			}
		}
		if (TeamID != 0 && World.WToDoi.TryGetValue(TeamID, out var value))
		{
			foreach (var value2 in value.PartyPlayers.Values)
			{
				if (value.PartyPlayers.Count >= 2 && value.PartyPlayers.Count <= 7 && value.PartyPlayers.Count != 8)
				{
					Buff_Party_Tu_2_Den_7_Nguoi(this);
				}
				else if (value.PartyPlayers.Count == 8)
				{
					Buff_Party_8_Nguoi(this);
				}
			}
		}
		if (World.EventX2ExpStatus == 0 && World.Eventx2 != 0)
		{
			World.EventX2ExpStatus = 1;
			Event_x2_Sunday(this);
			return;
		}
		if (DateTime.Now.Hour == 23 && DateTime.Now.Minute == 55 && DateTime.Now.Second == 0 && DateTime.Now.DayOfWeek == DayOfWeek.Saturday && (DateTime.Now.Second == 0 || DateTime.Now.Second == 1 || DateTime.Now.Second == 2))
		{
			HeThongNhacNho("Sau 5 khắc nữa sẽ khởi sự nhân đôi kinh nghiệm!", 8, World.ServerName);
			Time_Dem_Nguoc(300);
			return;
		}
		if (DateTime.Now.Hour == 23 && DateTime.Now.Minute == 55 && DateTime.Now.Second == 0 && DateTime.Now.DayOfWeek == DayOfWeek.Sunday && (DateTime.Now.Second == 0 || DateTime.Now.Second == 1 || DateTime.Now.Second == 2))
		{
			HeThongNhacNho("Sau 5 khắc nữa sẽ kết thúc nhân đôi kinh nghiệm!", 8, World.ServerName);
			Time_Dem_Nguoc(300);
			return;
		}
		if (World.EventX2ExpStatus == 1 && World.Eventx2 == 0 && DateTime.Now.DayOfWeek != 0)
		{
			World.EventX2ExpStatus = 0;
			{
				foreach (var value3 in World.allConnectedChars.Values)
				{
					if (value3.AppendStatusList.ContainsKey(World.IdItemX2))
					{
						value3.AppendStatusList[World.IdItemX2].ThoiGianKetThucSuKien();
					}
				}
				return;
			}
		}
		if (World.EventX2ExpStatus == 0 && DateTime.Now.DayOfWeek == DayOfWeek.Sunday)
		{
			World.EventX2ExpStatus = 1;
			Event_x2_Sunday(this);
		}
	}


	public async void ParticipateInASiege()
	{
		try
		{
			if (World.CongThanhChien_BatDau == null)
			{
				HeThongNhacNho("Công Thành Chiến chưa khai mở, không thể tham gia! Mỗi tuần vào ngày thứ nhất, thứ ba, thứ năm, 9 ấn khai chiến!", 10, "Thiên cơ các");
				return;
			}
			if (GuildName == string.Empty)
			{
				HeThongNhacNho("Đại hiệp chưa gia nhập môn phái, xin hãy gia nhập rồi mới tham gia!", 10, "Thiên cơ các");
				return;
			}
			if (World.CongThanhChien_Progress == 4)
			{
				HeThongNhacNho("Công Thành Chiến chưa khai mở, không thể tham gia! Mỗi tuần vào ngày thứ nhất, thứ ba, thứ năm, 9 ấn khai chiến!", 10, "Thiên cơ các");
				return;
			}
			//var dBToDataTable = DBA.GetDBToDataTable($"select  *  from  [CongThanhChien_ThanhChu]  ");
			var dBToDataTable = await GameDb.FindAllSiegeParticipants();
			if (dBToDataTable[0].congthanhchien_tenbang.ToString() == GuildName)
			{
				World.DangKyDanhSach_NguoiChoiCongThanhChien.Add(this);
				DemonMobile(-427f, -13f, 15f, 42001);
			}
			else if (GuildName == string.Empty)
			{
				HeThongNhacNho("Đại hiệp chưa gia nhập môn phái, xin hãy gia nhập rồi mới tham gia!", 10, "Thiên cơ các");
			}
			else
			{
				World.DangKyDanhSach_NguoiChoiCongThanhChien.Add(this);
				DemonMobile(-431f, -681f, 15f, 42001);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Áp dụng CongThanhChien chiến tranh error：" + ex);
		}
	}


	public void DissolveTheRelationshipBetweenMenAndWomen()
	{
		try
		{
			GiaiTruQuanHe_Countdown = 0;
			var players = World.KiemTra_Ten_NguoiChoi(FLD_Couple);
			if (players != null)
			{
				FLD_Couple = "";
				FLD_Couple_Love = 0;
				VoCongMoi[2, 16] = null;
				VoCongMoi[2, 17] = null;
				players.FLD_Couple = "";
				players.FLD_Couple_Love = 0;
				players.VoCongMoi[2, 16] = null;
				players.VoCongMoi[2, 17] = null;
				players.GiaiTruQuanHe_Countdown = 0;
				players.CoupleTips(15, FLD_Couple, CharacterName);
				CoupleTips(15, CharacterName, FLD_Couple);
				players.UpdateCoupleSystem(2, CharacterName, string.Empty, players.GiaiTruQuanHe_Countdown, DateTime.Now);
				UpdateCoupleSystem(2, FLD_Couple, string.Empty, GiaiTruQuanHe_Countdown, DateTime.Now);
				GameDb.UpdateMaritalStatus(FLD_Couple, 0);
			}
			else
			{
				HeThongNhacNho("Đối phương không trực tuyến, ly hôn thất bại!", 10, "Thiên cơ các");
			}
		}
		catch
		{
		}
	}


	public void CheckBackpackCopy(Players play)
	{
		Dictionary<long, Item> dictionary = new();
		for (var i = 0; i < 96; i++)
		{
			if (dictionary.TryGetValue(BitConverter.ToInt64(play.Item_In_Bag[i].ItemGlobal_ID, 0), out var value))
			{
				play.SubtractItem(i, value.GetVatPhamSoLuong);
			}
			else
			{
				dictionary.Add(BitConverter.ToInt64(play.Item_In_Bag[i].ItemGlobal_ID, 0), play.Item_In_Bag[i]);
			}
		}
		dictionary.Clear();
	}

	public int HeavenAndEarthTalisman()
	{
		if (AppendStatusList != null)
		{
			if (GetAddState(1008001026))
			{
				return 1;
			}
			if (GetAddState(1008001027))
			{
				return 2;
			}
			if (GetAddState(1008001028))
			{
				return 3;
			}
			if (GetAddState(1008001029))
			{
				return 4;
			}
			if (GetAddState(1008001030))
			{
				return 5;
			}
			if (GetAddState(1008001031))
			{
				return 6;
			}
			if (GetAddState(1008001032))
			{
				return 7;
			}
			if (GetAddState(1008001033))
			{
				return 8;
			}
			if (GetAddState(1008001034))
			{
				return 9;
			}
			if (GetAddState(1008001035))
			{
				return 10;
			}
		}
		return 0;
	}

	public Dictionary<int, ItmeClass> FetchProfessionalGoods(int reside2, int job, int zx, int sex, int level)
	{
		Dictionary<int, ItmeClass> dictionary = new();
		foreach (var value in World.ItemList.Values)
		{
			if (value.FLD_RESIDE2 == reside2 && value.FLD_RESIDE1 == job && (value.FLD_SEX == sex || value.FLD_SEX == 0) && value.FLD_LEVEL == level && value.FLD_RECYCLE_MONEY != 0)
			{
				dictionary.Add(value.FLD_PID, value);
			}
		}
		return null;
	}

	public void PickUpGetItems(int position, byte[] vatPhamSoLuong, byte[] itemGlobalId, byte[] vatPhamId, byte[] vatPhamThuocTinh, int type)
	{
		if (BitConverter.ToInt32(vatPhamSoLuong, 0) <= 0 || !World.ItemList.TryGetValue(BitConverter.ToInt32(vatPhamId, 0), out var value))
		{
			return;
		}
		if (value.FLD_QUESTITEM == 0)
		{
			if (type == 0 && HeavenAndEarthTalisman() != 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 5))
			{
				var dictionary = FetchProfessionalGoods(value.FLD_RESIDE2, HeavenAndEarthTalisman(), Player_Zx, Player_Sex, value.FLD_LEVEL);
				if (dictionary != null)
				{
					var key = RNG.Next(0, dictionary.Count - 1);
					vatPhamId = BitConverter.GetBytes(dictionary[key].FLD_PID);
				}
			}
			var bag = value.FLD_TYPE == 205 ? 193 : 1;
			var array = Converter.HexStringToByte("AA5579002D010D006B00010000002B9812110000000042EEF8050000000001000000000000000100090000020F000200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000998755AA");
			Buffer.BlockCopy(itemGlobalId, 0, array, 14, 8);
			Buffer.BlockCopy(vatPhamId, 0, array, 22, 4);
			Buffer.BlockCopy(vatPhamSoLuong, 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(bag), 0, array, 38, 1);
			Buffer.BlockCopy(BitConverter.GetBytes(position), 0, array, 40, 1);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array, 46, vatPhamThuocTinh.Length);
			byte[] name = Encoding.GetEncoding(World.Language_Charset).GetBytes(base.CharacterName);
			System.Buffer.BlockCopy(name, 0, array, 0x72, name.Length);

			var array2 = new byte[World.Item_Db_Byte_Length];
			Buffer.BlockCopy(itemGlobalId, 0, array2, 0, 8);
			Buffer.BlockCopy(vatPhamId, 0, array2, 8, 4);
			Buffer.BlockCopy(vatPhamSoLuong, 0, array2, 12, 4);
			Buffer.BlockCopy(vatPhamThuocTinh, 0, array2, 16, vatPhamThuocTinh.Length);
			if (bag == 193)
			{
				EventBag[position].VatPham_byte = array2;
			}
			else
			{
				Item_In_Bag[position].VatPham_byte = array2;
			}
			Item_In_Bag[position].VatPham_byte = array2;
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.SendMultiplePackage(array, array.Length);
		}
		else
		{
			SetUpQuestItems(BitConverter.ToInt32(vatPhamId, 0), BitConverter.ToInt32(vatPhamSoLuong, 0));
		}
	}

	public void THAO_TAC_PINKBAG_VAT_PHAM(int num, int num2, int num3, int num4, int num5)
	{
		var vatPhamByte = new byte[World.Item_Db_Byte_Length];
		if (EventBag[num2].Lock_Move || BitConverter.ToInt32(EventBag[num2].VatPham_ID, 0) == 0)
		{
			return;
		}
		switch (num3)
		{
			case 193:
				if (!EventBag[num2].Lock_Move)
				{
					if (BitConverter.ToInt32(EventBag[num4].VatPham_ID, 0) == 0)
					{
						EventBag[num4].VatPham_byte = EventBag[num2].VatPham_byte;
						EventBag[num2].VatPham_byte = vatPhamByte;
						ChangeEquipmentLocation(num, num2, num3, num4, EventBag[num4].VatPham_byte, BitConverter.ToInt32(EventBag[num4].VatPhamSoLuong, 0));
					}
					else
					{
						var vatPhamByte3 = EventBag[num4].VatPham_byte;
						EventBag[num4].VatPham_byte = EventBag[num2].VatPham_byte;
						EventBag[num2].VatPham_byte = vatPhamByte3;
						ChangeEquipmentLocation(num, num2, num3, num4, EventBag[num4].VatPham_byte, BitConverter.ToInt32(EventBag[num4].VatPhamSoLuong, 0));
					}
				}
				break;
			case 1:
				if (TitleDrug.ContainsKey(1008002684) || true)
				{
					if (BitConverter.ToInt32(EventBag[num2].VatPham_ID, 0) != 0 && !EventBag[num2].Lock_Move && BitConverter.ToInt32(Item_In_Bag[num4].VatPham_ID, 0) == 0)
					{
						if (Player_Money < 10000)
						{
							HeThongNhacNho("Đại hiệp thật nghèo, ngay cả 10,000 lượng cũng không có!!", 10, "Thiên cơ các");
							break;
						}
						Player_Money -= 10000L;
						Item_In_Bag[num4].VatPham_byte = EventBag[num2].VatPham_byte;
						EventBag[num2].VatPham_byte = vatPhamByte;
						ChangeEquipmentLocation(num, num2, num3, num4, Item_In_Bag[num4].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[num4].VatPhamSoLuong, 0));
						UpdateMoneyAndWeight();
					}
				}
				else
				{
					HeThongNhacNho("Cần bảo vật [Túi 1] mới có thể sử dụng!!", 10, "Thiên cơ các");
				}
				break;
			case 0:
				{
					if (BitConverter.ToInt32(EventBag[num2].VatPham_ID, 0) == 0 || EventBag[num2].Lock_Move || !World.ItemList.TryGetValue(BitConverter.ToInt32(EventBag[num2].VatPham_ID, 0), out var value) || (value.FLD_SEX != 0 && value.FLD_SEX != Player_Sex) || value.FLD_RESIDE2 != 9)
					{
						break;
					}
					ItmeClass value2;
					if (BitConverter.ToInt32(Item_Wear[num4].VatPham_ID, 0) == 0)
					{
						if (Player_Money < 1000000)
						{
							HeThongNhacNho("Đại hiệp thật nghèo, ngay cả 1,000,000 lượng cũng không có!", 10, "Thiên cơ các");
							break;
						}
						Player_Money -= 1000000L;
						Item_Wear[num4].VatPham_byte = EventBag[num2].VatPham_byte;
						EventBag[num2].VatPham_byte = vatPhamByte;
						ChangeEquipmentLocation(num, num2, num3, num4, Item_Wear[num4].VatPham_byte, BitConverter.ToInt32(Item_Wear[num4].VatPhamSoLuong, 0));
					}
					else if (World.ItemList.TryGetValue(BitConverter.ToInt32(Item_Wear[num4].VatPham_ID, 0), out value2))
					{
						if (value.FLD_RESIDE2 != value2.FLD_RESIDE2)
						{
							break;
						}
						var vatPhamByte2 = Item_Wear[num4].VatPham_byte;
						Item_Wear[num4].VatPham_byte = EventBag[num2].VatPham_byte;
						EventBag[num2].VatPham_byte = vatPhamByte2;
						ChangeEquipmentLocation(num, num2, num3, num4, Item_Wear[num4].VatPham_byte, BitConverter.ToInt32(Item_Wear[num4].VatPhamSoLuong, 0));
					}
					UpdateMoneyAndWeight();
					UpdateCharacterData(this);
					UpdateEquipmentEffects();
					CalculateCharacterEquipmentData();
					UpdateMartialArtsAndStatus();
					CapNhat_HP_MP_SP();
					break;
				}
		}
	}

}
