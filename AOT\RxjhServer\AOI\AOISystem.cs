using HeroYulgang.Helpers;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace RxjhServer.AOI
{
    public class AOISystem
    {
        #region Singleton
        private static readonly Lazy<AOISystem> _instance = new(() => new AOISystem());
        public static AOISystem Instance => _instance.Value;
        private AOISystem() { }
        #endregion

        #region Constants
        public const int GRID_SIZE = 512;
        public const int AOI_RADIUS = 512; 
        public const int GRIDS_PER_DIMENSION = 10;
        public const int MAP_SIZE = 5120;
        public const int HALF_MAP_SIZE = MAP_SIZE / 2;
        #endregion

        #region Data Structures
        private readonly ConcurrentDictionary<int, ConcurrentDictionary<(int, int), Grid>> _mapGrids = new();
        private readonly ConcurrentDictionary<int, (int, int)> _playerGrids = new();
        private readonly ConcurrentDictionary<int, (int, int)> _npcGrids = new();
        private readonly ConcurrentDictionary<long, (int, int)> _itemGrids = new();

        private static readonly Dictionary<int, (float x, float y)> _mapCenters = new()
        {
            { 201, (2560f, 0f) },
            { 301, (-2560f, 0f) }
        };

        public class Grid
        {
            public int X, Y, MapID;
            public readonly ConcurrentDictionary<int, byte> Players = new();
            public readonly ConcurrentDictionary<int, byte> NPCs = new();
            public readonly ConcurrentDictionary<long, byte> Items = new();
            public DateTime LastUpdate = DateTime.Now;
            public bool IsDirty;

            public int TotalEntities => Players.Count + NPCs.Count + Items.Count;

            public void ForEachPlayer(Action<Players> action)
            {
                foreach (var playerID in Players.Keys)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                        action(player);
                }
            }

            public IEnumerable<Players> GetPlayers()
            {
                foreach (var playerID in Players.Keys)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                        yield return player;
                }
            }

            public IEnumerable<NpcClass> GetNPCs()
            {
                foreach (var npcID in NPCs.Keys)
                {
                    if (MapClass.GetnpcTemplate(MapID).TryGetValue(npcID, out var npc))
                        yield return npc;
                }
            }

            public IEnumerable<GroundItem> GroundItems
            {
                get
                {
                    foreach (var itemID in Items.Keys)
                    {
                        if (World.GroundItemList.TryGetValue(itemID, out var item))
                            yield return item;
                    }
                }
            }
        }
        #endregion

        #region Grid Operations
        public (int x, int y) GetGridCoords(float posX, float posY, int mapID)
        {
            var center = _mapCenters.GetValueOrDefault(mapID, (0f, 0f));
            float adjustedX = posX - center.Item1;
            float adjustedY = posY - center.Item2;
            
            int gridX = Math.Clamp((int)((adjustedX + HALF_MAP_SIZE) / GRID_SIZE), 0, GRIDS_PER_DIMENSION - 1);
            int gridY = Math.Clamp((int)((adjustedY + HALF_MAP_SIZE) / GRID_SIZE), 0, GRIDS_PER_DIMENSION - 1);
            
            return (gridX, gridY);
        }

        private Grid GetOrCreateGrid(int mapID, int gridX, int gridY)
        {
            var mapGrids = _mapGrids.GetOrAdd(mapID, _ => new ConcurrentDictionary<(int, int), Grid>());
            return mapGrids.GetOrAdd((gridX, gridY), _ => new Grid { X = gridX, Y = gridY, MapID = mapID });
        }

        public List<Grid> GetNearbyGrids(int mapID, int centerX, int centerY)
        {
            var grids = new List<Grid>(9);
            var mapGrids = _mapGrids.GetValueOrDefault(mapID);
            if (mapGrids == null) return grids;

            for (int dx = -1; dx <= 1; dx++)
            {
                for (int dy = -1; dy <= 1; dy++)
                {
                    int x = centerX + dx;
                    int y = centerY + dy;
                    if (x >= 0 && x < GRIDS_PER_DIMENSION && y >= 0 && y < GRIDS_PER_DIMENSION)
                    {
                        if (mapGrids.TryGetValue((x, y), out var grid))
                            grids.Add(grid);
                    }
                }
            }
            return grids;
        }

        public List<Grid> GetNearbyGrids(int mapID, float posX, float posY)
        {
            var coords = GetGridCoords(posX, posY, mapID);
            return GetNearbyGrids(mapID, coords.Item1, coords.Item2);
        }
        #endregion

        #region Entity Management
        public void AddPlayer(Players player)
        {
            if (player?.Client?.Running != true) return;
            
            var coords = GetGridCoords(player.PosX, player.PosY, player.MapID);
            var grid = GetOrCreateGrid(player.MapID, coords.Item1, coords.Item2);
            
            if (grid.Players.TryAdd(player.SessionID, 0))
            {
                _playerGrids[player.SessionID] = coords;
                grid.IsDirty = true;
                UpdatePlayerAOI(player);
            }
        }

        public void RemovePlayer(int sessionID)
        {
            
            if (_playerGrids.TryRemove(sessionID, out var coords))
            {
                if (_mapGrids.TryGetValue(World.allConnectedChars.GetValueOrDefault(sessionID)?.MapID ?? 0, out var mapGrids))
                {
                    if (mapGrids.TryGetValue(coords, out var grid))
                    {
                        grid.Players.TryRemove(sessionID, out _);
                        grid.IsDirty = true;
                    }
                }
            }
        }

        public void MovePlayer(Players player, float newX, float newY)
        {
            if (player?.Client?.Running != true) return;
            
            var newCoords = GetGridCoords(newX, newY, player.MapID);
            if (_playerGrids.TryGetValue(player.SessionID, out var oldCoords) && oldCoords != newCoords)
            {
                // Move to new grid
                var oldGrid = GetOrCreateGrid(player.MapID, oldCoords.Item1, oldCoords.Item2);
                var newGrid = GetOrCreateGrid(player.MapID, newCoords.Item1, newCoords.Item2);
                
                oldGrid.Players.TryRemove(player.SessionID, out _);
                newGrid.Players.TryAdd(player.SessionID, 0);
                _playerGrids[player.SessionID] = newCoords;
                
                oldGrid.IsDirty = newGrid.IsDirty = true;
                UpdatePlayerAOI(player);
            }
            
            player.PosX = newX;
            player.PosY = newY;
        }

        public void AddNPC(NpcClass npc)
        {
            if (npc == null) return;
            
            var coords = GetGridCoords(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_Map);
            var grid = GetOrCreateGrid(npc.Rxjh_Map, coords.Item1, coords.Item2);
            
            if (grid.NPCs.TryAdd(npc.NPC_SessionID, 0))
            {
                _npcGrids[npc.NPC_SessionID] = coords;
                grid.IsDirty = true;
                UpdateNearbyPlayersAOI(npc.Rxjh_Map, coords.Item1, coords.Item2);
            }
        }

        public void RemoveNPC(int npcSessionID, int mapID)
        {
            if (_npcGrids.TryRemove(npcSessionID, out var coords))
            {
                if (_mapGrids.TryGetValue(mapID, out var mapGrids) && mapGrids.TryGetValue(coords, out var grid))
                {
                    grid.NPCs.TryRemove(npcSessionID, out _);
                    grid.IsDirty = true;
                    UpdateNearbyPlayersAOI(mapID, coords.Item1, coords.Item2);
                }
            }
        }

        public void AddGroundItem(GroundItem item)
        {
            if (item == null) return;
            
            var coords = GetGridCoords(item.PosX, item.PosY, item.MapID);
            var grid = GetOrCreateGrid(item.MapID, coords.Item1, coords.Item2);
            
            if (grid.Items.TryAdd(item.id, 0))
            {
                _itemGrids[item.id] = coords;
                grid.IsDirty = true;
                UpdateNearbyPlayersAOI(item.MapID, coords.Item1, coords.Item2);
            }
        }

        public void RemoveGroundItem(long itemID, int mapID)
        {
            if (_itemGrids.TryRemove(itemID, out var coords))
            {
                if (_mapGrids.TryGetValue(mapID, out var mapGrids) && mapGrids.TryGetValue(coords, out var grid))
                {
                    grid.Items.TryRemove(itemID, out _);
                    grid.IsDirty = true;
                    UpdateNearbyPlayersAOI(mapID, coords.Item1, coords.Item2);
                }
            }
        }

        public void UpdateNPCPosition(NpcClass npc, float newX, float newY)
        {
            if (npc == null) return;
            
            var newCoords = GetGridCoords(newX, newY, npc.Rxjh_Map);
            if (_npcGrids.TryGetValue(npc.NPC_SessionID, out var oldCoords) && oldCoords != newCoords)
            {
                // Move to new grid
                var oldGrid = GetOrCreateGrid(npc.Rxjh_Map, oldCoords.Item1, oldCoords.Item2);
                var newGrid = GetOrCreateGrid(npc.Rxjh_Map, newCoords.Item1, newCoords.Item2);
                
                oldGrid.NPCs.TryRemove(npc.NPC_SessionID, out _);
                newGrid.NPCs.TryAdd(npc.NPC_SessionID, 0);
                _npcGrids[npc.NPC_SessionID] = newCoords;
                
                oldGrid.IsDirty = newGrid.IsDirty = true;
                UpdateNearbyPlayersAOI(npc.Rxjh_Map, newCoords.Item1, newCoords.Item2);
            }
            
            npc.Rxjh_X = newX;
            npc.Rxjh_Y = newY;
        }

        public void UpdatePlayerPosition(Players player, float newX, float newY) => MovePlayer(player, newX, newY);
        #endregion

        #region AOI Updates
        public void UpdatePlayerAOI(Players player)
        {
            if (player?.Client?.Running != true) return;

            try
            {
                var coords = GetGridCoords(player.PosX, player.PosY, player.MapID);
                var nearbyGrids = GetNearbyGrids(player.MapID, coords.Item1, coords.Item2);
                
                var visiblePlayers = GetVisiblePlayers(player, nearbyGrids);
                var visibleNPCs = GetVisibleNPCs(player, nearbyGrids);
                var visibleItems = GetVisibleItems(player, nearbyGrids);

                UpdatePlayerVisibility(player, visiblePlayers);
                UpdateNPCVisibility(player, visibleNPCs);
                UpdateItemVisibility(player, visibleItems);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating AOI for {player.CharacterName}: {ex.Message}");
            }
        }

        private HashSet<Players> GetVisiblePlayers(Players player, List<Grid> grids)
        {
            var visible = new HashSet<Players>();
            foreach (var grid in grids)
            {
                foreach (var playerID in grid.Players.Keys)
                {
                    if (playerID != player.SessionID && World.allConnectedChars.TryGetValue(playerID, out var otherPlayer))
                    {
                        if (IsInRange(player.PosX, player.PosY, otherPlayer.PosX, otherPlayer.PosY))
                            visible.Add(otherPlayer);
                    }
                }
            }
            return visible;
        }

        private HashSet<NpcClass> GetVisibleNPCs(Players player, List<Grid> grids)
        {
            var visible = new HashSet<NpcClass>();
            foreach (var grid in grids)
            {
                foreach (var npcID in grid.NPCs.Keys)
                {
                    if (MapClass.GetnpcTemplate(player.MapID).TryGetValue(npcID, out var npc))
                    {
                        if (IsInRange(player.PosX, player.PosY, npc.Rxjh_X, npc.Rxjh_Y))
                            visible.Add(npc);
                    }
                }
            }
            return visible;
        }

        private HashSet<GroundItem> GetVisibleItems(Players player, List<Grid> grids)
        {
            var visible = new HashSet<GroundItem>();
            foreach (var grid in grids)
            {
                foreach (var itemID in grid.Items.Keys)
                {
                    if (World.GroundItemList.TryGetValue(itemID, out var item))
                    {
                        if (IsInRange(player.PosX, player.PosY, item.PosX, item.PosY))
                            visible.Add(item);
                    }
                }
            }
            return visible;
        }

        private void UpdatePlayerVisibility(Players player, HashSet<Players> newVisible)
        {
            if (player.NearbyPlayers == null) return;

            var current = new HashSet<Players>(player.NearbyPlayers.Values);
            
            // Add new players
            foreach (var newPlayer in newVisible.Except(current))
            {
                if (newPlayer?.Client?.Running == true)
                {
                    player.NearbyPlayers[(newPlayer.OriginalServerID, newPlayer.SessionID)] = newPlayer;
                    player.UpdateCharacterData(newPlayer);
                    
                    // Reciprocal
                    if (newPlayer.NearbyPlayers != null)
                    {
                        newPlayer.NearbyPlayers[(player.OriginalServerID, player.SessionID)] = player;
                        newPlayer.UpdateCharacterData(player);
                    }
                }
            }

            // Remove old players  
            foreach (var oldPlayer in current.Except(newVisible))
            {
                player.NearbyPlayers.TryRemove((oldPlayer.OriginalServerID, oldPlayer.SessionID), out _);
                player.NotifyPlayerExit(player, oldPlayer);
                
                // Reciprocal
                if (oldPlayer.NearbyPlayers != null)
                {
                    oldPlayer.NearbyPlayers.TryRemove((player.OriginalServerID, player.SessionID), out _);
                    oldPlayer.NotifyPlayerExit(oldPlayer, player);
                }
            }
        }

        private void UpdateNPCVisibility(Players player, HashSet<NpcClass> newVisible)
        {
            if (player.NearbyNpcs == null) return;

            var current = new HashSet<NpcClass>(player.NearbyNpcs.Values);
            var toAdd = newVisible.Except(current).ToDictionary(n => n.NPC_SessionID);
            var toRemove = current.Except(newVisible).ToDictionary(n => n.NPC_SessionID);

            foreach (var npc in toAdd.Values)
                player.NearbyNpcs.TryAdd(npc.NPC_SessionID, npc);

            foreach (var npc in toRemove.Values)
                player.NearbyNpcs.TryRemove(npc.NPC_SessionID, out _);

            if (toAdd.Count > 0) NpcClass.UpdateNPC_Spawn(toAdd, player);
            if (toRemove.Count > 0) NpcClass.UpdateNPC_Despawn(toRemove, player);
        }

        private void UpdateItemVisibility(Players player, HashSet<GroundItem> newVisible)
        {
            if (player.ListOfGroundItems == null) return;

            var current = new HashSet<GroundItem>(player.ListOfGroundItems.Values);
            var toAdd = newVisible.Except(current).ToDictionary(i => i.id);

            foreach (var item in toAdd.Values)
                player.ListOfGroundItems.TryAdd(item.id, item);

            foreach (var item in current.Except(newVisible))
            {
                player.ListOfGroundItems.TryRemove(item.id, out _);
                player.RemoveGroundItem(item.id);
            }

            if (toAdd.Count > 0) player.SendGroundItem(toAdd);
        }

        private void UpdateNearbyPlayersAOI(int mapID, int gridX, int gridY)
        {
            var nearbyGrids = GetNearbyGrids(mapID, gridX, gridY);
            foreach (var grid in nearbyGrids)
            {
                foreach (var playerID in grid.Players.Keys)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                        UpdatePlayerAOI(player);
                }
            }
        }
        #endregion

        #region Utility
        private bool IsInRange(float x1, float y1, float x2, float y2)
        {
            float dx = x2 - x1, dy = y2 - y1;
            return (dx * dx + dy * dy) <= (AOI_RADIUS * AOI_RADIUS);
        }

        public void BatchUpdate(List<Players> players)
        {
            foreach (var player in players.Where(p => p?.Client?.Running == true))
                UpdatePlayerAOI(player);
        }

        public int GetEntityCount(int mapID)
        {
            return _mapGrids.GetValueOrDefault(mapID)?.Values.Sum(g => g.TotalEntities) ?? 0;
        }
        #endregion
    }
}