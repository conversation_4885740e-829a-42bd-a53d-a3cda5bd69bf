using HeroYulgang.Helpers;
using RxjhServer;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class PlayersBes : <PERSON>_<PERSON><PERSON>_Cong_Thuoc_Tinh
    {
		public int FLD_CuongKhi_TrangBi { get; set; }
    public int Player_VoHoang { get; set; }
        public int Pet_CuongKhi { get; private set; }
	public bool TLC_DaTinhDiem_ChoMayRoi = false;

	public int DCH_HS_LuaChon;

	public int TargetSessionID;

	public int DCH_HP_BONUS;

	public int DCH_ATTACK_BONUS;

	public int DCH_DEF_BONUS;

	public double DCH_CLVC_BONUS;

	public double DCH_ULPT_BONUS;

	public int Event_TrangThai_Lock_DuongDua_F1;

	public int Event_CheckTrangThai_Lock_DuongDua_F1;

	public int DCH_TrangThaiTangHinh;

	public int TLC_TrangThaiCamPK;

	public int DCH_TrangThai_UnCheck;

	public int TLC_CheckTrangThaiCamPK;

	public int DCH_CheckTrangThai_UnCheck_2;

	public int DCH_CheckTrangThaiTangHinh;

	public int DCH_StackA;

	public int DCH_StackB;

	public int DCH_StackC;

	public int DCH_StackA_SoLuong;

	public int DCH_StackB_SoLuong;

	public int DCH_StackC_SoLuong;

	public long tickdupe;

	public long golđuple;

	public int ChucNang_Auto_ThucHien;

	public bool Thuong_Auto_IceBlock;

	public bool Ninja_Hut_Dame;

	public bool KCTT_16x_DaiPhu;

	public bool trangthai_miennhiem_debuff;

	public double dumplicate_exp;

	public double dumplicate_gold;

	public int las_autoskill = 0;

	public bool Tao_DangXaiAuto = false;

	public int Num_Attack;

	public int Player_Bien_Hinh_ID = 0;

	public int time_tao_danh_ConLai = 0;

	public int time_tao_danh_Char_DHL = 0;

	public int time_tao_danh_Cung = 0;

	public int time_tao_danh_ThichKhach = 0;

	public bool check_tao_danh_ra_dame = false;

	public int offline_buff_1 = 0;

	public int offline_buff_2 = 0;

	public int offline_buff_3 = 0;

	public int offline_buff_4 = 0;

	public int offline_buff_5 = 0;

	public ThreadSafeDictionary<int, KeyValuePair<int, DateTime>> timeDelaySkill;

	private object thisLock = new();

	public int DiDong_PhaiChang_Lan_1;

	public List<Players> tem = new();

	public List<int> ShortcutBar = new();

	public List<EventTopClass> Tao_GietNguoi_Trong_TLC = new();

	public List<int> DatDuoc_BangPhaiHuyHieu_ID = new();

	public float Speed = 127f;

	public X_Vo_Cong_Loai[,] VoCongMoi = new X_Vo_Cong_Loai[4, 32];

	public X_Cong_Kich_Loai DanhNhieuMucTieu_Loai_Bom_ThanNu;

	public List<X_Cong_Kich_Loai> DanhNhieuMucTieu_Loai_Bom_LuuNhiem;

	public long Old_Gold_One_Sec = 0L;

	public int WalkingStatusId = 1;

	public bool offline_buff = false;

	public bool offline_Cung_buff = false;

	public System.Timers.Timer CheckToaDo = new(6000.0);

	//public System.Timers.Timer Auto_Offline_Timer = new(2400.0);

	private byte[] KieuDang = [];

	public double QuyenSu_HoiTamNhatKich_UyLuc = 0.3;

	public string NhanCuoiKhacChu = string.Empty;

	public float TocDoDiChuyen_Max = 100f;

	public bool checkdelaydamexanh = false;

	public bool checkkepskill = false;

	public bool Buff_HoTro_Cung = false;

	public int HonNhan_NguoiDat_CauHoiDapAn = 2;

	public string TempGameSecurityCode = string.Empty;

	public string GameSecurityCode = string.Empty;

	public int VoCong_DanhLanCuoi_Khi_OffLine = 0;

	public string Player_AutoPartyString = "";

	public bool isholdlogin = false;

	public string StoreName = string.Empty;

	private string string_0 = string.Empty;

	private string string_1 = string.Empty;

	private string string_2 = string.Empty;

	private string string_3 = string.Empty;

	private string string_4 = string.Empty;

	private string string_5 = string.Empty;

	private string string_6 = string.Empty;

	private string string_9_1 = string.Empty;

	private string kich_hoat_tai_khoan = string.Empty;

	public int 是否拒绝势力战;

	private bool bool_0 = true;

	public string TLC_RANDOM_PHE = string.Empty;

	public string _TheLucChienPhePhai = string.Empty;

	public string _DCHPhePhai = string.Empty;

	public string ClientSettings = string.Empty;

	public byte[] MobileLastPacket = new byte[52];

	public byte[] AttackLastPacket = new byte[36];

	public byte[] HeartLastPacket = new byte[16];

	// Removed SerList - now using World.ServerList

	public System.Timers.Timer InvincibleTimeCounter;

	public System.Timers.Timer MobileMapTimer;

	public System.Timers.Timer RecoveryTimeCounter;

	public System.Timers.Timer RecoveryCheckBugGold;

	public System.Timers.Timer Recovery_Exp_ALL;

	public System.Timers.Timer Recovery_CheckLogin;

	public System.Timers.Timer Recovery_2_CheckLogin;

	public System.Timers.Timer Recovery_UnCheck_DCH;

	public System.Timers.Timer Recovery_CheckPill_TTTP;

	public System.Timers.Timer Check_Item_HetHanSuDung;

	public bool Ninja_BatDauPhanThuongKyNang;

	public bool checkcomboskill_DamHoaLien;

	public double Ninja_PhanThuongTanCongVatLy;

	public double Ninja_MaPhap_TanCong_TangThem;

	public double ThangThienKhiCong_CapDoVoCong;

	public bool WhetherToUpdateTheConfiguration;

	public int dame_gayboi_thannu_khidanhbom = 0;

	public int dame_gayboi_thannu_laynhiembom = 0;

	public bool Online;

	public bool ArchiveTime;

	public int delayphysic_ninja = 0;

	public ConfigClass Config;

	public int ThuocTinh_PhongAn;

	public bool PKTuVong;

	public bool Player_VoDich;

	public bool MoRaTrong_CuaHang;

	public bool MoRaGiaoDich;

	public bool MoRa_CuaHangCaNhan;

	public bool Exiting;

	public bool IsInLobby;

	public bool InTheShop;

	public bool YeuHoaThanhThao;

	public int InTheShopID;

	public int TuDongTiepTe;

	public int OfflineTreoMaySkill_ID;

	public int Auto_TreoMay_TheoDoi_ThoiGian;

	public int Offline_TreoMay_ToaDo_X;

	public int Offline_TreoMay_ToaDo_Y;

	public float PK_Save_ToaDo_X;

	public float PK_Save_ToaDo_Y;

	public bool OpenWarehouse;

	public int 是否排行 = 0;

	public int TinhGop_TonThuong = 0;

	public int VucSau_BOSS_TinhGopTonThuong = 0;

	public Dictionary<int, X_Nguoi_Truyen_Thu_Loai> DanhSach_TruyenThu;


	public bool FLD_canYouSendFlowers;

	public int _CurrentlyOperatingNPC;

	public int GioiHan_SoLan_Trung_BindAccount;

	public int Gioi_han_so_lan_dung_SK1;

	public bool Van_Cong;

	public int PVPScore;

	public int PVPEscapes;

	public bool check_tancong128_3mui_cung = false;

	public bool PhatDongVatLy_VoMinhAmThi;

	public bool PhatDong_TriMenhTuyetSat;

	public bool PhatDong_VoMinhAmThi;

	public bool check_bug_gold_tang_bat_thuong = false;

	public bool check_gold_batthuong_den_tu_Mua_Ban = false;

	public double TruocMat_DiDong_KhoangCach;

	public Dictionary<int, X_Cong_Huu_Duoc_Pham_Loai> SymbolList;

	public int _currentOperationType;

	public Dictionary<int, string> allChars;

	public X_Toa_Do_Class ToaDoNew;

	public X_Linh_Thu_Loai CharacterBeast;

	public ConcurrentDictionary<int, NpcClass> NearbyNpcs;


	public ConcurrentDictionary<double, GroundItem> ListOfGroundItems;

	public Hashtable ThoLinhPhu_ToaDo;

	public ThreadSafeDictionary<int, StatusEffect> AppendStatusList;

	public ThreadSafeDictionary<int, X_Them_Vao_Trang_Thai_New_Loai> AppendStatusNewList;

	public ThreadSafeDictionary<int, X_Di_Thuong_Trang_Thai_Loai> TrangThai_BatThuong;

	public ThreadSafeDictionary<int, X_Di_Thuong_Trang_Thai_Cong_Kich_Loai> TrangThai_TanCong_BatThuong;

	public ThreadSafeDictionary<int, X_Di_Thuong_Trang_Thai_Phong_Ngu_Loai> TrangThai_PhongThu_BatThuong;

	public ThreadSafeDictionary<int, X_Di_Thuong_Roi_Lam_Trang_Thai_Loai> TrangThai_XanhLam_BatThuong;

	public ThreadSafeDictionary<int, X_Di_Thuong_Mat_Mau_Trang_Thai_Loai> TrangThai_MatMau_BatThuong;

	public int TitlePoints;

	public ThreadSafeDictionary<int, X_Than_Nu_Di_Thuong_Trang_Thai_Loai> ThanNu_TrangThai_BatThuong;

	public Dictionary<int, X_Thoi_Gian_Duoc_Pham_Loai> TimeMedicine;

	public bool NoKhi;

	public List<X_Cong_Kich_Loai> AttackList;

	public List<int> NgocLienHoan;

	public List<X_Vo_Cong_Loai> NewMartialArtsCombos;

	public SortedDictionary<int, X_Thang_Thien_Khi_Cong_Loai> ThangThienKhiCong;

	public SortedDictionary<int, X_Thang_Thien_Khi_Cong_Loai> PhanKhiCong;

	public X_Nguoi_Cua_Hang_Loai CuaHangCaNhan;

	public int MartialArtsComboCounter;

	public int EquipmentDataVersion;

	public int ComprehensiveWarehouseEquipmentDataVersion;

    public DateTime CreatedAt { get; private set; }

    public Item[] PersonalWarehouse;

	public Item[] PublicWarehouse;

	public Item[] Item_In_Bag;

	public Item[] AoChang_HanhLy;

	public Item[] EventBag;

	public Item[] Item_Wear;

	public Item[] Sub_Wear;

	public Item[] ThietBiTab3;

	public Item[] Item_NTC;

	public int NgungThanBaoChau_ViTri;

	public X_Khi_Cong_Loai[] KhiCong;

	public X_Nhiem_Vu_Vat_Pham_Loai[] NhiemVu_VatPham;

	public Dictionary<int, X_Nhiem_Vu_Loai> QuestList;

	public Dictionary<int, X_Nhiem_Vu_Theo_Ngay> CompletedQuestList;

	public X_Su_Do_Loai MasterData;

	public X_Su_Do_Loai[] ApprenticeData;

	public double CurrentMovingDistance;

	public double PersonDistanceCalculation;

	public bool TriggerKillingStarYiqiTiger;

	public bool TriggerKillingStarLoyaltyKill;

	public float destX;

	public float destY;

	public float destZ;

	public float fromX;

	public float fromY;

	public float fromZ;

	public float reach;

	public X_Giao_Dich_Loai GiaoDich;

	public int GMMode;

	public int TeamID;

	public int TeamingStage;

	public int HinhThuc_TangHinh;

	public double NINJA_LienTieuDaiDa_SoLuong;

	public double HanBaoQuan_ThienMaCuongHuyet_LucCongKich;

	public int HanBaoQuan_ThienMaCuongHuyet_XSoLan;

	public byte[] PersonalMedicine;

	public byte[] AdditionalStatusItemsNew;

	public double ExplosiveState;

	public double FLD_TrangBi_ThuocTinhHoa_ThemVao_TangDame_QuaiVat;

	public int TrangThai_AnThan_id;

	public int CloseUp;

	public int SafeMode;

	public int ThangThienLichLuyen_TangKinhNghiemNhanDuoc_HienTai;

	public int ThangThienLichLuyen_KinhNghiem;

	public int ThangThienVoCong_DiemSo;

	public int FLD_NUMBER_OPEN;

	public int ThanNuVoCongDiemSo;

	public bool ChayTron;

	public int RemainingTimeOfTrainingMap;

	public int ActivityMapRemainingTime;

	public int ThienDiaDongThoNhungLanNeTranh;

	public int ThienDiaDongThoNeTranhTichLuyDame;

	public DateTime Delay_Bom_Mau;

	public DateTime time_party;

	public DateTime time_party1;

	public DateTime time_party2;

	public DateTime time_party3;

	public DateTime Time_Auto_AFK_NhatDo;

	public DateTime Time_Nhat_Item_Vut_Ra;

	public DateTime Time_Nhat_Item_Vut_Ra_1;

	public DateTime Time_Bieu_Cam;

	public DateTime Time_Nhat_Item;

	public DateTime Time_Relog;

	public DateTime FBtime;

	public DateTime Nhan_Vo_Huan_Online;

	public DateTime PKhmtime;

	public DateTime dateTime_000;

	public DateTime SThmtime;

	public DateTime XThmtime;

	public DateTime CWhmtime;

	public DateTime TMJCtime;

	public DateTime Offline_Auto_Attack_Time;

	public DateTime Auto_Train_Track;

	public DateTime Time_Su_Dung_HoTro_ChucNang;

	public DateTime Time_Bom_HP_Auto_Offline;

	public DateTime Time_HoiSinh_Auto_Offline;

	public int Offline_TreoMay_BanDo;

	public int Offline_TreoMay_Mode_ON_OFF;

	public int BachDocBatXam;

	public int MatDi_VoHuan;

	public int FLD_TrangBi_ThemVao_DoiQuai_CongKich;

	public int FLD_TrangBi_ThemVao_DoiQuai_PhongNgu;

	public int FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu;

	public int DuocPham_ThemVao_DoiQuai_CongKich;

	public int DuocPham_ThemVao_DoiQuai_CongKich_NEW;

	public int DuocPham_ThemVao_DoiQuai_PhongNgu;

	public int DuocPham_ThemVao_DoiQuai_PhongNgu_NEW;

	public int DuocPham_TuHaThanDan_ThemVao_DoiQuai_CongKich;

	public int DuocPham_TuHaThanDan_ThemVao_DoiQuai_PhongNgu;

	public int TheLucChien_Tang_HP_TayDai;

	public int TheLucChien_Tang_HP_TayNgan;

	public int TTTP_Tang_HP;

	public int TTTP_Tang_HP_NEW;

	public int TTTP_Tang_MP;

	public int TTTP_Tang_MP_NEW;

	public int FLD_DuocPham_ThemVao_KhiCong;

	public int NhanVoHuan_MoiNgay;

	public int VoHoang_ThongTe;

	public int QuyenSu_KiemSoat_ComBo;

	public int Khoa_ChatTyLeCaCuocNhanVat;

	public bool NhanVatKhoa_Chat;

	public float AttackX;

	public float AttackY;

	public int NumberOfAttacks;

	public float lastX;

	public float lastY;

	public int lastMAP;

	public bool Poisoning;

	public bool PhaiChangMangTheoAoChang_HanhLy;

	public int GiaiTruQuanHe_Countdown;

	public int WhetherMarried;

	public int AnToan_MatMa_ThaoTacID;

	public bool WhetherTheSecurityCodeIsVerified;

	public int GiaoDichNhanVat_ID;

	public int GiaoDichThaoTacD;

	public bool TriggerMapMovementEvent;

	public bool PhatDong_LoanPhuongHoaMinh;

	public bool Flag_resetThuocTinh_X2_VK = false;

	public bool Flag_resetThuocTinh_X2_Ao = false;

	public int TriggerAttributePromotion;

	public bool PhatDong_LuuTinhManThien;

	public bool Trigger_NhatDiemNguThanh;

	public bool PhatDong_LuuVanCuongKich;

	public bool PhatDong_LuuTinhNghiaKhiSat;

	public bool PhatDong_LuuTinhNghiaKhiHo;

	public bool Van_Cong_Ctrl_C;

	public int HoaHongXungHaoTichLuy;

	public bool VanDocBatXam;

	public bool CoupleInTeam;

	public int RoseTitlePoints;

	public bool PhaiChangKiemTraNhipTim;

	public int tracking_status_id1;

	public int MartialTitleType;

	public int TitleRanking;

	public string _CharacterPartitionID;

	private int int_0;

	public int _VoChong_VoCong_LucCongKich_MP;

	public int _CharacterPacketID;

	public bool tracking_status;

	private string string_7;

	private int int_1;

	private int int_2;

	private int int_3;

	private string string_8;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private bool bool_1;

	private int int_9;

	private int int_10;

	private int int_11;

	private int int_12;

	private int int_13;

	private int int_14;

	private byte[] _HuyHieuBang;

	private int int_15;

	private int int_16;

	private long long_0;

	private long long_1;

	private long long_2;

	private int int_17;

	private int int_18;

	private int int_19;

	private int int_20;

	private int int_21;

	private int int_22;

	private int int_23;

	private int int_24;

	private int int_25;

	private int int_26;

	private int int_27;

	private int int_28;

	private int int_28_PhanTram_HP;

	private int int_28_PhanTram_MP;

	private int int_29;

	private int int_30;

	private int int_31;

	private int int_32;

	private int int_33;

	private int int_34;

	private int int_35;

	private int int_36;

	private int int_37;

	private int int_38;

	private int int_39;

	private int int_40;

	private int int_41;

	private int int_42;

	private int int_43;

	private int int_44;

	private int int_45;

	private int int_46;

	private int int_47;

	public X_Character_Template_Class NewCharacterTemplate;

	private byte[] _CharacterNameTemplate;

	private int int_48;

	private float float_0;

	private float float_1;

	private float float_2;

	private int int_49;

	private int int_50;

	private int int_51;

	private int int_52;

	private long long_3;

	private long long_4;

	private int int_53;

	private int int_54;

	private int int_55;

	private int int_1199;

	private int int_1200;

	public Dictionary<int, X_Cong_Huu_Duoc_Pham_Loai> PublicDrugs;

	public Dictionary<int, PillItem> TitleDrug;

	private int int_56;

	private int int_57;

	private DateTime dateTime_0;

	private int int_58;

	private int int_59;

	private int int_60;

	private int int_61;

	private string string_9;

	private int int_62;

	private DateTime dateTime_1;

	public int TrungCapPhuHon_PhucCuu;

	public int TrungCapPhuHon_HapHon;

	public int TrungCapPhuHon_KyDuyen;

	public int TrungCapPhuHon_PhanNo;

	public int TrungCapPhuHon_DiTinh;

	public int TrungCapPhuHon_HoThe;

	public int TrungCapPhuHon_HonNguyen;

	public int TrungCapPhuHon_HonNguyen_Giap;

	public int _FLD_HonorID;

	public int _VoLamHonorID_;

	public bool _PlayerTuVong;

	private int int_63;

	private bool bool_2;

	private bool bool_3;

	private int int_64;

	private string string_10;

	private double double_173;

	private double double_173_2;

	private double double_173_3;

	private double double_174;

	private double double_175;

	private double double_176;

	private double double_177;

	private double double_178;

	private double _FLD_HieuUngGiamDef_PK = 0.0;

	private double _FLD_CamSuHieuUngGiamDef_PK = 0.0;

	private double _FLD_CamSuHieuUngGiamDame_PK = 0.0;

	private int int_65;

	private int int_66;

	private int int_67;

	private int int_68;

	private int int_69;

	private double double_179;

	private int int_70;

	private int int_71;

	private int int_72;

	private int int_73;

	private DateTime dateTime_2;

	private DateTime dateTime_3;

	private double double_180;

	private int int_74;

	private int int_75;

	private int int_76;

	private int int_77;

	private double double_181;

	private double double_181_HP_MP;

	private int int_31_TTTP_1022;

	private int int_31_TTTP_1023;

	private int int_31_TTTP_1027;

	private int int_31_TTTP_1028;

	private int int_31_TTTP_1480;

	private int int_35_TTTP_1022;

	private int int_35_TTTP_1023;

	private int int_35_TTTP_1027;

	private int int_35_TTTP_1028;

	private int int_35_TTTP_1480;

	private double double_173_TTTP_1022;

	private double double_173_TTTP_1023;

	private double double_173_TTTP_1027;

	private double double_173_TTTP_1028;

	private double double_173_TTTP_1480;

	private double double_HoanVuVanHoa_PhanTram_Def;

	private double double_174_TTTP_1022;

	private double double_174_TTTP_1023;

	private double double_174_TTTP_1027;

	private double double_174_TTTP_1028;

	private double double_174_TTTP_1480;

	private double double_181_TTTP_1022;

	private double double_181_TTTP_1023;

	private double double_181_TTTP_1027;

	private double double_181_TTTP_1028;

	private double double_181_TTTP_1480;

	private double double_182_TTTP_1022;

	private double double_182_TTTP_1023;

	private double double_182_TTTP_1027;

	private double double_182_TTTP_1028;

	private double double_182_TTTP_1480;

	private double double_182;

	private double double_182_HP_MP;

	private double double_183;

	private double double_184;

	private int int_78;

	private int int_78_1480;

	private int int_78_HP_MP;

	private int int_79;

	private int int_79_1480;

	private int int_79_HP_MP;

	private int int_80;

	private int int_81;

	private int int_82;

	private int int_82_TTTP_1480;

	private double double_185;

	private double double_186;

	private double double_186_Gold;

	private double double_187;

	private double double_188;

	private int int_83;

	private int int_84;

	private int int_85;

	private int int_86;

	private int int_87;

	private int int_88;

	private double double_189;

	private double double_190;

	private double double_191;

	private double double_192;

	private double double_193;

	private double double_194;

	private double double_195;

	private double double_196;

	private double double_197;

	private double double_198;

	private double double_198_HatNgoc;

	private int int_89;

	private int int_90;

	private int int_91;

	private int int_92;

	private int int_93;

	private int int_94;

	private double double_199;

	private double double_200;

	private double double_201;

	private int int_95;

	private double double_202;

	private double double_203;

	private int int_96;

	private double double_204;

	private double double_205;

	private int int_97;

	private int int_98;

	private double double_206;

	private double double_207;

	private double double_208;

	private double double_209;

	private double double_210;

	private double double_211;

	private double double_212;

	private double double_213;

	private double double_214;

	private double double_215;

	private double double_216;

	private double double_217;

	private double double_218;

	private double double_219;

	private double double_2190;

	private double double_2191;

	private double double_2192;

	private double double_2193;

	private double double_2194;

	private double double_2195;

	private double double_2196;

	private double double_2197;

	private double double_2198;

	private double double_2199;

	private double double_2200;

	private double double_2201;

	private double double_2202;

	private double double_2203;

	private double double_2204;

	private double double_2205;

	private double double_2206;

	private double double_2207;

	private double double_2208;

	private double double_2209;

	private double double_2210;

	private double double_2211;

	private double double_2212;

	private double double_21900;

	private double double_21910;

	private double double_21920;

	private double double_21930;

	private double double_21940;

	private double double_21950;

	private double double_21960;

	private double double_21970;

	private double double_21980;

	private double double_21990;

	private double double_22000;

	private double double_22010;

	private double double_22020;

	private double double_22030;

	private double double_22040;

	private double double_22050;

	private double double_22060;

	private double double_22070;

	private double double_22080;

	private double double_22090;

	private double double_22100;

	private double double_22110;

	private double double_22120;

	private int int_99;

	private int int_100;

	private int int_101;

	private int int_102;

	private double double_220;

	private double double_221;

	private double double_222;

	private double double_223;

	private double double_224;

	private double double_225;

	private double double_226;

	private double double_227;

	private double double_228;

	private double double_229;

	private double double_230;

	private double double_231;

	private double double_232;

	private double double_233;

	private double double_234;

	private double double_235;

	private double double_236;

	private double double_237;

	private double double_238;

	private double double_239;

	private double double_240;

	private double double_241;

	private double double_242;

	private double double_243;

	private double double_244;

	private double double_245;

	private double double_246;

	private double double_247;

	private double double_248;

	private double double_249;

	private double double_250;

	private double double_251;

	private double double_252;

	private double double_253;

	private double double_254;

	private double double_255;

	private double double_256;

	private double double_257;

	private double double_258;

	private double double_259;

	private double double_260;

	private double double_261;

	private double double_262;

	private double double_263;

	private double double_274;

	private double double_275;

	private double double_277;

	private double double_2777;

	private double double_27777;

	private int int_103;

	private int int_104;

	private int int_105;

	private int int_106;

	private int int_107;

	private int int_499;

	public int VuKhi_LucCongKich;

	public int YPhuc_LucPhongNgu;

	public double VuKhiTyLePhanTram_CongKichVoCong;

	public double YPhuc_LucPhongNguVoCong_TiLePhanTram;

	public int FLD_loveDegreeLevel;

	public int _BangPhai_DoCongHien;

	private int int_108;

	public string _FLD_CoupleRing;

	public double _FLD_DuocPham_GiamBotCongKich;

	public double _FLD_DuocPham_GiamBotPhongNgu;

	public int _addHPForTheTitleOfRose;

	public int _roseTitleAdditionalDefense;

	public int _additionalAttackOnTheTitleOfRose;

	public int _addedHPToTheTitleOfMartialArt;

	public int _theTitleOfMartialArtAddsDefense;

	public int _additionalAttackOnTheTitleOfSect;

	public int _addHpToTitle;

	public int _titleAdditionalDefense;

	public int _titleAdditionalAttack;

	public int _NhanVatNeTranhSkill;

	public int _NhanVatThemVaoNeTranhSkill;

	public int _characterAdditionalCombatPower;

	public int _charactersAddMartialArtsPower;

	public double _FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat;

	public int _FLD_CongKichThapNhat;

	public double _FLD_NhanVat_ThemVao_LucPhongNguVoCong;

	public double _FLD_ThanThu_ThemVao_PhanTramKinhNghiem;

	private double _FLD_NhanVat_ThemVao_KinhNghiem_Party;

	private double _FLD_NhanVat_ThemVao_KinhNghiem_KetHon;

	public double _FLD_ThanThu_ThemVao_PhanTramTraiNghiem;

	public int _FLD_TrangBi_ThemVao_VoCongTrungDich;

	public int _FLD_TrangBi_ThemVao_VoCongNeTranh;

	public double _FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh;

	public int _GiamMienDoiPhuongTonThuong;

	public double _Nhan_GiamNeTranh_DoiPhuong;

	public double _DayChuyen_GiamChinhXac_DoiPhuong;

	public int _Doi_Phuong_Bi_Thuong_Giam;

	public int _FLD_TrangBi_GiaTang_DiThuongTrangThai;

	public int _FLD_TrangBi_GiaTangDoiPhuong_DiThuong;

	public int _FLD_ThanNu_ThemVao_CongKich;

	public int _FLD_ThanNu_ThemVao_PhongNgu;

	private int _numberofrebirths;

	public int _set_item;

	private int int_109;

	private int int_110;

	private int int_111;

	private int int_THQT_Dame_Def;

	private int int_THQT_HP_MP;

	private int int_HNCP_Dame_Def;

	private int int_MNCP_Dame_Def;

	private int int_CCLT_ThemVao_def;

	private double int_CPVP_Dame;

	private double int_CPVP_Def;

	private double int_1117979;

	private double int_1118080;

	private int int_112;

	private int int_113;

	private int int_114;

	private int int_115;

	private int int_116;

	private int int_117;

	private int _Player_HP_Guild;

	private int _Player_MP_Guild;

	private int _Player_TanCong_Guild;

	private int _Player_PhongThu_Guild;

	private double _Player_CLVC_Guild;

	private int _Player_ULPT_Guild;

	private int _Player_KhiCong_Guild;

	private int _Player_NeTranh_Guild;

	private int _Player_ChinhXac_Guild;

	private int _Player_CLVC_Mau_ST;

	private int _Player_ULPT_Sam_ST;

	private double _Player_CLVC_VoHuan;

	private int _Player_ULPT_VoHuan;

	private int int_54_1;

	private int int_54_2;

	private double double_89898;

	private double double_SieuAmThanh;

	private int _extra_money;

	public int OnOffChatAll = 1;

	public string firstusername = "";

	public int firstglobalid = 0;

	public int dem = 0;


	public double FLD_DoThanThemVao_PhanTramTanCong
	{
		get
		{
			return double_89898;
		}
		set
		{
			double_89898 = value;
		}
	}

	public double FLD_CamSu_HieuUng_SieuAmThanh_DameDef
	{
		get
		{
			return double_SieuAmThanh;
		}
		set
		{
			double_SieuAmThanh = value;
		}
	}

	public int ThongBao_CongThanh
	{
		get
		{
			return int_54_2;
		}
		set
		{
			int_54_2 = value;
		}
	}

	public string MonPhai_LienMinh_MinhChu
	{
		get
		{
			return string_9_1;
		}
		set
		{
			string_9_1 = value;
		}
	}

	public int Tuyen_Bo_BaoVay
	{
		get
		{
			return int_54_1;
		}
		set
		{
			int_54_1 = value;
		}
	}

	public int NumberOfRebirths
	{
		get
		{
			return _numberofrebirths;
		}
		set
		{
			_numberofrebirths = value;
		}
	}

	public int BanThuong_ThemVao_SinhMenh
	{
		get
		{
			return int_109;
		}
		set
		{
			int_109 = value;
		}
	}

	public int BanThuong_ThemVao_TanCong
	{
		get
		{
			return int_110;
		}
		set
		{
			int_110 = value;
		}
	}

	public int CaoCotLieuThuong_ThemVao_Def
	{
		get
		{
			return int_CCLT_ThemVao_def;
		}
		set
		{
			int_CCLT_ThemVao_def = value;
		}
	}

	public int TruongHongQuanThien_ThemVao_Dame_Def
	{
		get
		{
			return int_THQT_Dame_Def;
		}
		set
		{
			int_THQT_Dame_Def = value;
		}
	}

	public int TruongHongQuanThien_ThemVao_HP_MP
	{
		get
		{
			return int_THQT_HP_MP;
		}
		set
		{
			int_THQT_HP_MP = value;
		}
	}

	public int HongNguyetCuongPhong_ThemVao_Dame_Def
	{
		get
		{
			return int_HNCP_Dame_Def;
		}
		set
		{
			int_HNCP_Dame_Def = value;
		}
	}

	public int ManNguyetCuongPhong_ThemVao_Dame_Def
	{
		get
		{
			return int_MNCP_Dame_Def;
		}
		set
		{
			int_MNCP_Dame_Def = value;
		}
	}

	public double CPVP_ThemVao_Dame
	{
		get
		{
			return int_CPVP_Dame;
		}
		set
		{
			int_CPVP_Dame = value;
		}
	}

	public double CPVP_ThemVao_Def
	{
		get
		{
			return int_CPVP_Def;
		}
		set
		{
			int_CPVP_Def = value;
		}
	}

	public double AoGuild_ThemVao_PhanTram_CongKich
	{
		get
		{
			return int_1117979;
		}
		set
		{
			int_1117979 = value;
		}
	}

	public double AoGuild_ThemVao_PhanTram_PhongThu
	{
		get
		{
			return int_1118080;
		}
		set
		{
			int_1118080 = value;
		}
	}

	public int BanThuong_ThemVao_PhongThu
	{
		get
		{
			return int_111;
		}
		set
		{
			int_111 = value;
		}
	}

	public int BanThuong_ThemVao_NeTranh
	{
		get
		{
			return int_112;
		}
		set
		{
			int_112 = value;
		}
	}

	public int BanThuong_ThemVao_NoiCong
	{
		get
		{
			return int_113;
		}
		set
		{
			int_113 = value;
		}
	}

	public int BanThuong_ThemVao_TrungDich
	{
		get
		{
			return int_114;
		}
		set
		{
			int_114 = value;
		}
	}

	public int BanThuong_ThemVao_CLVC
	{
		get
		{
			return int_115;
		}
		set
		{
			int_115 = value;
		}
	}

	public int BanThuong_ThemVao_PTVC
	{
		get
		{
			return int_116;
		}
		set
		{
			int_116 = value;
		}
	}

	public int BanThuong_ThemVao_KC
	{
		get
		{
			return int_117;
		}
		set
		{
			int_117 = value;
		}
	}

	public string CharacterPartitionID
	{
		get
		{
			return _CharacterPartitionID;
		}
		set
		{
			_CharacterPartitionID = value;
		}
	}

	public double FLD_HieuUngGiamDef_PK
	{
		get
		{
			return _FLD_HieuUngGiamDef_PK;
		}
		set
		{
			_FLD_HieuUngGiamDef_PK = value;
		}
	}

	public double FLD_CamSuHieuUngGiamDef_PK
	{
		get
		{
			return _FLD_CamSuHieuUngGiamDef_PK;
		}
		set
		{
			_FLD_CamSuHieuUngGiamDef_PK = value;
		}
	}

	public double FLD_CamSuHieuUngGiamDame_PK
	{
		get
		{
			return _FLD_CamSuHieuUngGiamDame_PK;
		}
		set
		{
			_FLD_CamSuHieuUngGiamDame_PK = value;
		}
	}

	public int BangPhai_DoCongHien
	{
		get
		{
			return _BangPhai_DoCongHien;
		}
		set
		{
			_BangPhai_DoCongHien = value;
		}
	}

	public int Player_Whtb
	{
		get
		{
			return int_108;
		}
		set
		{
			if (value < 0)
			{
				int_108 = 0;
			}
			else
			{
				int_108 = value;
			}
		}
	}

	public int CurrentlyOperatingNPC
	{
		get
		{
			return _CurrentlyOperatingNPC;
		}
		set
		{
			_CurrentlyOperatingNPC = value;
		}
	}

	public int CurrentOperationType
	{
		get
		{
			return _currentOperationType;
		}
		set
		{
			_currentOperationType = value;
		}
	}


	public int MartialArtsAttackPowerOfHusbandAndWife
	{
		get
		{
			if (CoupleInTeam)
			{
				return (int)(int_0 + int_0 * 0.1);
			}
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int MartialArtsAttackPowerOfHusbandAndWifeMP
	{
		get
		{
			if (CoupleInTeam)
			{
				return (int)(_VoChong_VoCong_LucCongKich_MP + _VoChong_VoCong_LucCongKich_MP * 0.1);
			}
			return _VoChong_VoCong_LucCongKich_MP;
		}
		set
		{
			_VoChong_VoCong_LucCongKich_MP = value;
		}
	}

	public int CharacterBeastFullServiceID => SessionID * 3 + 40000;

	public int CharacterPosition
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int Character_KhinhCong
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int CamSu_TrangThai
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public int CurrentlyActiveSkill_ID
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public bool CungPhatDong_TanCongNhom
	{
		get
		{
			return bool_1;
		}
		set
		{
			bool_1 = value;
		}
	}

	public string AccountID
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public string Password
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public string Kich_Hoat_Tai_Khoan
	{
		get
		{
			return kich_hoat_tai_khoan;
		}
		set
		{
			kich_hoat_tai_khoan = value;
		}
	}

	public string lastloginip
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	public string LanIp
	{
		get
		{
			return string_3;
		}
		set
		{
			string_3 = value;
		}
	}

	public int TheLucChien_SatNhan_SoLuong
	{
		get
		{
			return int_9;
		}
		set
		{
			int_9 = value;
		}
	}

	public int TheLucChien_TuVong_SoLuong
	{
		get
		{
			return int_10;
		}
		set
		{
			int_10 = value;
		}
	}

	public string CharacterName
	{
		get
		{
			return string_4;
		}
		set
		{
			string_4 = value;
		}
	}

	public int GuildId
	{
		get
		{
			return int_11;
		}
		set
		{
			int_11 = value;
		}
	}

	public string GuildName
	{
		get
		{
			return string_5;
		}
		set
		{
			string_5 = value;
		}
	}

	public int GangLevel
	{
		get
		{
			return int_12;
		}
		set
		{
			int_12 = value;
		}
	}

	public int GangCharacterLevel
	{
		get
		{
			return int_13;
		}
		set
		{
			int_13 = value;
		}
	}

	public int GangServiceWords
	{
		get
		{
			return int_14;
		}
		set
		{
			int_14 = value;
		}
	}

	public byte[] GangBadge
	{
		get
		{
			return _HuyHieuBang;
		}
		set
		{
			_HuyHieuBang = value;
		}
	}

	public int GangDoorClothesColor
	{
		get
		{
			return int_15;
		}
		set
		{
			int_15 = value;
		}
	}

	public int SkillExperience
	{
		get
		{
			return int_16;
		}
		set
		{
			int_16 = value;
		}
	}

	public long CharacterExperience
	{
		get
		{
			return long_0;
		}
		set
		{
			long_0 = value;
		}
	}

	public long CharacterGreatestExperience
	{
		get
		{
			return long_1;
		}
		set
		{
			long_1 = value;
		}
	}

	public long Player_Money
	{
		get
		{
			return long_2;
		}
		set
		{
			if (value < 0)
			{
				long_2 = 0L;
			}
			else
			{
				long_2 = value;
			}
		}
	}

	public int ThuongHaDieu_DemSo
	{
		get
		{
			return int_17;
		}
		set
		{
			int_17 = value;
		}
	}

	public int LowerRiverMonitor
	{
		get
		{
			return int_18;
		}
		set
		{
			int_18 = value;
		}
	}

	public int NgocLienHoan_DemSo
	{
		get
		{
			return int_19;
		}
		set
		{
			int_19 = value;
		}
	}

	public int NhanVat_HP
	{
		get
		{
			return int_20;
		}
		set
		{
			int_20 = value;
		}
	}

	public int NhanVat_WX_BUFF_SinhMenh
	{
		get
		{
			return int_21;
		}
		set
		{
			int_21 = value;
		}
	}

	public int NhanVat_WX_BUFF_CongKich
	{
		get
		{
			return int_22;
		}
		set
		{
			int_22 = value;
		}
	}

	public int NhanVat_WX_BUFF_PhongNgu
	{
		get
		{
			return int_23;
		}
		set
		{
			int_23 = value;
		}
	}

	public int NhanVat_WX_BUFF_NeTranh
	{
		get
		{
			return int_24;
		}
		set
		{
			int_24 = value;
		}
	}

	public int NhanVat_WX_BUFF_NoiCong
	{
		get
		{
			return int_25;
		}
		set
		{
			int_25 = value;
		}
	}

	public int NhanVat_WX_BUFF_TrungDich
	{
		get
		{
			return int_26;
		}
		set
		{
			int_26 = value;
		}
	}

	public int NhanVat_WX_BUFF_KhiCong
	{
		get
		{
			return int_27;
		}
		set
		{
			int_27 = value;
		}
	}

	public int FLD_NhanVatCoBan_CongKich
	{
		get
		{
			if (FLD_ThemVaoTiLePhanTram_CongKich < 1E-05)
			{
				FLD_ThemVaoTiLePhanTram_CongKich = 0.0;
			}
			return (int)((FLD_CongKich + FLD_TrangBi_ThemVao_CongKich + FLD_NhanVat_ThemVao_CongKich + FLD_NhanVat_ThemVao_CongKich_1480 + FLD_NhanVat_ThemVao_CongKich_HP_MP + HongNguyetCuongPhong_ThemVao_Dame_Def + TruongHongQuanThien_ThemVao_Dame_Def + ManNguyetCuongPhong_ThemVao_Dame_Def + (int)KCTT_16x_All_TinhKimBachLuyen) * (1.0 + CPVP_ThemVao_Dame + AoGuild_ThemVao_PhanTram_CongKich + FLD_ThemVaoTiLePhanTram_CongKich + FLD_TrangBi_ThemVao_TiLePhanTram_CongKich + FLD_DoThanThemVao_PhanTramTanCong + FLD_CamSu_HieuUng_SieuAmThanh_DameDef + FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022 + FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023 + FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027 + FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028 + FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480 + FLD_DuocPham_GiamBotCongKich - FLD_CamSuHieuUngGiamDame_PK) + NhanVat_WX_BUFF_CongKich + FLD_Pet_ThemVao_CongKich + FLD_NhanVat_KhiCong_CongKich) + TitleAddedAttack + RoseTitleAddAttack + BangPhaiTitleAddedAttack + BanThuong_ThemVao_TanCong + Player_TanCong_Guild + DCH_ATTACK_BONUS;
		}
	}

	public int FLD_NhanVatCoBan_PhongNgu
	{
		get
		{
			if (FLD_ThemVaoTiLePhanTram_PhongNgu < 0.0001)
			{
				FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
			}
			return (int)((FLD_PhongNgu + FLD_TrangBi_ThemVao_PhongNgu + FLD_NhanVat_ThemVao_PhongNgu + FLD_NhanVat_ThemVao_PhongNgu_1480 + FLD_NhanVat_ThemVao_PhongNgu_HP_MP + HongNguyetCuongPhong_ThemVao_Dame_Def + TruongHongQuanThien_ThemVao_Dame_Def + ManNguyetCuongPhong_ThemVao_Dame_Def + CaoCotLieuThuong_ThemVao_Def + NhanVat_WX_BUFF_PhongNgu + FLD_Pet_ThemVao_PhongNgu + FLD_NhanVat_KhiCong_PhongNgu + TitleAddedDefense + RoseTitleAddDefense + BangPhaiTitleAddedDefense + BanThuong_ThemVao_PhongThu + Player_PhongThu_Guild +  DCH_DEF_BONUS) * (1.0 + FLD_ThemVao_PhanTram_Def_HoanVuVanHoa + CPVP_ThemVao_Def + AoGuild_ThemVao_PhanTram_PhongThu + FLD_ThemVaoTiLePhanTram_PhongNgu + FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu + FLD_CamSu_HieuUng_SieuAmThanh_DameDef + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480 + FLD_DuocPham_GiamBotPhongNgu - FLD_HieuUngGiamDef_PK - FLD_CamSuHieuUngGiamDef_PK));
		}
	}

	public int NhanVatCuongKhi
	{
		get
		{
			return (int)FLD_CuongKhi_TrangBi + (int)KCTT_16x_All_HuyetKhiPhuongCuong + Pet_CuongKhi;
		}
	}

	public int FLD_NhanVatCoBan_PhongNguNew
	{
		get
		{
			if (FLD_ThemVaoTiLePhanTram_PhongNgu < 0.0001)
			{
				FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
			}
			return (int)((FLD_PhongNgu + FLD_TrangBi_ThemVao_PhongNguNew + FLD_NhanVat_ThemVao_PhongNgu + FLD_NhanVat_ThemVao_PhongNgu_1480 + FLD_NhanVat_ThemVao_PhongNgu_HP_MP + HongNguyetCuongPhong_ThemVao_Dame_Def + TruongHongQuanThien_ThemVao_Dame_Def + ManNguyetCuongPhong_ThemVao_Dame_Def + CaoCotLieuThuong_ThemVao_Def + NhanVat_WX_BUFF_PhongNgu + FLD_Pet_ThemVao_PhongNgu + FLD_NhanVat_KhiCong_PhongNgu + TitleAddedDefense + RoseTitleAddDefense + BangPhaiTitleAddedDefense + BanThuong_ThemVao_PhongThu + Player_PhongThu_Guild + (int)KCTT_16x_All_HuyetKhiPhuongCuong + DCH_DEF_BONUS) * (1.0 + FLD_ThemVao_PhanTram_Def_HoanVuVanHoa + CPVP_ThemVao_Def + AoGuild_ThemVao_PhanTram_PhongThu + FLD_ThemVaoTiLePhanTram_PhongNgu + FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu + FLD_CamSu_HieuUng_SieuAmThanh_DameDef + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028 + FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480 + FLD_DuocPham_GiamBotPhongNgu - FLD_HieuUngGiamDef_PK - FLD_CamSuHieuUngGiamDef_PK));
		}
	}

	public int FLD_NhanVatCoBan_TrungDich
	{
		get
		{
			if (FLD_ThemVaoTiLePhanTram_TrungDich < 0.0001)
			{
				FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
			}
			if (FLD_TrangBi_ThemVao_TrungDichTiLePhanTram < 0.0001)
			{
				FLD_TrangBi_ThemVao_TrungDichTiLePhanTram = 0.0;
			}
			return (int)((FLD_TrungDich + FLD_TrangBi_ThemVao_TrungDich + FLD_NhanVat_ThemVao_TrungDich + FLD_NhanVat_KhiCong_TrungDich + Player_ChinhXac_Guild) * (1.0 + FLD_ThemVaoTiLePhanTram_TrungDich + FLD_TrangBi_ThemVao_TrungDichTiLePhanTram)) + NhanVat_WX_BUFF_TrungDich + BanThuong_ThemVao_TrungDich;
		}
	}

	public int FLD_NhanVatCoBan_NeTranh
	{
		get
		{
			if (FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh < 0.0001)
			{
				FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
			}
			if (FLD_TrangBi_ThemVao_NeTranhTiLePhanTram < 0.0001)
			{
				FLD_TrangBi_ThemVao_NeTranhTiLePhanTram = 0.0;
			}
			return (int)((FLD_NeTranh + FLD_TrangBi_ThemVao_NeTranh + FLD_NhanVat_ThemVao_NeTranh + FLD_NhanVat_KhiCong_NeTranh + Player_NeTranh_Guild) * (1.0 + FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh + FLD_TrangBi_ThemVao_NeTranhTiLePhanTram)) + NhanVat_WX_BUFF_NeTranh + BanThuong_ThemVao_NeTranh;
		}
	}

	public int CharacterMax_HP
	{
		get
		{
			if (FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0001)
			{
				FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
			}
			return (int)((CharacterIsBasicallyTheLargest_HP + FLD_TrangBi_ThemVao_HP + TTTP_Tang_HP + TTTP_Tang_HP_NEW + TheLucChien_Tang_HP_TayNgan + TheLucChien_Tang_HP_TayDai + NhanVat_KhiCong_ThemVao_HP + NhanVat_KhiCong_ThemVao_PhanTram_HP + CharactersToAddMax_HP + CharactersToAddMax_HP_TTTP_1022 + CharactersToAddMax_HP_TTTP_1023 + CharactersToAddMax_HP_TTTP_1027 + CharactersToAddMax_HP_TTTP_1028 + CharactersToAddMax_HP_TTTP_1480 + TruongHongQuanThien_ThemVao_HP_MP + Player_HP_Guild) * (1.0 + FLD_ThemVaoTiLePhanTram_HPCaoNhat + FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat)) + NhanVat_WX_BUFF_SinhMenh + FLD_Pet_ThemVao_LonNhatHP + TitleAddedHP + RoseTitleAddHP + BangPhaiTitleAddedHP + BanThuong_ThemVao_SinhMenh + DCH_HP_BONUS;
		}
	}

	public int CharacterMax_MP
	{
		get
		{
			if (FLD_ThemVaoTiLePhanTram_MPCaoNhat < 0.0001)
			{
				FLD_ThemVaoTiLePhanTram_MPCaoNhat = 0.0;
			}
			return (int)((CharacterIsBasicallyTheLargest_MP + FLD_TrangBi_ThemVao_MP + TTTP_Tang_MP + TTTP_Tang_MP_NEW + NhanVat_KhiCong_ThemVao_MP + NhanVat_KhiCong_ThemVao_PhanTram_MP + CharactersToAddMax_MP + CharactersToAddMax_MP_TTTP_1022 + CharactersToAddMax_MP_TTTP_1023 + CharactersToAddMax_MP_TTTP_1027 + CharactersToAddMax_MP_TTTP_1028 + CharactersToAddMax_MP_TTTP_1480 + TruongHongQuanThien_ThemVao_HP_MP + Player_MP_Guild) * (1.0 + FLD_ThemVaoTiLePhanTram_MPCaoNhat)) + NhanVat_WX_BUFF_NoiCong + BanThuong_ThemVao_NoiCong;
		}
	}

	public int CharacterMax_AP => CharacterIsBasicallyTheLargest_Barrier + FLD_TrangBi_ThemVao_LaChan;

	public int NhanVat_LonNhat_VoCong_PhongNgu
	{
		get
		{
			if (FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0001)
			{
				FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
			}
			return (int)((1.0 + FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram + FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram + FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram_HP_MP + FLD_NhanVat_KhiCong_VoCong_LucPhongNgu_GiaTangTiLePhanTram + FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1022 + FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1023 + FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1027 + FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1028 + FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1480 + DCH_ULPT_BONUS) * (FLD_TrangBi_LucPhongNguVoCong + NhanVat_KhiCong_ThemVao_LucPhongNguVoCong + FLD_NhanVat_ThemVao_LucPhongNguVoCong + BanThuong_ThemVao_PTVC + Player_ULPT_Guild + Player_ULPT_VoHuan));
		}
	}

	public double TotalSkillDamage
	{
		get
		{
			if (FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0001)
			{
				FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
			}
			return 1.0 + FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram + FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1022 + FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1023 + FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1027 + FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1028 + FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1480 + FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram + FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram_HP_MP + FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram + World.TyLePhanTram_CongKichVoCong + DCH_CLVC_BONUS + Player_CLVC_Guild + Player_CLVC_VoHuan;
		}
	}

	public int FLD_NhanVat_ThemVao_TanCong_QuaiVat
	{
		get
		{
			if (FLD_TrangBi_ThemVao_DoiQuai_CongKich < 0)
			{
				FLD_TrangBi_ThemVao_DoiQuai_CongKich = 0;
			}
			return (int)((1.0 + FLD_TrangBi_ThuocTinhHoa_ThemVao_TangDame_QuaiVat) * (FLD_TrangBi_ThemVao_DoiQuai_CongKich + DuocPham_ThemVao_DoiQuai_CongKich + DuocPham_ThemVao_DoiQuai_CongKich_NEW + DuocPham_TuHaThanDan_ThemVao_DoiQuai_CongKich));
		}
	}

	public int FLD_NhanVat_ThemVao_PhongThu_QuaiVat
	{
		get
		{
			if (FLD_TrangBi_ThemVao_DoiQuai_PhongNgu < 0)
			{
				FLD_TrangBi_ThemVao_DoiQuai_PhongNgu = 0;
			}
			return FLD_TrangBi_ThemVao_DoiQuai_PhongNgu + FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu + DuocPham_ThemVao_DoiQuai_PhongNgu + DuocPham_ThemVao_DoiQuai_PhongNgu_NEW + DuocPham_TuHaThanDan_ThemVao_DoiQuai_PhongNgu + (int)ThangThien_5_MaHonChiLuc;
		}
	}

	public int MagicEvade
	{
		get
		{
			if (FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh < 0.0001)
			{
				FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = 0.0;
			}
			return (int)((1.0 + FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh) * (FLD_TrangBi_ThemVao_VoCongNeTranh + NhanVat_ThemVao_NeTranhSkill));
		}
	}

	public double FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh
	{
		get
		{
			return _FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh;
		}
		set
		{
			_FLD_TrangBi_ThemVao_PhanTram_VoCongNeTranh = value;
		}
	}

	public double NhanVat_ThemVao_PhanTramKinhNghiem => FLD_NhanVat_ThemVao_PhanTramKinhNghiem;

	public double NhanVat_ThemVao_KinhNghiem_CuoiTuan => FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan;

	public double NhanVat_ThemVao_KinhNghiem_Bonus => FLD_NhanVat_ThemVao_KinhNghiem_Bonus;

	public double NhanVat_ThemVao_KinhNghiem_TanThu => FLD_NhanVat_ThemVao_KinhNghiem_TanThu;

	public double NhanVat_ThemVao_Exp_CTP => FLD_NhanVat_ThemVao_Exp_CTP;

	public double NhanVat_ThemVao_KinhNghiem_TheLucChien => FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien;

	public double NhanVat_ThemVao_KinhNghiem_Party => FLD_NhanVat_ThemVao_KinhNghiem_Party;

	public double NhanVat_ThemVao_KinhNghiem_KetHon => FLD_NhanVat_ThemVao_KinhNghiem_KetHon;

	public int NhanVat_KhiCong_ThemVao_HP
	{
		get
		{
			return int_28;
		}
		set
		{
			int_28 = value;
		}
	}

	public int NhanVat_KhiCong_ThemVao_PhanTram_HP
	{
		get
		{
			return int_28_PhanTram_HP;
		}
		set
		{
			int_28_PhanTram_HP = value;
		}
	}

	public int NhanVat_KhiCong_ThemVao_PhanTram_MP
	{
		get
		{
			return int_28_PhanTram_MP;
		}
		set
		{
			int_28_PhanTram_MP = value;
		}
	}

	public int NhanVat_KhiCong_ThemVao_LucPhongNguVoCong
	{
		get
		{
			return int_29;
		}
		set
		{
			int_29 = value;
		}
	}

	public int CharacterIsBasicallyTheLargest_HP
	{
		get
		{
			return int_30;
		}
		set
		{
			int_30 = value;
		}
	}

	public int CharactersToAddMax_HP
	{
		get
		{
			return int_31;
		}
		set
		{
			int_31 = value;
		}
	}

	public int CharactersToAddMax_HP_TTTP_1022
	{
		get
		{
			return int_31_TTTP_1022;
		}
		set
		{
			int_31_TTTP_1022 = value;
		}
	}

	public int CharactersToAddMax_HP_TTTP_1023
	{
		get
		{
			return int_31_TTTP_1023;
		}
		set
		{
			int_31_TTTP_1023 = value;
		}
	}

	public int CharactersToAddMax_HP_TTTP_1027
	{
		get
		{
			return int_31_TTTP_1027;
		}
		set
		{
			int_31_TTTP_1027 = value;
		}
	}

	public int CharactersToAddMax_HP_TTTP_1480
	{
		get
		{
			return int_31_TTTP_1480;
		}
		set
		{
			int_31_TTTP_1480 = value;
		}
	}

	public int CharactersToAddMax_HP_TTTP_1028
	{
		get
		{
			return int_31_TTTP_1028;
		}
		set
		{
			int_31_TTTP_1028 = value;
		}
	}

	public int NhanVat_MP
	{
		get
		{
			return int_32;
		}
		set
		{
			int_32 = value;
		}
	}

	public int NhanVat_AP
	{
		get
		{
			return int_33;
		}
		set
		{
			int_33 = value;
		}
	}

	public int CharacterIsBasicallyTheLargest_MP
	{
		get
		{
			return int_34;
		}
		set
		{
			int_34 = value;
		}
	}

	public int CharactersToAddMax_MP
	{
		get
		{
			return int_35;
		}
		set
		{
			int_35 = value;
		}
	}

	public int CharactersToAddMax_MP_TTTP_1022
	{
		get
		{
			return int_35_TTTP_1022;
		}
		set
		{
			int_35_TTTP_1022 = value;
		}
	}

	public int CharactersToAddMax_MP_TTTP_1023
	{
		get
		{
			return int_35_TTTP_1023;
		}
		set
		{
			int_35_TTTP_1023 = value;
		}
	}

	public int CharactersToAddMax_MP_TTTP_1027
	{
		get
		{
			return int_35_TTTP_1027;
		}
		set
		{
			int_35_TTTP_1027 = value;
		}
	}

	public int CharactersToAddMax_MP_TTTP_1028
	{
		get
		{
			return int_35_TTTP_1028;
		}
		set
		{
			int_35_TTTP_1028 = value;
		}
	}

	public int CharactersToAddMax_MP_TTTP_1480
	{
		get
		{
			return int_35_TTTP_1480;
		}
		set
		{
			int_35_TTTP_1480 = value;
		}
	}

	public int NhanVat_KhiCong_ThemVao_MP
	{
		get
		{
			return int_36;
		}
		set
		{
			int_36 = value;
		}
	}

	public int CharacterIsBasicallyTheLargest_Barrier
	{
		get
		{
			return int_37;
		}
		set
		{
			int_37 = value;
		}
	}

	public int NhanVat_SP
	{
		get
		{
			return int_38;
		}
		set
		{
			int_38 = value;
		}
	}

	public int CharacterMax_SP
	{
		get
		{
			return int_39;
		}
		set
		{
			int_39 = value;
		}
	}

	public int Player_Qigong_point
	{
		get
		{
			return int_40;
		}
		set
		{
			int_40 = value;
		}
	}

	public int Player_Anti_Qigong_point { get; set; }

	public int Player_Zx
	{
		get
		{
			return int_41;
		}
		set
		{
			int_41 = value;
		}
	}

	public int Player_Level
	{
		get
		{
			return int_42;
		}
		set
		{
			int_42 = value;
		}
	}

	public int Player_WuXun
	{
		get
		{
			return int_43;
		}
		set
		{
			int_43 = value;
		}
	}

	public int VoHuanGiaiDoan
	{
		get
		{
			return int_44;
		}
		set
		{
			int_44 = value;
		}
	}

	public int NhanVatThienVaAc
	{
		get
		{
			return int_45;
		}
		set
		{
			if (int_45 + value > 0)
			{
				int_45 = 0;
			}
			else if (int_45 - value < -30000)
			{
				int_45 = -30000;
			}
			else
			{
				int_45 = value;
			}
		}
	}

	public int Player_Extra_Money_Level
	{
		get
		{
			return _extra_money;
		}
		set
		{
			_extra_money = value;
		}
	}

	public int Player_Job
	{
		get
		{
			return int_46;
		}
		set
		{
			int_46 = value;
		}
	}

	public int Player_Job_level
	{
		get
		{
			return int_47;
		}
		set
		{
			int_47 = value;
		}
	}

	public byte[] CharacterNameTemplate
	{
		get
		{
			return _CharacterNameTemplate;
		}
		set
		{
			_CharacterNameTemplate = value;
		}
	}

	public int Player_Sex
	{
		get
		{
			return int_48;
		}
		set
		{
			int_48 = value;
		}
	}

	public float PosX
	{
		get
		{
			return float_0;
		}
		set
		{
			float_0 = value;
		}
	}

	public float PosY
	{
		get
		{
			return float_1;
		}
		set
		{
			float_1 = value;
		}
	}

	public float PosZ
	{
		get
		{
			return float_2;
		}
		set
		{
			float_2 = value;
		}
	}

	public int MapID
	{
		get
		{
			return int_49;
		}
		set
		{
			int_49 = value;
		}
	}

	public int CharacterCurrentWeight
	{
		get
		{
			return int_50;
		}
		set
		{
			int_50 = value;
		}
	}

	public int TheTotalWeightOfTheCharacter
	{
		get
		{
			return int_51 + FLD_NhanVat_KhiCong_TrongLuong;
		}
		set
		{
			int_51 = value;
		}
	}

	public int CharacterPacketID
	{
		get
		{
			return _CharacterPacketID;
		}
		set
		{
			_CharacterPacketID = value;
		}
	}

	public string OriginalServerIP
	{
		get
		{
			return string_7;
		}
		set
		{
			string_7 = value;
		}
	}

	public int OriginalServerPort
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int OriginalServerSerialNumber
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int OriginalServerID
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public string SilverCoinSquareServerIP
	{
		get
		{
			return string_8;
		}
		set
		{
			string_8 = value;
		}
	}

	public int SilverCoinSquareServerID
	{
		get
		{
			return int_499;
		}
		set
		{
			int_499 = World.SilverCoinSquareServerID;
		}
	}

	public int SilverCoinSquareServerPort
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = World.SilverCoinSquareServerPort;
		}
	}

//	public int SessionID => Client.PlayerSessionID;

	public int SessionID { get; set; }

	public int CharacterPKMode
	{
		get
		{
			return int_52;
		}
		set
		{
			int_52 = value;
		}
	}

	public long PersonalWarehouseMoney
	{
		get
		{
			return long_3;
		}
		set
		{
			long_3 = value;
		}
	}

	public long ComprehensiveWarehouseMoney
	{
		get
		{
			return long_4;
		}
		set
		{
			long_4 = value;
		}
	}

	public int FLD_RXPIONT
	{
		get
		{
			return int_53;
		}
		set
		{
			int_53 = value;
		}
	}

	public int FLD_RXPIONTX
	{
		get
		{
			return int_54;
		}
		set
		{
			int_54 = value;
		}
	}

	public int 副本剩余次数
	{
		get
		{
			return int_1199;
		}
		set
		{
			int_1199 = value;
		}
	}

	public int ThaoPhat_TinhGop_TonThuong
	{
		get
		{
			return int_1200;
		}
		set
		{
			int_1200 = value;
		}
	}

	public int FLD_Coin
	{
		get
		{
			return int_55;
		}
		set
		{
			int_55 = value;
		}
	}

	public int Rxjh_WX
	{
		get
		{
			return int_56;
		}
		set
		{
			int_56 = value;
		}
	}

	public int FLD_VIP
	{
		get
		{
			return int_57;
		}
		set
		{
			int_57 = value;
		}
	}

	public DateTime FLD_VIPTIM
	{
		get
		{
			return dateTime_0;
		}
		set
		{
			dateTime_0 = value;
		}
	}

	public int FLD_CongKichTocDo
	{
		get
		{
			return int_58;
		}
		set
		{
			int_58 = value;
		}
	}

	public int FLD_LoaiSanXuat
	{
		get
		{
			return int_59;
		}
		set
		{
			int_59 = value;
		}
	}

	public int FLD_LevelSanXuat
	{
		get
		{
			return int_60;
		}
		set
		{
			int_60 = value;
		}
	}

	public int FLD_TrinhDoSanXuat
	{
		get
		{
			return int_61;
		}
		set
		{
			if (value >= 1500)
			{
				int_61 = 1500;
			}
			else
			{
				int_61 = value;
			}
		}
	}

	public string FLD_Couple
	{
		get
		{
			return string_9;
		}
		set
		{
			string_9 = value;
		}
	}

	public string FLD_CoupleRing
	{
		get
		{
			return _FLD_CoupleRing;
		}
		set
		{
			_FLD_CoupleRing = value;
		}
	}

	public int FLD_Couple_Love
	{
		get
		{
			return int_62;
		}
		set
		{
			int_62 = value;
		}
	}

	public string FLD_TemporaryMasters
	{
		get
		{
			return string_6;
		}
		set
		{
			string_6 = value;
		}
	}

	public bool CanFormAStateOfMaster_DiscipleRelationShip
	{
		get
		{
			return bool_0;
		}
		set
		{
			bool_0 = value;
		}
	}

	public DateTime GiaiTru_QuanHeThayTro_ThoiGian
	{
		get
		{
			return dateTime_1;
		}
		set
		{
			dateTime_1 = value;
		}
	}

	public int FLD_HonorID
	{
		get
		{
			return _FLD_HonorID;
		}
		set
		{
			_FLD_HonorID = value;
		}
	}

	public int HonorID_
	{
		get
		{
			return _VoLamHonorID_;
		}
		set
		{
			_VoLamHonorID_ = value;
		}
	}

	public bool PlayerTuVong
	{
		get
		{
			if (NhanVat_HP <= 0)
			{
				_PlayerTuVong = true;
			}
			return _PlayerTuVong;
		}
		set
		{
			_PlayerTuVong = value;
		}
	}

	public string TheLucChien_PhePhai
	{
		get
		{
			return _TheLucChienPhePhai;
		}
		set
		{
			_TheLucChienPhePhai = value;
		}
	}

	public string DCH_PhePhai
	{
		get
		{
			return _DCHPhePhai;
		}
		set
		{
			_DCHPhePhai = value;
		}
	}

	public int FLD_PVP_Piont
	{
		get
		{
			return int_63;
		}
		set
		{
			int_63 = value;
		}
	}

	public bool NguyenBao_TaiKhoanTrangThai
	{
		get
		{
			return bool_2;
		}
		set
		{
			bool_2 = value;
		}
	}

	public bool WhetherToBet
	{
		get
		{
			return bool_3;
		}
		set
		{
			bool_3 = value;
		}
	}

	public int BettingOnTheSpecialCode
	{
		get
		{
			return int_64;
		}
		set
		{
			int_64 = value;
		}
	}

	public string DatCuocVao_TyLeCuocVaTienThuong
	{
		get
		{
			return string_10;
		}
		set
		{
			string_10 = value;
		}
	}

	public double FLD_DuocPham_GiamBotCongKich
	{
		get
		{
			return _FLD_DuocPham_GiamBotCongKich;
		}
		set
		{
			_FLD_DuocPham_GiamBotCongKich = value;
		}
	}

	public double FLD_NhanVat_ThemVao_KinhNghiem_Party
	{
		get
		{
			return _FLD_NhanVat_ThemVao_KinhNghiem_Party;
		}
		set
		{
			_FLD_NhanVat_ThemVao_KinhNghiem_Party = value;
		}
	}

	public double FLD_NhanVat_ThemVao_KinhNghiem_KetHon
	{
		get
		{
			return _FLD_NhanVat_ThemVao_KinhNghiem_KetHon;
		}
		set
		{
			_FLD_NhanVat_ThemVao_KinhNghiem_KetHon = value;
		}
	}

	public double FLD_DuocPham_GiamBotPhongNgu
	{
		get
		{
			return _FLD_DuocPham_GiamBotPhongNgu;
		}
		set
		{
			_FLD_DuocPham_GiamBotPhongNgu = value;
		}
	}

	public int RoseTitleAddHP
	{
		get
		{
			return _addHPForTheTitleOfRose;
		}
		set
		{
			_addHPForTheTitleOfRose = value;
		}
	}

	public int RoseTitleAddDefense
	{
		get
		{
			return _roseTitleAdditionalDefense;
		}
		set
		{
			_roseTitleAdditionalDefense = value;
		}
	}

	public int RoseTitleAddAttack
	{
		get
		{
			return _additionalAttackOnTheTitleOfRose;
		}
		set
		{
			_additionalAttackOnTheTitleOfRose = value;
		}
	}

	public int BangPhaiTitleAddedHP
	{
		get
		{
			return _addedHPToTheTitleOfMartialArt;
		}
		set
		{
			_addedHPToTheTitleOfMartialArt = value;
		}
	}

	public int BangPhaiTitleAddedDefense
	{
		get
		{
			return _theTitleOfMartialArtAddsDefense;
		}
		set
		{
			_theTitleOfMartialArtAddsDefense = value;
		}
	}

	public int BangPhaiTitleAddedAttack
	{
		get
		{
			return _additionalAttackOnTheTitleOfSect;
		}
		set
		{
			_additionalAttackOnTheTitleOfSect = value;
		}
	}

	public int TitleAddedHP
	{
		get
		{
			return _addHpToTitle;
		}
		set
		{
			_addHpToTitle = value;
		}
	}

	public int TitleAddedDefense
	{
		get
		{
			return _titleAdditionalDefense;
		}
		set
		{
			_titleAdditionalDefense = value;
		}
	}

	public int TitleAddedAttack
	{
		get
		{
			return _titleAdditionalAttack;
		}
		set
		{
			_titleAdditionalAttack = value;
		}
	}

	public int NhanVat_ThemVao_NeTranhSkill => NhanVatNeTranhSkill + NhanVatThemVaoNeTranhSkill;

	public int NhanVatNeTranhSkill
	{
		get
		{
			return _NhanVatNeTranhSkill;
		}
		set
		{
			_NhanVatNeTranhSkill = value;
		}
	}

	public int NhanVatThemVaoNeTranhSkill
	{
		get
		{
			return _NhanVatThemVaoNeTranhSkill;
		}
		set
		{
			_NhanVatThemVaoNeTranhSkill = value;
		}
	}

	public int NhanVat_ThemVao_PVPChienLuc => CharacterAdditionalCombatPower + CharactersAddMartialArtsPower;

	public int CharacterAdditionalCombatPower
	{
		get
		{
			return _characterAdditionalCombatPower;
		}
		set
		{
			_characterAdditionalCombatPower = value;
		}
	}

	public int CharactersAddMartialArtsPower
	{
		get
		{
			return _charactersAddMartialArtsPower;
		}
		set
		{
			_charactersAddMartialArtsPower = value;
		}
	}

	public double FLD_TrangBi_ThemVao_TiLePhanTram_CongKich
	{
		get
		{
			return double_173_2;
		}
		set
		{
			double_173_2 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu
	{
		get
		{
			return double_173_3;
		}
		set
		{
			double_173_3 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich
	{
		get
		{
			return double_173;
		}
		set
		{
			double_173 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022
	{
		get
		{
			return double_173_TTTP_1022;
		}
		set
		{
			double_173_TTTP_1022 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023
	{
		get
		{
			return double_173_TTTP_1023;
		}
		set
		{
			double_173_TTTP_1023 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027
	{
		get
		{
			return double_173_TTTP_1027;
		}
		set
		{
			double_173_TTTP_1027 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028
	{
		get
		{
			return double_173_TTTP_1028;
		}
		set
		{
			double_173_TTTP_1028 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480
	{
		get
		{
			return double_173_TTTP_1480;
		}
		set
		{
			double_173_TTTP_1480 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu
	{
		get
		{
			return double_174;
		}
		set
		{
			double_174 = value;
		}
	}

	public double FLD_ThemVao_PhanTram_Def_HoanVuVanHoa
	{
		get
		{
			return double_HoanVuVanHoa_PhanTram_Def;
		}
		set
		{
			double_HoanVuVanHoa_PhanTram_Def = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022
	{
		get
		{
			return double_174_TTTP_1022;
		}
		set
		{
			double_174_TTTP_1022 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023
	{
		get
		{
			return double_174_TTTP_1023;
		}
		set
		{
			double_174_TTTP_1023 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027
	{
		get
		{
			return double_174_TTTP_1027;
		}
		set
		{
			double_174_TTTP_1027 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028
	{
		get
		{
			return double_174_TTTP_1028;
		}
		set
		{
			double_174_TTTP_1028 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480
	{
		get
		{
			return double_174_TTTP_1480;
		}
		set
		{
			double_174_TTTP_1480 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_TrungDich
	{
		get
		{
			return double_175;
		}
		set
		{
			double_175 = value;
		}
	}

	public double FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh
	{
		get
		{
			return double_176;
		}
		set
		{
			double_176 = value;
		}
	}

	public double FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat
	{
		get
		{
			return _FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat;
		}
		set
		{
			_FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_HPCaoNhat
	{
		get
		{
			return double_177;
		}
		set
		{
			double_177 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_MPCaoNhat
	{
		get
		{
			return double_178;
		}
		set
		{
			double_178 = value;
		}
	}

	public int FLD_Tam
	{
		get
		{
			return int_65;
		}
		set
		{
			int_65 = value;
		}
	}

	public int FLD_The
	{
		get
		{
			return int_66;
		}
		set
		{
			int_66 = value;
		}
	}

	public int FLD_Luc
	{
		get
		{
			return int_67;
		}
		set
		{
			int_67 = value;
		}
	}

	public int FLD_Than
	{
		get
		{
			return int_68;
		}
		set
		{
			int_68 = value;
		}
	}

	public int FLD_CongKich
	{
		get
		{
			return int_69;
		}
		set
		{
			int_69 = value;
		}
	}

	public double CongKichThapNhat
	{
		get
		{
			return double_179;
		}
		set
		{
			double_179 = value;
		}
	}

	public int FLD_CongKichThapNhat
	{
		get
		{
			return _FLD_CongKichThapNhat;
		}
		set
		{
			_FLD_CongKichThapNhat = value;
		}
	}

	public int FLD_LonNhatCongKich
	{
		get
		{
			return int_70;
		}
		set
		{
			int_70 = value;
		}
	}

	public int FLD_TrungDich
	{
		get
		{
			return int_71;
		}
		set
		{
			int_71 = value;
		}
	}

	public int FLD_PhongNgu
	{
		get
		{
			return int_72;
		}
		set
		{
			int_72 = value;
		}
	}

	public int FLD_NeTranh
	{
		get
		{
			return int_73;
		}
		set
		{
			int_73 = value;
		}
	}

	public DateTime 解除师徒关系ThoiGian
	{
		get
		{
			return dateTime_2;
		}
		set
		{
			dateTime_2 = value;
		}
	}

	public DateTime FLD_NgayKiNiemKetHon
	{
		get
		{
			return dateTime_3;
		}
		set
		{
			dateTime_3 = value;
		}
	}

	public double FLD_NhanVat_KhiCong_CongKich
	{
		get
		{
			return double_180;
		}
		set
		{
			double_180 = value;
		}
	}

	public int FLD_NhanVat_KhiCong_TrungDich
	{
		get
		{
			return int_74;
		}
		set
		{
			int_74 = value;
		}
	}

	public int FLD_NhanVat_KhiCong_PhongNgu
	{
		get
		{
			return int_75;
		}
		set
		{
			int_75 = value;
		}
	}

	public int FLD_NhanVat_KhiCong_TrongLuong
	{
		get
		{
			return int_76;
		}
		set
		{
			int_76 = value;
		}
	}

	public int FLD_NhanVat_KhiCong_NeTranh
	{
		get
		{
			return int_77;
		}
		set
		{
			int_77 = value;
		}
	}

	public double FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram
	{
		get
		{
			return double_181;
		}
		set
		{
			double_181 = value;
		}
	}

	public double FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram_HP_MP
	{
		get
		{
			return double_181_HP_MP;
		}
		set
		{
			double_181_HP_MP = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1022
	{
		get
		{
			return double_181_TTTP_1022;
		}
		set
		{
			double_181_TTTP_1022 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1023
	{
		get
		{
			return double_181_TTTP_1023;
		}
		set
		{
			double_181_TTTP_1023 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1027
	{
		get
		{
			return double_181_TTTP_1027;
		}
		set
		{
			double_181_TTTP_1027 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1028
	{
		get
		{
			return double_181_TTTP_1028;
		}
		set
		{
			double_181_TTTP_1028 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1480
	{
		get
		{
			return double_181_TTTP_1480;
		}
		set
		{
			double_181_TTTP_1480 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1022
	{
		get
		{
			return double_182_TTTP_1022;
		}
		set
		{
			double_182_TTTP_1022 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1023
	{
		get
		{
			return double_182_TTTP_1023;
		}
		set
		{
			double_182_TTTP_1023 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1027
	{
		get
		{
			return double_182_TTTP_1027;
		}
		set
		{
			double_182_TTTP_1027 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1028
	{
		get
		{
			return double_182_TTTP_1028;
		}
		set
		{
			double_182_TTTP_1028 = value;
		}
	}

	public double FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1480
	{
		get
		{
			return double_182_TTTP_1480;
		}
		set
		{
			double_182_TTTP_1480 = value;
		}
	}

	public double FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram
	{
		get
		{
			return double_182;
		}
		set
		{
			double_182 = value;
		}
	}

	public double FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram_HP_MP
	{
		get
		{
			return double_182_HP_MP;
		}
		set
		{
			double_182_HP_MP = value;
		}
	}

	public double FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram
	{
		get
		{
			return double_183;
		}
		set
		{
			double_183 = value;
		}
	}

	public double FLD_NhanVat_KhiCong_VoCong_LucPhongNgu_GiaTangTiLePhanTram
	{
		get
		{
			return double_184;
		}
		set
		{
			double_184 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_LucPhongNguVoCong
	{
		get
		{
			return _FLD_NhanVat_ThemVao_LucPhongNguVoCong;
		}
		set
		{
			_FLD_NhanVat_ThemVao_LucPhongNguVoCong = value;
		}
	}

	public int FLD_NhanVat_ThemVao_CongKich
	{
		get
		{
			return int_78;
		}
		set
		{
			int_78 = value;
		}
	}

	public int FLD_NhanVat_ThemVao_CongKich_1480
	{
		get
		{
			return int_78_1480;
		}
		set
		{
			int_78_1480 = value;
		}
	}

	public int FLD_NhanVat_ThemVao_CongKich_HP_MP
	{
		get
		{
			return int_78_HP_MP;
		}
		set
		{
			int_78_HP_MP = value;
		}
	}

	public int FLD_NhanVat_ThemVao_PhongNgu_1480
	{
		get
		{
			return int_79_1480;
		}
		set
		{
			int_79_1480 = value;
		}
	}

	public int FLD_NhanVat_ThemVao_PhongNgu
	{
		get
		{
			return int_79;
		}
		set
		{
			int_79 = value;
		}
	}

	public int FLD_NhanVat_ThemVao_PhongNgu_HP_MP
	{
		get
		{
			return int_79_HP_MP;
		}
		set
		{
			int_79_HP_MP = value;
		}
	}

	public int FLD_ThanNu_ThemVao_CongKich
	{
		get
		{
			return _FLD_ThanNu_ThemVao_CongKich;
		}
		set
		{
			_FLD_ThanNu_ThemVao_CongKich = value;
		}
	}

	public int FLD_ThanNu_ThemVao_PhongNgu
	{
		get
		{
			return _FLD_ThanNu_ThemVao_PhongNgu;
		}
		set
		{
			_FLD_ThanNu_ThemVao_PhongNgu = value;
		}
	}

	public int FLD_NhanVat_ThemVao_TrungDich
	{
		get
		{
			return int_80;
		}
		set
		{
			int_80 = value;
		}
	}

	public int FLD_NhanVat_ThemVao_NeTranh
	{
		get
		{
			return int_81;
		}
		set
		{
			int_81 = value;
		}
	}

	public int FLD_NhanVat_ThemVao_KhiCong_TTTP_1480
	{
		get
		{
			return int_82_TTTP_1480;
		}
		set
		{
			int_82_TTTP_1480 = value;
		}
	}

	public int FLD_NhanVat_ThemVao_KhiCong
	{
		get
		{
			return int_82;
		}
		set
		{
			int_82 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_PhanTramKinhNghiem
	{
		get
		{
			return double_185;
		}
		set
		{
			double_185 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan
	{
		get
		{
			return double_274;
		}
		set
		{
			double_274 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien
	{
		get
		{
			return double_275;
		}
		set
		{
			double_275 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_KinhNghiem_Bonus
	{
		get
		{
			return double_277;
		}
		set
		{
			double_277 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_KinhNghiem_TanThu
	{
		get
		{
			return double_2777;
		}
		set
		{
			double_2777 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_Exp_CTP
	{
		get
		{
			return double_27777;
		}
		set
		{
			double_27777 = value;
		}
	}

	public double FLD_Pet_ThemVao_PhanTramKinhNghiem
	{
		get
		{
			return double_186;
		}
		set
		{
			double_186 = value;
		}
	}

	public double FLD_Pet_ThemVao_PhanTram_Gold
	{
		get
		{
			return double_186_Gold;
		}
		set
		{
			double_186_Gold = value;
		}
	}

	public double FLD_ThanThu_ThemVao_PhanTramKinhNghiem
	{
		get
		{
			return _FLD_ThanThu_ThemVao_PhanTramKinhNghiem;
		}
		set
		{
			_FLD_ThanThu_ThemVao_PhanTramKinhNghiem = value;
		}
	}

	public double FLD_ThanThu_ThemVao_PhanTramTraiNghiem
	{
		get
		{
			return _FLD_ThanThu_ThemVao_PhanTramTraiNghiem;
		}
		set
		{
			_FLD_ThanThu_ThemVao_PhanTramTraiNghiem = value;
		}
	}

	public double FLD_NhanVat_ThemVao_PhanTramTraiNghiem
	{
		get
		{
			return double_187;
		}
		set
		{
			double_187 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram
	{
		get
		{
			return double_188;
		}
		set
		{
			double_188 = value;
		}
	}

	public int FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh
	{
		get
		{
			return int_83;
		}
		set
		{
			int_83 = value;
		}
	}

	public int FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh
	{
		get
		{
			return int_84;
		}
		set
		{
			int_84 = value;
		}
	}

	public int FLD_KetHonLeVat_ThemVaoThuocTinhThach
	{
		get
		{
			return int_85;
		}
		set
		{
			int_85 = value;
		}
	}

	public int FLD_Pet_ThemVao_LonNhatHP
	{
		get
		{
			return int_86;
		}
		set
		{
			int_86 = value;
		}
	}

	public int FLD_Pet_ThemVao_CongKich
	{
		get
		{
			return int_87;
		}
		set
		{
			int_87 = value;
		}
	}

	public int FLD_Pet_ThemVao_PhongNgu
	{
		get
		{
			return int_88;
		}
		set
		{
			int_88 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_GiaBanTiLePhanTram
	{
		get
		{
			return double_189;
		}
		set
		{
			double_189 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_VoHuanThuHoach_SoLuongTiLePhanTram
	{
		get
		{
			return double_190;
		}
		set
		{
			double_190 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_HapHonTiLe_TiLePhanTram
	{
		get
		{
			return double_191;
		}
		set
		{
			double_191 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram
	{
		get
		{
			return double_192;
		}
		set
		{
			double_192 = value;
		}
	}

	public double FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram
	{
		get
		{
			return double_193;
		}
		set
		{
			double_193 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram
	{
		get
		{
			return double_194;
		}
		set
		{
			double_194 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram
	{
		get
		{
			return double_195;
		}
		set
		{
			double_195 = value;
		}
	}

	public double FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram
	{
		get
		{
			return double_196;
		}
		set
		{
			double_196 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_VoCongTrungDich
	{
		get
		{
			return _FLD_TrangBi_ThemVao_VoCongTrungDich;
		}
		set
		{
			_FLD_TrangBi_ThemVao_VoCongTrungDich = value;
		}
	}

	public int FLD_TrangBi_ThemVao_VoCongNeTranh
	{
		get
		{
			return _FLD_TrangBi_ThemVao_VoCongNeTranh;
		}
		set
		{
			_FLD_TrangBi_ThemVao_VoCongNeTranh = value;
		}
	}

	public int GiamSatThuong_DoiPhuong
	{
		get
		{
			return _GiamMienDoiPhuongTonThuong;
		}
		set
		{
			_GiamMienDoiPhuongTonThuong = value;
		}
	}

	public double FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong
	{
		get
		{
			return _Nhan_GiamNeTranh_DoiPhuong;
		}
		set
		{
			_Nhan_GiamNeTranh_DoiPhuong = value;
		}
	}

	public double FLD_TrangBi_DayChuyen_GiamChinhXac_DoiPhuong
	{
		get
		{
			return _DayChuyen_GiamChinhXac_DoiPhuong;
		}
		set
		{
			_DayChuyen_GiamChinhXac_DoiPhuong = value;
		}
	}

	public int FLD_TrangBi_DoiPhuong_BiThuong_Giam
	{
		get
		{
			return _Doi_Phuong_Bi_Thuong_Giam;
		}
		set
		{
			_Doi_Phuong_Bi_Thuong_Giam = value;
		}
	}

	public int set_item
	{
		get
		{
			return _set_item;
		}
		set
		{
			_set_item = value;
		}
	}

	public int FLD_TrangBi_GiaTang_DiThuongTrangThai
	{
		get
		{
			return _FLD_TrangBi_GiaTang_DiThuongTrangThai;
		}
		set
		{
			_FLD_TrangBi_GiaTang_DiThuongTrangThai = value;
		}
	}

	public int FLD_TrangBi_GiaTangDoiPhuong_DiThuong
	{
		get
		{
			return _FLD_TrangBi_GiaTangDoiPhuong_DiThuong;
		}
		set
		{
			_FLD_TrangBi_GiaTangDoiPhuong_DiThuong = value;
		}
	}

	public double FLD_TrangBi_LucPhongNguVoCong
	{
		get
		{
			return double_197;
		}
		set
		{
			double_197 = value;
		}
	}

	public double FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram
	{
		get
		{
			return double_198_HatNgoc;
		}
		set
		{
			double_198_HatNgoc = value;
		}
	}

	public double FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram
	{
		get
		{
			return double_198;
		}
		set
		{
			double_198 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_CongKich
	{
		get
		{
			return int_89;
		}
		set
		{
			int_89 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_PhongNgu
	{
		get
		{
			return int_90;
		}
		set
		{
			int_90 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_PhongNguNew
	{
		get
		{
			return int_91;
		}
		set
		{
			int_91 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_VuKhi_CuongHoa
	{
		get
		{
			return int_92;
		}
		set
		{
			int_92 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa
	{
		get
		{
			return int_93;
		}
		set
		{
			int_93 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_TrungDich
	{
		get
		{
			return int_94;
		}
		set
		{
			int_94 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich
	{
		get
		{
			return double_199;
		}
		set
		{
			double_199 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramPhongNgu
	{
		get
		{
			return double_200;
		}
		set
		{
			double_200 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_TrungDichTiLePhanTram
	{
		get
		{
			return double_201;
		}
		set
		{
			double_201 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_Phan_NoKhi
	{
		get
		{
			return int_95;
		}
		set
		{
			int_95 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_NeTranhTiLePhanTram
	{
		get
		{
			return double_202;
		}
		set
		{
			double_202 = value;
		}
	}

	public double FLD_TrangBi_GiamXuong_MucThuongTon
	{
		get
		{
			return double_203;
		}
		set
		{
			double_203 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_MucThuongTon
	{
		get
		{
			return int_96;
		}
		set
		{
			int_96 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhoiTao_XacSuat_PhanNo_BanDauTiLePhanTram
	{
		get
		{
			return double_204;
		}
		set
		{
			double_204 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram
	{
		get
		{
			return double_205;
		}
		set
		{
			double_205 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_NeTranh
	{
		get
		{
			return int_97;
		}
		set
		{
			int_97 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_KhiCong
	{
		get
		{
			return int_98;
		}
		set
		{
			int_98 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_PhanTramKinhNghiem
	{
		get
		{
			return double_206;
		}
		set
		{
			double_206 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot
	{
		get
		{
			return double_207;
		}
		set
		{
			double_207 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_0
	{
		get
		{
			return double_208;
		}
		set
		{
			double_208 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_1
	{
		get
		{
			return double_209;
		}
		set
		{
			double_209 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_2
	{
		get
		{
			return double_210;
		}
		set
		{
			double_210 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_3
	{
		get
		{
			return double_211;
		}
		set
		{
			double_211 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_4
	{
		get
		{
			return double_212;
		}
		set
		{
			double_212 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_5
	{
		get
		{
			return double_213;
		}
		set
		{
			double_213 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_6
	{
		get
		{
			return double_214;
		}
		set
		{
			double_214 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_7
	{
		get
		{
			return double_215;
		}
		set
		{
			double_215 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_8
	{
		get
		{
			return double_216;
		}
		set
		{
			double_216 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_9
	{
		get
		{
			return double_217;
		}
		set
		{
			double_217 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_11
	{
		get
		{
			return double_219;
		}
		set
		{
			double_219 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_KhiCong_10
	{
		get
		{
			return double_218;
		}
		set
		{
			double_218 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_RecoveryMoney
	{
		get
		{
			return int_100;
		}
		set
		{
			int_100 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_HP
	{
		get
		{
			return int_99;
		}
		set
		{
			int_99 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_MP
	{
		get
		{
			return int_101;
		}
		set
		{
			int_101 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_LaChan
	{
		get
		{
			return int_102;
		}
		set
		{
			int_102 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1DonXuatNghichCanh
	{
		get
		{
			return double_220;
		}
		set
		{
			double_220 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1PhaGiapThuHon
	{
		get
		{
			return double_221;
		}
		set
		{
			double_221 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1TuyetAnhXaHon
	{
		get
		{
			return double_222;
		}
		set
		{
			double_222 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3NoYChiHoa
	{
		get
		{
			return double_223;
		}
		set
		{
			double_223 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe
	{
		get
		{
			return double_2190;
		}
		set
		{
			double_2190 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia
	{
		get
		{
			return double_2191;
		}
		set
		{
			double_2191 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong
	{
		get
		{
			return double_2192;
		}
		set
		{
			double_2192 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich
	{
		get
		{
			return double_2193;
		}
		set
		{
			double_2193 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong
	{
		get
		{
			return double_2194;
		}
		set
		{
			double_2194 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan
	{
		get
		{
			return double_2195;
		}
		set
		{
			double_2195 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu
	{
		get
		{
			return double_2196;
		}
		set
		{
			double_2196 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc
	{
		get
		{
			return double_2197;
		}
		set
		{
			double_2197 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang
	{
		get
		{
			return double_2198;
		}
		set
		{
			double_2198 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu
	{
		get
		{
			return double_2199;
		}
		set
		{
			double_2199 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien
	{
		get
		{
			return double_2200;
		}
		set
		{
			double_2200 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu
	{
		get
		{
			return double_2201;
		}
		set
		{
			double_2201 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy
	{
		get
		{
			return double_2202;
		}
		set
		{
			double_2202 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc
	{
		get
		{
			return double_2203;
		}
		set
		{
			double_2203 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe
	{
		get
		{
			return double_2204;
		}
		set
		{
			double_2204 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu
	{
		get
		{
			return double_2205;
		}
		set
		{
			double_2205 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung
	{
		get
		{
			return double_2206;
		}
		set
		{
			double_2206 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh
	{
		get
		{
			return double_2207;
		}
		set
		{
			double_2207 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet
	{
		get
		{
			return double_2208;
		}
		set
		{
			double_2208 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru
	{
		get
		{
			return double_2209;
		}
		set
		{
			double_2209 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo
	{
		get
		{
			return double_2210;
		}
		set
		{
			double_2210 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat
	{
		get
		{
			return double_2211;
		}
		set
		{
			double_2211 = value;
		}
	}

	public double TrangBi_ThuocTinh_ThemVao_ThangThien_5_TriTan
	{
		get
		{
			return double_2212;
		}
		set
		{
			double_2212 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_LongHong_PhuThe
	{
		get
		{
			return double_21900;
		}
		set
		{
			double_21900 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_KinhThien_DongDia
	{
		get
		{
			return double_21910;
		}
		set
		{
			double_21910 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_DietThe_CuongVong
	{
		get
		{
			return double_21920;
		}
		set
		{
			double_21920 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_ThienLy_NhatKich
	{
		get
		{
			return double_21930;
		}
		set
		{
			double_21930 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_HinhDi_YeuTuong
	{
		get
		{
			return double_21940;
		}
		set
		{
			double_21940 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_NhatChieuSatThan
	{
		get
		{
			return double_21950;
		}
		set
		{
			double_21950 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_LongTraoChiThu
	{
		get
		{
			return double_21960;
		}
		set
		{
			double_21960 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_ThienMaChiLuc
	{
		get
		{
			return double_21970;
		}
		set
		{
			double_21970 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_KinhDao_HaiLang
	{
		get
		{
			return double_21980;
		}
		set
		{
			double_21980 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_BatTu_ChiKhu
	{
		get
		{
			return double_21990;
		}
		set
		{
			double_21990 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1_HuyenVuLoiDien
	{
		get
		{
			return double_22000;
		}
		set
		{
			double_22000 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2_HuyenVuTroChu
	{
		get
		{
			return double_22010;
		}
		set
		{
			double_22010 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3_SatNhanQuy
	{
		get
		{
			return double_22020;
		}
		set
		{
			double_22020 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_MaHonChiLuc
	{
		get
		{
			return double_22030;
		}
		set
		{
			double_22030 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1_LangKinhThoiLe
	{
		get
		{
			return double_22040;
		}
		set
		{
			double_22040 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2_SatTinhQuangPhu
	{
		get
		{
			return double_22050;
		}
		set
		{
			double_22050 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3_KyQuanQuanHung
	{
		get
		{
			return double_22060;
		}
		set
		{
			double_22060 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_PhaKhongTruyTinh
	{
		get
		{
			return double_22070;
		}
		set
		{
			double_22070 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1_PhanNoDieuTiet
	{
		get
		{
			return double_22080;
		}
		set
		{
			double_22080 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2_CoDocGiaiTru
	{
		get
		{
			return double_22090;
		}
		set
		{
			double_22090 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3_ThanLucBaoHo
	{
		get
		{
			return double_22100;
		}
		set
		{
			double_22100 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_ThiDocBaoPhat
	{
		get
		{
			return double_22110;
		}
		set
		{
			double_22110 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_5_TriTan
	{
		get
		{
			return double_22120;
		}
		set
		{
			double_22120 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1DaMaTrienThan
	{
		get
		{
			return double_224;
		}
		set
		{
			double_224 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1LucPhachHoaSon
	{
		get
		{
			return double_225;
		}
		set
		{
			double_225 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1TruongHong_QuanNhat
	{
		get
		{
			return double_226;
		}
		set
		{
			double_226 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1KimChungCuongKhi
	{
		get
		{
			return double_227;
		}
		set
		{
			double_227 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1VanKhiHanhTam
	{
		get
		{
			return double_228;
		}
		set
		{
			double_228 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1ChinhBanBoiNguyen
	{
		get
		{
			return double_229;
		}
		set
		{
			double_229 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1VanKhi_LieuThuong
	{
		get
		{
			return double_230;
		}
		set
		{
			double_230 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1BachBien_ThanHanh
	{
		get
		{
			return double_231;
		}
		set
		{
			double_231 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1CuongPhongThienY
	{
		get
		{
			return double_232;
		}
		set
		{
			double_232 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1PhiHoaDiemThuy
	{
		get
		{
			return double_233;
		}
		set
		{
			double_233 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1HanhPhongLongVu
	{
		get
		{
			return double_234;
		}
		set
		{
			double_234 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2CungDoMatLo
	{
		get
		{
			return double_235;
		}
		set
		{
			double_235 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3HoaLong_ChiHoa
	{
		get
		{
			return double_236;
		}
		set
		{
			double_236 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2ThienDiaDongTho
	{
		get
		{
			return double_237;
		}
		set
		{
			double_237 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1HoThan_CuongKhi
	{
		get
		{
			return double_238;
		}
		set
		{
			double_238 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2DiThoiViTien
	{
		get
		{
			return double_239;
		}
		set
		{
			double_239 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2ThienQuanApDa
	{
		get
		{
			return double_240;
		}
		set
		{
			double_240 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2ThuanThuyThoiChu
	{
		get
		{
			return double_241;
		}
		set
		{
			double_241 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2TamDamAnhNguyet
	{
		get
		{
			return double_242;
		}
		set
		{
			double_242 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2ThienMaHoThe
	{
		get
		{
			return double_243;
		}
		set
		{
			double_243 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_2VanVatHoiXuan
	{
		get
		{
			return double_244;
		}
		set
		{
			double_244 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1HoThanKhiGiap
	{
		get
		{
			return double_245;
		}
		set
		{
			double_245 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3HoaPhuongLamTrieu
	{
		get
		{
			return double_246;
		}
		set
		{
			double_246 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3ThienNgoaiTamThi
	{
		get
		{
			return double_247;
		}
		set
		{
			double_247 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3VoTinhDaKich
	{
		get
		{
			return double_248;
		}
		set
		{
			double_248 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3MinhKinhChiThuy
	{
		get
		{
			return double_249;
		}
		set
		{
			double_249 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3TuDaThuCa
	{
		get
		{
			return double_250;
		}
		set
		{
			double_250 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3NoiTucHanhTam
	{
		get
		{
			return double_251;
		}
		set
		{
			double_251 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_3DiNhuKhacCuong
	{
		get
		{
			return double_252;
		}
		set
		{
			double_252 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong
	{
		get
		{
			return double_253;
		}
		set
		{
			double_253 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong
	{
		get
		{
			return double_254;
		}
		set
		{
			double_254 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong
	{
		get
		{
			return double_255;
		}
		set
		{
			double_255 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4LietNhatViemViem
	{
		get
		{
			return double_256;
		}
		set
		{
			double_256 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4TruongHongQuanThien
	{
		get
		{
			return double_257;
		}
		set
		{
			double_257 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4AiHongBienDa
	{
		get
		{
			return double_258;
		}
		set
		{
			double_258 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1DoatMenhLienHoan
	{
		get
		{
			return double_259;
		}
		set
		{
			double_259 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1DienQuangThachHoa
	{
		get
		{
			return double_260;
		}
		set
		{
			double_260 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_1TinhIchCauTinh
	{
		get
		{
			return double_261;
		}
		set
		{
			double_261 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4VongMaiThiemHoa
	{
		get
		{
			return double_262;
		}
		set
		{
			double_262 = value;
		}
	}

	public double FLD_TrangBi_ThemVao_ThangThien_4HuyenTiChanMach
	{
		get
		{
			return double_263;
		}
		set
		{
			double_263 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_Tam
	{
		get
		{
			return int_103;
		}
		set
		{
			int_103 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_The
	{
		get
		{
			return int_104;
		}
		set
		{
			int_104 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_Luc
	{
		get
		{
			return int_105;
		}
		set
		{
			int_105 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_Than
	{
		get
		{
			return int_106;
		}
		set
		{
			int_106 = value;
		}
	}

	public int FLD_TrangBi_ThemVao_ThucTinh
	{
		get
		{
			return int_107;
		}
		set
		{
			int_107 = value;
		}
	}

	public int Player_HP_Guild
	{
		get
		{
			return _Player_HP_Guild;
		}
		set
		{
			_Player_HP_Guild = value;
		}
	}

	public int Player_MP_Guild
	{
		get
		{
			return _Player_MP_Guild;
		}
		set
		{
			_Player_MP_Guild = value;
		}
	}

	public int Player_TanCong_Guild
	{
		get
		{
			return _Player_TanCong_Guild;
		}
		set
		{
			_Player_TanCong_Guild = value;
		}
	}

	public int Player_PhongThu_Guild
	{
		get
		{
			return _Player_PhongThu_Guild;
		}
		set
		{
			_Player_PhongThu_Guild = value;
		}
	}

	public double Player_CLVC_Guild
	{
		get
		{
			return _Player_CLVC_Guild;
		}
		set
		{
			_Player_CLVC_Guild = value;
		}
	}

	public int Player_CLVC_Mau_ST
	{
		get
		{
			return _Player_CLVC_Mau_ST;
		}
		set
		{
			_Player_CLVC_Mau_ST = value;
		}
	}

	public int Player_ULPT_Sam_ST
	{
		get
		{
			return _Player_ULPT_Sam_ST;
		}
		set
		{
			_Player_ULPT_Sam_ST = value;
		}
	}

	public double Player_CLVC_VoHuan
	{
		get
		{
			return _Player_CLVC_VoHuan;
		}
		set
		{
			_Player_CLVC_VoHuan = value;
		}
	}

	public int Player_ULPT_Guild
	{
		get
		{
			return _Player_ULPT_Guild;
		}
		set
		{
			_Player_ULPT_Guild = value;
		}
	}

	public int Player_ULPT_VoHuan
	{
		get
		{
			return _Player_ULPT_VoHuan;
		}
		set
		{
			_Player_ULPT_VoHuan = value;
		}
	}

	public int Player_KhiCong_Guild
	{
		get
		{
			return _Player_KhiCong_Guild;
		}
		set
		{
			_Player_KhiCong_Guild = value;
		}
	}

	public int Player_NeTranh_Guild
	{
		get
		{
			return _Player_NeTranh_Guild;
		}
		set
		{
			_Player_NeTranh_Guild = value;
		}
	}

	public int Player_ChinhXac_Guild
	{
		get
		{
			return _Player_ChinhXac_Guild;
		}
		set
		{
			_Player_ChinhXac_Guild = value;
		}
	}

	public int get_HonorID(string string_11, int int_109)
	{
		try
		{
			switch (int_109)
			{
				case 1:
					{
						for (var j = 0; j < World.TheLucChien_XepHang_SoLieu.Count; j++)
						{
							if (World.TheLucChien_XepHang_SoLieu[j].PlayerName == string_11)
							{
								return j switch
								{
									0 => 1008001300,
									1 => 1008001301,
									2 => 1008001302,
									3 => 1008001303,
									4 => 1008001304,
									5 => 1008001305,
									6 => 1008001306,
									7 => 1008001307,
									8 => 1008001308,
									9 => 1008001309,
									10 => 1008001240,
									11 => 1008001241,
									12 => 1008001242,
									13 => 1008001243,
									14 => 1008001244,
									15 => 1008001245,
									16 => 1008001246,
									17 => 1008001247,
									18 => 1008001248,
									19 => 1008001249,
									20 => 1008001230,
									21 => 1008001231,
									22 => 1008001232,
									23 => 1008001233,
									24 => 1008001234,
									25 => 1008001235,
									26 => 1008001236,
									27 => 1008001237,
									28 => 1008001238,
									29 => 1008001239,
									30 => 1008001220,
									31 => 1008001221,
									32 => 1008001222,
									33 => 1008001223,
									34 => 1008001224,
									35 => 1008001225,
									36 => 1008001226,
									37 => 1008001227,
									38 => 1008001228,
									39 => 1008001229,
									40 => 1008001210,
									41 => 1008001211,
									42 => 1008001212,
									43 => 1008001213,
									44 => 1008001214,
									45 => 1008001215,
									46 => 1008001216,
									47 => 1008001217,
									48 => 1008001218,
									49 => 1008001219,
									50 => 1008001200,
									51 => 1008001201,
									52 => 1008001202,
									53 => 1008001203,
									54 => 1008001204,
									55 => 1008001205,
									56 => 1008001206,
									57 => 1008001207,
									58 => 1008001208,
									59 => 1008001209,
									_ => 0,
								};
							}
						}
						break;
					}
				case 2:
					{
						for (var k = 0; k < World.VoLamHuyetChien_XepHang_SoLieu.Count; k++)
						{
							if (World.VoLamHuyetChien_XepHang_SoLieu[k].PlayerName == string_11)
							{
								return k switch
								{
									0 => 1008001310,
									1 => 1008001311,
									2 => 1008001312,
									3 => 1008001313,
									4 => 1008001314,
									5 => 1008001315,
									6 => 1008001316,
									7 => 1008001317,
									8 => 1008001318,
									9 => 1008001319,
									10 => 1008001290,
									11 => 1008001291,
									12 => 1008001292,
									13 => 1008001293,
									14 => 1008001294,
									15 => 1008001295,
									16 => 1008001296,
									17 => 1008001297,
									18 => 1008001298,
									19 => 1008001299,
									20 => 1008001280,
									21 => 1008001281,
									22 => 1008001282,
									23 => 1008001283,
									24 => 1008001284,
									25 => 1008001285,
									26 => 1008001286,
									27 => 1008001287,
									28 => 1008001288,
									29 => 1008001289,
									30 => 1008001270,
									31 => 1008001271,
									32 => 1008001272,
									33 => 1008001273,
									34 => 1008001274,
									35 => 1008001275,
									36 => 1008001276,
									37 => 1008001277,
									38 => 1008001278,
									39 => 1008001279,
									40 => 1008001260,
									41 => 1008001261,
									42 => 1008001262,
									43 => 1008001263,
									44 => 1008001264,
									45 => 1008001265,
									46 => 1008001266,
									47 => 1008001267,
									48 => 1008001268,
									49 => 1008001269,
									50 => 1008001250,
									51 => 1008001251,
									52 => 1008001252,
									53 => 1008001253,
									54 => 1008001254,
									55 => 1008001255,
									56 => 1008001256,
									57 => 1008001257,
									58 => 1008001258,
									59 => 1008001259,
									_ => 0,
								};
							}
						}
						break;
					}
				case 3:
					{
						for (var i = 0; i < World.BangPhai_XepHang_SoLieu.Count; i++)
						{
							if (World.BangPhai_XepHang_SoLieu[i].PlayerName == string_11)
							{
								return i switch
								{
									0 => 1008001310,
									1 => 1008001311,
									2 => 1008001312,
									3 => 1008001313,
									4 => 1008001314,
									5 => 1008001315,
									6 => 1008001316,
									7 => 1008001317,
									8 => 1008001318,
									9 => 1008001319,
									10 => 1008001290,
									11 => 1008001291,
									12 => 1008001292,
									13 => 1008001293,
									14 => 1008001294,
									15 => 1008001295,
									16 => 1008001296,
									17 => 1008001297,
									18 => 1008001298,
									19 => 1008001299,
									20 => 1008001280,
									21 => 1008001281,
									22 => 1008001282,
									23 => 1008001283,
									24 => 1008001284,
									25 => 1008001285,
									26 => 1008001286,
									27 => 1008001287,
									28 => 1008001288,
									29 => 1008001289,
									30 => 1008001270,
									31 => 1008001271,
									32 => 1008001272,
									33 => 1008001273,
									34 => 1008001274,
									35 => 1008001275,
									36 => 1008001276,
									37 => 1008001277,
									38 => 1008001278,
									39 => 1008001279,
									40 => 1008001260,
									41 => 1008001261,
									42 => 1008001262,
									43 => 1008001263,
									44 => 1008001264,
									45 => 1008001265,
									46 => 1008001266,
									47 => 1008001267,
									48 => 1008001268,
									49 => 1008001269,
									50 => 1008001250,
									51 => 1008001251,
									52 => 1008001252,
									53 => 1008001253,
									54 => 1008001254,
									55 => 1008001255,
									56 => 1008001256,
									57 => 1008001257,
									58 => 1008001258,
									59 => 1008001259,
									_ => 0,
								};
							}
						}
						break;
					}
			}
			return 0;
		}
		catch
		{
			return 0;
		}
	}

	public void Clear()
	{
		Pet_CuongKhi = 0;
		set_item = 0;
		Num_Attack = 0;
		PhaiChangKiemTraNhipTim = false;
		ThongBao_CongThanh = 0;
		DanhNhieuMucTieu_Loai_Bom_ThanNu = null;
		ThuocTinh_PhongAn = 0;
		TuDongTiepTe = 0;
		ChucNang_Auto_ThucHien = 0;
		OfflineTreoMaySkill_ID = 0;
		DiDong_PhaiChang_Lan_1 = 0;
		Offline_Auto_Attack_Time = DateTime.Now;
		Auto_Train_Track = DateTime.Now;
		Time_Su_Dung_HoTro_ChucNang = DateTime.Now;
		Time_Bom_HP_Auto_Offline = DateTime.Now;
		Time_HoiSinh_Auto_Offline = DateTime.Now;
		Offline_TreoMay_BanDo = 101;
		Offline_TreoMay_Mode_ON_OFF = 0;
		BachDocBatXam = 0;
		Auto_TreoMay_TheoDoi_ThoiGian = 0;
		Offline_TreoMay_ToaDo_X = 0;
		Offline_TreoMay_ToaDo_Y = 0;
		PK_Save_ToaDo_X = 0f;
		PK_Save_ToaDo_Y = 0f;
		PhatDongVatLy_VoMinhAmThi = false;
		PhatDong_VoMinhAmThi = false;
		PhatDong_TriMenhTuyetSat = false;
		TriggerAttributePromotion = 0;
		PhatDong_LoanPhuongHoaMinh = false;
		Player_VoDich = false;
		PKTuVong = false;
		ChayTron = false;
		WhetherToUpdateTheConfiguration = false;
		Online = false;
		TriggerMapMovementEvent = false;
		FLD_TrangBi_ThemVao_DoiQuai_PhongNgu = 0;
		FLD_TrangBi_ThemVao_DoiQuai_CongKich = 0;
		FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu = 0;
		FLD_TrangBi_ThuocTinhHoa_ThemVao_TangDame_QuaiVat = 0.0;
		FLD_ThanThu_ThemVao_PhanTramKinhNghiem = 0.0;
		FLD_ThanThu_ThemVao_PhanTramTraiNghiem = 0.0;
		TruongHongQuanThien_ThemVao_Dame_Def = 0;
		TruongHongQuanThien_ThemVao_HP_MP = 0;
		ManNguyetCuongPhong_ThemVao_Dame_Def = 0;
		HongNguyetCuongPhong_ThemVao_Dame_Def = 0;
		CaoCotLieuThuong_ThemVao_Def = 0;
		CPVP_ThemVao_Dame = 0.0;
		CPVP_ThemVao_Def = 0.0;
		AoGuild_ThemVao_PhanTram_CongKich = 0.0;
		AoGuild_ThemVao_PhanTram_PhongThu = 0.0;
		Player_TanCong_Guild = 0;
		Player_PhongThu_Guild = 0;
		DuocPham_ThemVao_DoiQuai_PhongNgu = 0;
		DuocPham_ThemVao_DoiQuai_PhongNgu_NEW = 0;
		DuocPham_TuHaThanDan_ThemVao_DoiQuai_CongKich = 0;
		DuocPham_TuHaThanDan_ThemVao_DoiQuai_PhongNgu = 0;
		DuocPham_ThemVao_DoiQuai_CongKich = 0;
		DuocPham_ThemVao_DoiQuai_CongKich_NEW = 0;
		TheLucChien_Tang_HP_TayDai = 0;
		TheLucChien_Tang_HP_TayNgan = 0;
		TTTP_Tang_HP = 0;
		TTTP_Tang_HP_NEW = 0;
		TTTP_Tang_MP = 0;
		TTTP_Tang_MP_NEW = 0;
		FLD_DuocPham_ThemVao_KhiCong = 0;
		VuKhi_LucCongKich = 0;
		VuKhiTyLePhanTram_CongKichVoCong = 0.0;
		YPhuc_LucPhongNgu = 0;
		YPhuc_LucPhongNguVoCong_TiLePhanTram = 0.0;
		GiaoDichNhanVat_ID = 0;
		GiaoDichThaoTacD = 0;
		TempGameSecurityCode = string.Empty;
		StoreName = string.Empty;
		AnToan_MatMa_ThaoTacID = -1;
		WhetherTheSecurityCodeIsVerified = false;
		WhetherMarried = 0;
		HonNhan_NguoiDat_CauHoiDapAn = 2;
		GiaiTruQuanHe_Countdown = 0;
		NhanCuoiKhacChu = string.Empty;
		HonorID_ = 0;
		PhaiChangMangTheoAoChang_HanhLy = false;
		NoKhi = false;
		Poisoning = false;
		NewMartialArtsCombos = new();
		// SerList initialization removed - using World.ServerList
		TimeMedicine = new();
		Ninja_BatDauPhanThuongKyNang = false;
		Ninja_PhanThuongTanCongVatLy = 0.0;
		Ninja_MaPhap_TanCong_TangThem = 0.0;
		ThangThienKhiCong_CapDoVoCong = 0.0;
		NhanVoHuan_MoiNgay = 0;
		MatDi_VoHuan = 0;
		PVPScore = 0;
		PVPEscapes = 0;
		lastX = 0f;
		lastY = 0f;
		lastMAP = 101;
		AttackX = 0f;
		AttackY = 0f;
		NumberOfAttacks = 0;
		GioiHan_SoLan_Trung_BindAccount = 5;
		Gioi_han_so_lan_dung_SK1 = 10;
		RemainingTimeOfTrainingMap = 0;
		ActivityMapRemainingTime = 0;
		FBtime = DateTime.Now;
		Nhan_Vo_Huan_Online = DateTime.Now;
		PKhmtime = DateTime.Now;
		XThmtime = DateTime.Now;
		TMJCtime = DateTime.Now;
		CWhmtime = DateTime.Now;
		SThmtime = DateTime.Now;
		NgungThanBaoChau_ViTri = -1;
		ThaoPhat_TinhGop_TonThuong = 0;
		FLD_Pet_ThemVao_CongKich = 0;
		FLD_Pet_ThemVao_PhongNgu = 0;
		FLD_Pet_ThemVao_LonNhatHP = 0;
		CungPhatDong_TanCongNhom = false;
		HanBaoQuan_ThienMaCuongHuyet_LucCongKich = 0.0;
		HanBaoQuan_ThienMaCuongHuyet_XSoLan = 0;
		FLD_loveDegreeLevel = 0;
		FLD_canYouSendFlowers = true;
		CurrentOperationType = 0;
		NhanVatKhoa_Chat = false;
		Khoa_ChatTyLeCaCuocNhanVat = 0;
		FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh = 0;
		FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh = 0;
		FLD_KetHonLeVat_ThemVaoThuocTinhThach = 0;
		QuyenSu_KiemSoat_ComBo = 0;
		QuyenSu_HoiTamNhatKich_UyLuc = 0.3;
		GiaiTru_QuanHeThayTro_ThoiGian = DateTime.Parse("2012/12/12      12:12:12");
		MatDi_VoHuan = 0;
		VoHoang_ThongTe = 0;
		CurrentlyOperatingNPC = 0;
		ThienDiaDongThoNhungLanNeTranh = 0;
		ThienDiaDongThoNeTranhTichLuyDame = 0;
		FLD_HonorID = 0;
		TheLucChien_SatNhan_SoLuong = 0;
		TheLucChien_TuVong_SoLuong = 0;
		CamSu_TrangThai = 0;
		NguyenBao_TaiKhoanTrangThai = false;
		FLD_PVP_Piont = 0;
		CurrentlyActiveSkill_ID = 0;
		NgocLienHoan = new();
		EquipmentDataVersion = 1;
		ComprehensiveWarehouseEquipmentDataVersion = 1;
		Exiting = false;
		IsInLobby = true;
		ArchiveTime = false;
		YeuHoaThanhThao = false;
		VoCongMoi = new X_Vo_Cong_Loai[4, 32];
		MartialArtsAttackPowerOfHusbandAndWife = 0;
		MartialArtsAttackPowerOfHusbandAndWifeMP = 0;
		AppendStatusList = new();
		AppendStatusNewList = new();
		PublicDrugs = new();
		TitleDrug = new();
		QuestList = new();
		CompletedQuestList = new();
		ClientSettings = string.Empty;
		MasterData = new();
		ApprenticeData = new X_Su_Do_Loai[3];
		VoCongMoi = new X_Vo_Cong_Loai[4, 32];
		TrangThai_BatThuong = new();
		TrangThai_PhongThu_BatThuong = new();
		TrangThai_TanCong_BatThuong = new();
		TrangThai_MatMau_BatThuong = new();
		TrangThai_XanhLam_BatThuong = new();
		ThanNu_TrangThai_BatThuong = new();
		Item_In_Bag = new Item[96];
		AoChang_HanhLy = new Item[66];
		EventBag = new Item[24];
		Item_Wear = new Item[18];
		Sub_Wear = new Item[15];
		ThietBiTab3 = new Item[6];
		Item_NTC = new Item[6];
		KhiCong = new X_Khi_Cong_Loai[15];
		PersonalWarehouse = new Item[60];
		PublicWarehouse = new Item[60];
		NhiemVu_VatPham = new X_Nhiem_Vu_Vat_Pham_Loai[36];
		ThangThienKhiCong = new();
		PhanKhiCong = new();
		FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat = 0.0;
		CharactersToAddMax_HP = 0;
		CharactersToAddMax_HP_TTTP_1022 = 0;
		CharactersToAddMax_HP_TTTP_1023 = 0;
		CharactersToAddMax_HP_TTTP_1027 = 0;
		CharactersToAddMax_HP_TTTP_1028 = 0;
		CharactersToAddMax_HP_TTTP_1480 = 0;
		CharactersToAddMax_MP = 0;
		CharactersToAddMax_MP_TTTP_1022 = 0;
		CharactersToAddMax_MP_TTTP_1023 = 0;
		CharactersToAddMax_MP_TTTP_1027 = 0;
		CharactersToAddMax_MP_TTTP_1028 = 0;
		CharactersToAddMax_MP_TTTP_1480 = 0;
		NhanVat_KhiCong_ThemVao_LucPhongNguVoCong = 0;
		CharacterIsBasicallyTheLargest_HP = 0;
		FLD_TrangBi_ThemVao_HP = 0;
		FLD_TrangBi_ThemVao_RecoveryMoney = 0;
		NhanVat_KhiCong_ThemVao_HP = 0;
		NhanVat_KhiCong_ThemVao_PhanTram_HP = 0;
		NhanVat_KhiCong_ThemVao_MP = 0;
		NhanVat_KhiCong_ThemVao_PhanTram_MP = 0;
		CharacterIsBasicallyTheLargest_MP = 0;
		CharacterIsBasicallyTheLargest_Barrier = 0;
		NhanVat_AP = 0;
		GangBadge = null;
		AdditionalStatusItemsNew = null;
		GangCharacterLevel = 0;
		GuildName = string.Empty;
		GuildId = 0;
		GangLevel = 0;
		FLD_ThemVaoTiLePhanTram_CongKich = 0.0;
		FLD_DoThanThemVao_PhanTramTanCong = 0.0;
		FLD_TrangBi_Nhan_va_VuKhi_GiamNeTranh_TiLePhanTram_DoiPhuong = 0.0;
		FLD_TrangBi_DayChuyen_GiamChinhXac_DoiPhuong = 0.0;
		FLD_TrangBi_ThemVao_TiLePhanTram_CongKich = 0.0;
		FLD_TrangBi_ThemVao_TiLePhanTram_PhongThu = 0.0;
		FLD_TrangBi_DoiPhuong_BiThuong_Giam = 0;
		FLD_CamSu_HieuUng_SieuAmThanh_DameDef = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
		FLD_ThemVao_PhanTram_Def_HoanVuVanHoa = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480 = 0.0;
		FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
		FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
		FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
		FLD_ThemVaoTiLePhanTram_MPCaoNhat = 0.0;
		FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
		FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram_HP_MP = 0.0;
		FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram_HP_MP = 0.0;
		FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1022 = 0.0;
		FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1023 = 0.0;
		FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1027 = 0.0;
		FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1028 = 0.0;
		FLD_NhanVat_SuDungPill_CLVC_TiLePhanTram_TTTP_1480 = 0.0;
		FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1022 = 0.0;
		FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1023 = 0.0;
		FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1027 = 0.0;
		FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1028 = 0.0;
		FLD_NhanVat_SuDungPill_ULPT_TiLePhanTram_TTTP_1480 = 0.0;
		CongKichThapNhat = 0.0;
		FLD_DuocPham_GiamBotCongKich = 0.0;
		FLD_DuocPham_GiamBotPhongNgu = 0.0;
		NhanVatNeTranhSkill = 0;
		NhanVatThemVaoNeTranhSkill = 0;
		CharacterAdditionalCombatPower = 0;
		CharactersAddMartialArtsPower = 0;
		TitleAddedHP = 0;
		TitleAddedAttack = 0;
		TitleAddedDefense = 0;
		BangPhaiTitleAddedHP = 0;
		BangPhaiTitleAddedAttack = 0;
		BangPhaiTitleAddedDefense = 0;
		RoseTitleAddHP = 0;
		RoseTitleAddAttack = 0;
		RoseTitleAddDefense = 0;
		FLD_NhanVat_ThemVao_CongKich = 0;
		FLD_NhanVat_ThemVao_CongKich_1480 = 0;
		FLD_NhanVat_ThemVao_CongKich_HP_MP = 0;
		FLD_NhanVat_ThemVao_PhongNgu = 0;
		FLD_NhanVat_ThemVao_PhongNgu_HP_MP = 0;
		FLD_ThanNu_ThemVao_PhongNgu = 0;
		FLD_ThanNu_ThemVao_CongKich = 0;
		FLD_NhanVat_ThemVao_TrungDich = 0;
		FLD_NhanVat_ThemVao_NeTranh = 0;
		FLD_NhanVat_ThemVao_KhiCong = 0;
		FLD_NhanVat_ThemVao_KhiCong_TTTP_1480 = 0;
		FLD_NhanVat_ThemVao_GiaBanTiLePhanTram = 0.0;
		FLD_NhanVat_ThemVao_VoHuanThuHoach_SoLuongTiLePhanTram = 0.0;
		FLD_NhanVat_ThemVao_HapHonTiLe_TiLePhanTram = 0.0;
		FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram = 0.0;
		FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.0;
		FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
		FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan = 0.0;
		FLD_NhanVat_ThemVao_KinhNghiem_Bonus = 0.0;
		FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien = 0.0;
		FLD_NhanVat_ThemVao_KinhNghiem_Party = 0.0;
		FLD_NhanVat_ThemVao_KinhNghiem_KetHon = 0.0;
		FLD_NhanVat_ThemVao_KinhNghiem_TanThu = 0.0;
		FLD_NhanVat_ThemVao_Exp_CTP = 0.0;
		FLD_Pet_ThemVao_PhanTramKinhNghiem = 0.0;
		FLD_Pet_ThemVao_PhanTram_Gold = 0.0;
		FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram = 0.0;
		FLD_NhanVat_ThemVao_PhanTramTraiNghiem = 0.0;
		FLD_NhanVat_ThemVao_LucPhongNguVoCong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2VanVatHoiXuan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThienQuanApDa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2CungDoMatLo = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3HoaLong_ChiHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1HoThan_CuongKhi = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1HoThanKhiGiap = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3NoYChiHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2TamDamAnhNguyet = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThuanThuyThoiChu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThienDiaDongTho = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2ThienMaHoThe = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2DiThoiViTien = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3HoaPhuongLamTrieu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3MinhKinhChiThuy = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3NoiTucHanhTam = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3VoTinhDaKich = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3ThienNgoaiTamThi = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3DiNhuKhacCuong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3TuDaThuCa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4AiHongBienDa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4DocXaXuatDong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4HongNguyetCuongPhong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4LietNhatViemViem = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4ManNguyetCuongPhong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4VongMaiThiemHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4HuyenTiChanMach = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_4TruongHongQuanThien = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1BachBien_ThanHanh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DonXuatNghichCanh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1PhiHoaDiemThuy = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1HanhPhongLongVu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1KimChungCuongKhi = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1TuyetAnhXaHon = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1CuongPhongThienY = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1LucPhachHoaSon = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1PhaGiapThuHon = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DaMaTrienThan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1VanKhiHanhTam = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1VanKhi_LieuThuong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1TruongHong_QuanNhat = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1ChinhBanBoiNguyen = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DoatMenhLienHoan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1DienQuangThachHoa = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1TinhIchCauTinh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_LongHong_PhuThe = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_KinhThien_DongDia = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_DietThe_CuongVong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_ThienLy_NhatKich = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_HinhDi_YeuTuong = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_NhatChieuSatThan = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_LongTraoChiThu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_ThienMaChiLuc = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_KinhDao_HaiLang = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_BatTu_ChiKhu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1_HuyenVuLoiDien = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2_HuyenVuTroChu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3_SatNhanQuy = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_MaHonChiLuc = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1_LangKinhThoiLe = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2_SatTinhQuangPhu = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3_KyQuanQuanHung = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_PhaKhongTruyTinh = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_1_PhanNoDieuTiet = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_2_CoDocGiaiTru = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_3_ThanLucBaoHo = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_ThiDocBaoPhat = 0.0;
		FLD_TrangBi_ThemVao_ThangThien_5_TriTan = 0.0;
		FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.0;
		FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
		FLD_TrangBi_LucPhongNguVoCong = 0.0;
		FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich = 0.0;
		FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramPhongNgu = 0.0;
		FLD_TrangBi_ThemVao_TrungDichTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_NeTranhTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_Phan_NoKhi = 0;
		FLD_TrangBi_ThemVao_KhoiTao_XacSuat_PhanNo_BanDauTiLePhanTram = 0.0;
		FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram = 0.0;
		FLD_TrangBi_GiamXuong_MucThuongTon = 0.0;
		FLD_TrangBi_ThemVao_MucThuongTon = 0;
		FLD_TrangBi_ThemVao_CongKich = 0;
		FLD_TrangBi_ThemVao_LaChan = 0;
		FLD_TrangBi_ThemVao_PhongNgu = 0;
		FLD_TrangBi_ThemVao_PhongNguNew = 0;
		FLD_TrangBi_ThemVao_TrungDich = 0;
		FLD_TrangBi_ThemVao_NeTranh = 0;
		FLD_TrangBi_ThemVao_KhiCong = 0;
		FLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa = 0;
		FLD_TrangBi_ThemVao_VuKhi_CuongHoa = 0;
		FLD_TrangBi_ThemVao_PhanTramKinhNghiem = 0.0;
		FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot = 0.0;
		FLD_TrangBi_ThemVao_Tam = 0;
		FLD_TrangBi_ThemVao_The = 0;
		FLD_TrangBi_ThemVao_Luc = 0;
		FLD_TrangBi_ThemVao_Than = 0;
		FLD_TrangBi_ThemVao_ThucTinh = 0;
		FLD_TrangBi_ThemVao_KhiCong_0 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_1 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_2 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_3 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_4 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_5 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_6 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_7 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_8 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_9 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_10 = 0.0;
		FLD_TrangBi_ThemVao_KhiCong_11 = 0.0;
		FLD_NgayKiNiemKetHon = DateTime.Now;
		HanBaoQuan_ThienMaCuongHuyet = 0.0;
		HanBaoQuan_TruyCotHapNguyen = 0.0;
		HanBaoQuan_HoaLongVanDinh = 0.0;
		DAO_LienHoanPhiVu = 0.0;
		DAO_ThangThien_3_KhiCong_HoaLong_ChiHoa = 0.0;
		KIEM_PhaThien_NhatKiem = 0.0;
		THUONG_ThangThien_3_KhiCong_NoYChiHoa = 0.0;
		NhatKich_TriMang_TiLe = 0.0;
		DamHoaLien_ChieuThucTanPhap = 0.0;
		QuaiVat_PhanSatThuong_TiLe = 0.0;
		NguoiChoi_PhanSatThuong_Tile = 0.0;
		PhaGiap_TiLe = 0.0;
		ThanCo_MinhChau = 0.0;
		ChanVu_TuyetKich = 0.0;
		AmAnh_TuyetSat = 0.0;
		LuuQuang_LoanVu = 0.0;
		KIEM_ThangThien_1_KhiCong_HoThan_CuongKhi = 0.0;
		KIEM_DiHoa_TiepMoc = 0.0;
		KIEM_HoiLieu_ThanPhap = 0.0;
		KIEM_NoHai_CuongLan = 0.0;
		KIEM_TrungQuan_NhatNo = 0.0;
		KIEM_NhanKiem_NhatThe = 0.0;
		THUONG_VanKhi_LieuThuong = 0.0;
		THUONG_LinhGiapHoThan = 0.0;
		THUONG_CanKhonNaDi = 0.0;
		THUONG_CuongThanHangThe = 0.0;
		THUONG_ChuyenCongViThu = 0.0;
		TuHao_ChuyenCongViThu = 0.0;
		THUONG_MatNhatCuongVu = 0.0;
		CUNG_NhueLoiChiTien = 0.0;
		CUNG_LiepUngChiNhan = 0.0;
		CUNG_VoMinhAmThi = 0.0;
		CUNG_HoiLuuChanKhi = 0.0;
		CUNG_LuuTinhTamThi = 0.0;
		CUNG_TriMenhTuyetSat = 0.0;
		CUNG_TamThanNgungTu = 0.0;
		DAIPHU_VanKhiLieuTam = 0.0;
		DAIPHU_TruongCongKichLuc = 0.0;
		DAIPHU_ThaiCucTamPhap = 0.0;
		DAIPHU_DieuThuHoiXuan = 0.0;
		DAIPHU_ThanNongTienThuat = 0.0;
		DAIPHU_CuuThienChanKhi = 0.0;
		DAIPHU_ThangThien_2_KhiCong_VanVatHoiXuan = 0.0;
		DaiPhu_TT5_HapTheLieuThuong = 0.0;
		DAIPHU_HapTinhDaiPhap = 0.0;
		NINJA_KinhKhaChiNo = 0.0;
		NINJA_TamHoaTuDinh = 0.0;
		NINJA_LienHoanPhiVu = 0.0;
		NINJA_TatSatNhatKich = 0.0;
		NINJA_TamThanNgungTu = 0.0;
		NINJA_TriThuTuyetMenh = 0.0;
		NINJA_TienPhatCheNhan = 0.0;
		NINJA_ThienChuVanThu = 0.0;
		NINJA_LienTieuDaiDa = 0.0;
		NINJA_KhoaiDaoLoanVu = 0.0;
		NINJA_NhatChieuTanSat = 0.0;
		NINJA_ThangThien_3_KhiCong_VoTinhDaKich = 0.0;
		CAMSU_CaoSonLuuThuy = 0.0;
		CAMSU_HanCungThuNguyet = 0.0;
		CAMSU_LoanPhuongHoaMinh = 0.0;
		CAMSU_MaiHoaTamLong = 0.0;
		CAMSU_ThanhTamPhoThien = 0.0;
		CAMSU_ThuGiangDaBac = 0.0;
		CAMSU_TieuTuongVuDa = 0.0;
		CAMSU_DuongQuanTamDiep = 0.0;
		CAMSU_DuongMinhXuanHieu = 0.0;
		CAMSU_NhacDuongTamTuy = 0.0;
		CAMSU_ChienMaBonDang = 0.0;
		CAMSU_TamHoaHuyen_XacXuat_XuatHien = 0.0;
		DamHoaLien_TrungQuan_NhatNo = 0.0;
		DamHoaLien_NoHai_CuongLan = 0.0;
		DamHoaLien_HoiLieu_ThanPhap = 0.0;
		DamHoaLien_TungHoanhVoSong = 0.0;
		DamHoaLien_DiHoa_TiepMoc = 0.0;
		DamHoaLien_HoThan_CuongKhi = 0.0;
		DamHoaLien_LienHoanPhiVu = 0.0;
		HanBaoQuan_ThangThien_1_KhiCong_HanhPhongLongVu = 0.0;
		HanBaoQuan_ThangThien_2_KhiCong_ThienMaHoThe = 0.0;
		HanBaoQuan_ThangThien_3_KhiCong_NoiTucHanhTam = 0.0;
		DAO_ThangThien_1_KhiCong_DonXuatNghichCanh = 0.0;
		KIEM_HonNguyen_KiemPhap = 0.0;
		THUONG_ThangThien_1_KhiCong_PhaGiapThuHon = 0.0;
		CUNG_ThangThien_1_KhiCong_TuyetAnhXaHon = 0.0;
		DAIPHU_HoanVuVanHoa = 0.0;
		DAIPHU_ThangThien_1_KhiCong_HoThanKhiGiap = 0.0;
		NINJA_ThangThien_1_KhiCong_DaMaTrienThan = 0.0;
		ThangThien_1_KhiCong_LucPhachHoaSon = 0.0;
		ThangThien_1_KhiCong_TruongHong_QuanNhat = 0.0;
		ThangThien_1_KhiCong_KimChungCuongKhi = 0.0;
		ThangThien_1_KhiCong_VanKhiHanhTam = 0.0;
		ThangThien_1_KhiCong_ChinhBanBoiNguyen = 0.0;
		ThangThien_1_KhiCong_VanKhi_LieuThuong = 0.0;
		ThangThien_1_KhiCong_BachBien_ThanHanh = 0.0;
		ThangThien_1_KhiCong_CuongPhongThienY = 0.0;
		DAO_ThangThien_2_KhiCong_CungDoMatLo = 0.0;
		KIEM_ThangThien_2_KhiCong_ThienDiaDongTho = 0.0;
		THUONG_ThangThien_2_KhiCong_DiThoiViTien = 0.0;
		CUNG_ThangThien_2_KhiCong_ThienQuanApDa = 0.0;
		DAIPHU_VoTrungSinhHuu = 0.0;
		NINJA_ThangThien_2_KhiCong_ThuanThuyThoiChu = 0.0;
		CAMSU_ThangThien_1_KhiCong_PhiHoaDiemThuy = 0.0;
		CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet = 0.0;
		CAMSU_ThangThien_3_KhiCong_TuDaThuCa = 0.0;
		NINJA_DiNoHoanNo = 0.0;
		DAO_ManhLongSatTran = 0.0;
		KIEM_ThangThien_3_KhiCong_HoaPhuongLamTrieu = 0.0;
		THUONG_NoYChiHong = 0.0;
		CUNG_ThangThien_3_KhiCong_ThienNgoaiTamThi = 0.0;
		DAIPHU_ThangThien_3_KhiCong_MinhKinhChiThuy = 0.0;
		DamHoaLien_ThangThien_1_KhiCong_BaVuongQuyDienGiap = 0.0;
		DamHoaLien_ThangThien_3_KhiCong_HoaPhuongLamTrieu = 0.0;
		DamHoaLien_ThangThien_2_KhiCong_ThienDiaDongTho = 0.0;
		DamHoaLien_ThangThien_2_KhiCong_TungHoanhVoSong = 0.0;
		ThangThien_4_HongNguyetCuongPhong = 0.0;
		ThangThien_4_DocXaXuatDong = 0.0;
		ThangThien_4_ManNguyetCuongPhong = 0.0;
		ThangThien_4_LietNhatViemViem = 0.0;
		ThangThien_4_VongMaiThiemHoa = 0.0;
		ThangThien_4_HuyenTiChanMach = 0.0;
		ThangThien_4_TruongHongQuanThien = 0.0;
		ThangThien_4_AiHongBienDa = 0.0;
		ThangThien_5_TriTan = 0.0;
		ThangThien_5_PhaKhongTruyTinh = 0.0;
		ThangThien_5_MaHonChiLuc = 0.0;
		ThangThien_5_BatTu_ChiKhu = 0.0;
		ThangThien_5_DongLinhDienThuat = 0.0;
		ThangThien_5_ThienMaChiLuc = 0.0;
		ThangThien_5_LongTraoTiemChiThu = 0.0;
		ThangThien_5_NhatChieuSatThan = 0.0;
		ThangThien_5_HinhDiYeuTuong = 0.0;
		ThangThien_5_ThienLyNhatKich = 0.0;
		ThangThien_5_DietTheCuongVu = 0.0;
		ThangThien_5_KinhThienDongDia = 0.0;
		ThangThien_5_HoaLongPhapChieu = 0.0;
		ThangThien_1_LangKinhToiLuyen = 0.0;
		ThangThien_2_SatTinhQuangPhu = 0.0;
		ThangThien_3_KyQuan_QuanHung = 0.0;
		Kiem_ThangThien_6_BachDocBatXam = 0.0;
		Thuong_ThangThien_6_HanBangLinhVuc = 0.0;
		Cung_ThangThien_6_AcTanMuiTenNgheo = 0.0;
		DaiPhu_ThangThien_6_VanTamNguyetTinh = 0.0;
		Ninja_ThangThien_6_BenNgoaiVuaBenTrongVua = 0.0;
		CamSu_ThangThien_6_HuyetMachLenCao = 0.0;
		HanBaoQuan_ThangThien_6_ChanKhiHoanNguyen = 0.0;
		DamHoaLien_ThangThien_6_DienQuangSuongMai = 0.0;
		QuyenSu_ThangThien_6_KhongChuongNgaiVat = 0.0;
		MaiLieuChan_ThangThien_6_BienNguyThanhAn = 0.0;
		TuHao_ThangThien_6_BanNguocVoHieu = 0.0;
		ThanNu_ThangThien_6_ChongLaiThanPhap = 0.0;
		KCTT_16x_All_HuyetKhiPhuongCuong = 0.0;
		KCTT_16x_All_TinhKimBachLuyen = 0.0;
		Giam_Bot_CongKich = 0.0;
		Giam_Bot_CuongKhi = 0.0;
		NhanVat_WX_BUFF_SinhMenh = 0;
		NhanVat_WX_BUFF_CongKich = 0;
		NhanVat_WX_BUFF_PhongNgu = 0;
		NhanVat_WX_BUFF_NeTranh = 0;
		NhanVat_WX_BUFF_NoiCong = 0;
		NhanVat_WX_BUFF_TrungDich = 0;
		NhanVat_WX_BUFF_KhiCong = 0;
		CuongPhong_VanPha = 0.0;
		ThuongHaDieu_DemSo = 0;
		LowerRiverMonitor = 0;
		NgocLienHoan_DemSo = 0;
		FLD_CongKichTocDo = 100;
		HinhThuc_TangHinh = 0;
		ExplosiveState = 0.0;
		NINJA_LienTieuDaiDa_SoLuong = 0.0;
		CloseUp = 0;
		ThangThienVoCong_DiemSo = 0;
		FLD_NUMBER_OPEN = 0;
		ThangThienLichLuyen_KinhNghiem = 0;
		ThangThienLichLuyen_TangKinhNghiemNhanDuoc_HienTai = 0;
		LuuTinhManThien = 0.0;
		CongPhaNhuocDiem = 0.0;
		KhongGiPhaNoi = 0.0;
		LangKinhToiLuyen = 0.0;
		TuHao_PhaHuyenCuongPhong = 0.0;
		KyQuan_QuanHung = 0.0;
		ThanNu_SatTinhNghiaHo = 10.0;
		ThanNu_SatTinhNghiaSat = 10.0;
		ThanNu_VanKhiHanhTam = 0.0;
		ThanNu_ThaiCucTamPhap = 0.0;
		ThanNu_ThanLucKichPhat = 0.0;
		ThanNu_SatTinhNghiaKhi = 0.0;
		ThanNu_TayTuyDichCan = 0.0;
		ThanNu_HacHoaManKhai = 0.0;
		ThanNu_TruongCongKichLuc = 0.0;
		ThanNu_HacHoaTapTrung = 0.0;
		ThanNu_ChanVu_TuyetKich = 0.0;
		ThanNu_VanDocBatXam = 0.0;
		ThanNu_PhanNoDieuTiet = 0.0;
		ThanNu_CoDocGiaiTru = 0.0;
		ThanNu_ThanLucBaoHo = 0.0;
		ThanNu_ThiDocBaoPhat = 0.0;
		Player_Anti_Qigong_point = 0;
		Player_Level = 0;
		VoHuanGiaiDoan = 0;
	}

	public int NguongSo_NhipTim;
	~PlayersBes()
	{
	}

	public PlayersBes()
	{
		NguongSo_NhipTim = 3;
		DCH_HP_BONUS = 0;
		DCH_ATTACK_BONUS = 0;
		DCH_DEF_BONUS = 0;
		DCH_CLVC_BONUS = 0.0;
		DCH_ULPT_BONUS = 0.0;
		Event_TrangThai_Lock_DuongDua_F1 = 0;
		DCH_TrangThaiTangHinh = 0;
		TLC_TrangThaiCamPK = 0;
		DCH_TrangThai_UnCheck = 0;
		DCH_StackA = 0;
		DCH_StackB = 0;
		DCH_StackC = 0;
		Ninja_Hut_Dame = true;
		KCTT_16x_DaiPhu = true;
		Thuong_Auto_IceBlock = true;
		trangthai_miennhiem_debuff = false;
		offline_buff_1 = 0;
		offline_buff_2 = 0;
		offline_buff_3 = 0;
		offline_buff_4 = 0;
		offline_buff_5 = 0;
		offline_buff = false;
		DanhNhieuMucTieu_Loai_Bom_ThanNu = null;
		ThuocTinh_PhongAn = 0;
		Player_HP_Guild = 0;
		Player_MP_Guild = 0;
		Player_TanCong_Guild = 0;
		Player_PhongThu_Guild = 0;
		TruongHongQuanThien_ThemVao_Dame_Def = 0;
		TruongHongQuanThien_ThemVao_HP_MP = 0;
		ManNguyetCuongPhong_ThemVao_Dame_Def = 0;
		HongNguyetCuongPhong_ThemVao_Dame_Def = 0;
		FLD_NhanVat_ThemVao_CongKich_HP_MP = 0;
		CaoCotLieuThuong_ThemVao_Def = 0;
		CPVP_ThemVao_Dame = 0.0;
		CPVP_ThemVao_Def = 0.0;
		AoGuild_ThemVao_PhanTram_CongKich = 0.0;
		AoGuild_ThemVao_PhanTram_PhongThu = 0.0;
		FLD_ThanThu_ThemVao_PhanTramKinhNghiem = 0.0;
		FLD_ThanThu_ThemVao_PhanTramTraiNghiem = 0.0;
		FLD_TrangBi_ThemVao_DoiQuai_PhongNgu = 0;
		FLD_TrangBi_ThemVao_DoiQuai_CongKich = 0;
		FLD_TrangBi_ThemVao_DanhHieuHiepKhach_DoiQuai_PhongNgu = 0;
		FLD_TrangBi_ThuocTinhHoa_ThemVao_TangDame_QuaiVat = 0.0;
		Player_CLVC_Mau_ST = 0;
		Player_ULPT_Sam_ST = 0;
		Player_CLVC_Guild = 0.0;
		Player_ULPT_Guild = 0;
		Player_CLVC_VoHuan = 0.0;
		Player_ULPT_VoHuan = 0;
		Player_KhiCong_Guild = 0;
		Player_NeTranh_Guild = 0;
		Player_ChinhXac_Guild = 0;
		checkcomboskill_DamHoaLien = false;
		PhatDong_LoanPhuongHoaMinh = false;
		Player_VoDich = false;
		PKTuVong = false;
		ChayTron = false;
		TriggerMapMovementEvent = false;
		WhetherToUpdateTheConfiguration = false;
		Online = false;
		VuKhi_LucCongKich = 0;
		VuKhiTyLePhanTram_CongKichVoCong = 0.0;
		YPhuc_LucPhongNgu = 0;
		YPhuc_LucPhongNguVoCong_TiLePhanTram = 0.0;
		GiaoDichNhanVat_ID = 0;
		GiaoDichThaoTacD = 0;
		TempGameSecurityCode = string.Empty;
		StoreName = string.Empty;
		AnToan_MatMa_ThaoTacID = 0;
		WhetherTheSecurityCodeIsVerified = false;
		WhetherMarried = 0;
		HonNhan_NguoiDat_CauHoiDapAn = 2;
		GiaiTruQuanHe_Countdown = 0;
		NhanCuoiKhacChu = string.Empty;
		ClientSettings = string.Empty;
		HonorID_ = 0;
		PhaiChangMangTheoAoChang_HanhLy = false;
		NoKhi = false;
		Poisoning = false;
		NewMartialArtsCombos = [];
		EventBag = new Item[24];
		// SerList initialization removed - using World.ServerList
		Ninja_BatDauPhanThuongKyNang = false;
		Ninja_PhanThuongTanCongVatLy = 0.0;
		Ninja_MaPhap_TanCong_TangThem = 0.0;
		NhanVoHuan_MoiNgay = 0;
		MatDi_VoHuan = 0;
		lastX = 0f;
		lastY = 0f;
		lastMAP = 101;
		AttackX = 0f;
		AttackY = 0f;
		NumberOfAttacks = 0;
		CurrentlyActiveSkill_ID = 0;
		GioiHan_SoLan_Trung_BindAccount = 5;
		Gioi_han_so_lan_dung_SK1 = 10;
		RemainingTimeOfTrainingMap = 0;
		ActivityMapRemainingTime = 0;
		NgungThanBaoChau_ViTri = -1;
		FLD_Pet_ThemVao_CongKich = 0;
		FLD_Pet_ThemVao_PhongNgu = 0;
		FLD_Pet_ThemVao_LonNhatHP = 0;
		CungPhatDong_TanCongNhom = false;
		HanBaoQuan_ThienMaCuongHuyet_LucCongKich = 0.0;
		HanBaoQuan_ThienMaCuongHuyet_XSoLan = 0;
		ThangThienKhiCong_CapDoVoCong = 0.0;
		FLD_loveDegreeLevel = 0;
		FLD_canYouSendFlowers = true;
		CurrentOperationType = 0;
		NhanVatKhoa_Chat = false;
		Khoa_ChatTyLeCaCuocNhanVat = 0;
		NhanVatNeTranhSkill = 0;
		CharacterAdditionalCombatPower = 0;
		FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh = 0;
		FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh = 0;
		FLD_KetHonLeVat_ThemVaoThuocTinhThach = 0;
		GiaiTru_QuanHeThayTro_ThoiGian = DateTime.Parse("2012/12/12      12:12:12");
		MatDi_VoHuan = 0;
		VoHoang_ThongTe = 0;
		QuyenSu_KiemSoat_ComBo = 0;
		QuyenSu_HoiTamNhatKich_UyLuc = 0.3;
		DanhSach_TruyenThu = new();
		ThienDiaDongThoNhungLanNeTranh = 0;
		ThienDiaDongThoNeTranhTichLuyDame = 0;
		FLD_CamSu_HieuUng_SieuAmThanh_DameDef = 0.0;
		FLD_HonorID = 0;
		MartialArtsAttackPowerOfHusbandAndWifeMP = 0;
		MartialArtsAttackPowerOfHusbandAndWife = 0;
		HinhThuc_TangHinh = 0;
		EquipmentDataVersion = 1;
		ComprehensiveWarehouseEquipmentDataVersion = 1;
		Nhan_Vo_Huan_Online = DateTime.Now;
		PKhmtime = DateTime.Now;
		TMJCtime = DateTime.Now;
		XThmtime = DateTime.Now;
		FBtime = DateTime.Now;
		CWhmtime = DateTime.Now;
		SThmtime = DateTime.Now;
		Config = new();
		GiaoDich = new();
		YeuHoaThanhThao = false;
		NearbyNpcs = new();
		ListOfGroundItems = new();
		ThoLinhPhu_ToaDo = new();
		AttackList = new();
		NgocLienHoan = new();
		FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat = 0.0;
		CharactersToAddMax_HP = 0;
		CharactersToAddMax_HP_TTTP_1022 = 0;
		CharactersToAddMax_HP_TTTP_1023 = 0;
		CharactersToAddMax_HP_TTTP_1027 = 0;
		CharactersToAddMax_HP_TTTP_1028 = 0;
		CharactersToAddMax_MP = 0;
		CharactersToAddMax_MP_TTTP_1022 = 0;
		CharactersToAddMax_MP_TTTP_1023 = 0;
		CharactersToAddMax_MP_TTTP_1027 = 0;
		CharactersToAddMax_MP_TTTP_1028 = 0;
		CharacterIsBasicallyTheLargest_HP = 0;
		FLD_TrangBi_ThemVao_RecoveryMoney = 0;
		FLD_TrangBi_ThemVao_HP = 0;
		NhanVat_KhiCong_ThemVao_HP = 0;
		NhanVat_KhiCong_ThemVao_PhanTram_HP = 0;
		NhanVat_KhiCong_ThemVao_MP = 0;
		NhanVat_KhiCong_ThemVao_PhanTram_MP = 0;
		CharacterIsBasicallyTheLargest_MP = 0;
		CharacterIsBasicallyTheLargest_Barrier = 0;
		FLD_TrangBi_ThemVao_MP = 0;
		FLD_CongKichTocDo = 100;
		NhanVat_AP = 0;
		ExplosiveState = 0.0;
		NINJA_LienTieuDaiDa_SoLuong = 0.0;
		NINJA_LienTieuDaiDa = 0.0;
		ThangThienVoCong_DiemSo = 0;
		FLD_NUMBER_OPEN = 0;
		TeamingStage = 0;
		SafeMode = 0;
		CurrentlyOperatingNPC = 0;
		TitleRanking = 0;
		tracking_status_id1 = 0;
		MartialTitleType = 0;
	}
    }
