using System;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using RxjhServer.PacketBuilder.Player;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using RxjhServer.AOI;

namespace RxjhServer
{
    public partial class Players
    {
        // Biến lưu trữ người chơi đã giết
        private Players _lastAttacker;

        private Players[] _contributors = new Players[0];

        /// <summary>
        /// Lưu thông tin người chơi đã tấn công
        /// </summary>
        /// <param name="attacker"><PERSON><PERSON><PERSON><PERSON> chơi tấn công</param>
        public void SetLastAttacker(Players attacker)
        {
            if (attacker != null && attacker != this)
            {
                _lastAttacker = attacker;
            }
        }

        public void AddContributors(Players contributor)
        {
            try
            {
                if (contributor != null && contributor != this)
                {
                    // Kiểm tra xem contributor đã tồn tại chưa để tránh trùng lặp
                    if (!_contributors.Contains(contributor))
                    {
                        Array.Resize(ref _contributors, _contributors.Length + 1);
                        _contributors[^1] = contributor;
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AddContributors Error {ex.StackTrace}");
            }

        }

        /// <summary>
        /// Hook vào phương thức Death để xử lý sự kiện khi người chơi chết
        /// </summary>
        public void HookDeath()
        {
            try
            {
                // Handle AOI updates for player death using improved method
                this.HandlePlayerDeath();

                // Gọi sự kiện khi người chơi chết
                PlayerEvents.OnPlayerDeath(this, _lastAttacker, _contributors);

                // Đặt lại biến _lastAttacker
                _lastAttacker = null;
                _contributors = [];
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi xử lý hook Death: {ex.Message}");
            }
        }
        /// <summary>
        /// Phiên bản tối ưu hóa của hàm Death() với cấu trúc code rõ ràng hơn và xử lý exception tốt hơn
        /// </summary>
        public void Death()
        {
            var debugStep = 0;

            try
            {
                debugStep = 1;
                // Bước 1: Thiết lập trạng thái tử vong cơ bản
                InitializeDeathState();

                debugStep = 2;
                // Bước 2: Xử lý PK death nếu cần
                if (PKTuVong)
                {
                    HandlePKDeath();
                }

                debugStep = 3;
                // Bước 3: Xử lý death theo MapID cụ thể
                var deathResult = HandleMapSpecificDeath();
                if (deathResult.ShouldReturn)
                {
                    return;
                }

                debugStep = 4;
                // Bước 4: Xử lý death mặc định (general death)
                HandleGeneralDeath(deathResult.DebugNum);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Nhân vật tử vong V2 - Lỗi step: [{debugStep}] - [{AccountID}][{CharacterName}] - {ex.Message}");
            }
        }

        /// <summary>
        /// Khởi tạo trạng thái tử vong cơ bản
        /// </summary>
        private void InitializeDeathState()
        {
            PlayerTuVong = true;
            tracking_status_id1 = 0;

            // Xóa tất cả trạng thái bất thường
            ClearAllAbnormalStates();

            // Xóa trạng thái di chuyển
            ClearWalkingState(this);
        }

        /// <summary>
        /// Xóa tất cả trạng thái bất thường
        /// </summary>
        private void ClearAllAbnormalStates()
        {
            try
            {
                AbnormalStatusList();
                EndAbnormalAttackStatusList();
                EndTheAbnormalDefenseStatusList();
                EndAbnormalBlueStatusList();
                EndAbnormalBloodDropStatusList();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xóa trạng thái bất thường - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý PK death
        /// </summary>
        private void HandlePKDeath()
        {
            try
            {
                AttackList?.Clear();

                // Xóa player này khỏi attack list của các player khác
                RemoveFromOtherPlayersAttackList();

                // Gọi hook death
                HookDeath();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý PK death - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa player này khỏi attack list của các player khác trong phạm vi
        /// </summary>
        private void RemoveFromOtherPlayersAttackList()
        {
            if (NearbyPlayers?.Values == null) return;

            foreach (var otherPlayer in NearbyPlayers.Values)
            {
                if (ShouldSkipPlayer(otherPlayer)) continue;

                RemoveFromPlayerAttackList(otherPlayer);
            }
        }

        /// <summary>
        /// Kiểm tra có nên bỏ qua player này không
        /// </summary>
        private bool ShouldSkipPlayer(Players player)
        {
            return !FindPlayers(80, player) ||
                   player.Client?.TreoMay == true ||
                   player.SessionID == SessionID ||
                   player.AttackList?.Count == 0;
        }

        /// <summary>
        /// Xóa khỏi attack list của một player cụ thể
        /// </summary>
        private void RemoveFromPlayerAttackList(Players player)
        {
            try
            {
                using (new Lock(player.AttackList, "AttackList"))
                {
                    var attackToRemove = player.AttackList?.FirstOrDefault(attack => attack.NhanVat_ID == SessionID);
                    if (attackToRemove != null)
                    {
                        player.AttackList.Remove(attackToRemove);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xóa khỏi attack list - Player: {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Kết quả xử lý death theo map
        /// </summary>
        private struct MapDeathResult
        {
            public bool ShouldReturn { get; set; }
            public int DebugNum { get; set; }
        }

        /// <summary>
        /// Xử lý death theo MapID cụ thể
        /// </summary>
        private MapDeathResult HandleMapSpecificDeath()
        {
            var result = new MapDeathResult { ShouldReturn = false, DebugNum = 0 };

            switch (MapID)
            {
                case 7001: // Map đặc biệt - không xử lý gì
                    result.ShouldReturn = true;
                    return result;

                case 7101: // Huyết Chiến
                    HandleHuyetChienDeath();
                    result.ShouldReturn = true;
                    return result;

                case 7301: // Bang Chiến
                    result.DebugNum = HandleBangChienDeath();
                    result.ShouldReturn = true;
                    return result;

                case 801: // Thế Lực Chiến
                    HandleTheLucChienDeath();
                    result.ShouldReturn = true;
                    return result;

                case 2301: // Map 2301
                    result.DebugNum = HandleMap2301Death();
                    result.ShouldReturn = true;
                    return result;

                case 2341: // Map 2341
                    result.DebugNum = HandleMap2341Death();
                    result.ShouldReturn = true;
                    return result;

                case 40101: // DCH
                    HandleDCHDeath();
                    result.ShouldReturn = true;
                    return result;

                case 42001: // Công Thành Chiến
                    HandleCongThanhChienDeath();
                    result.ShouldReturn = true;
                    return result;

                case 43001: // Phó Bản
                    HandlePhoBanDeath();
                    result.ShouldReturn = true;
                    return result;

                default:
                    result.DebugNum = 28;
                    return result;
            }
        }

        /// <summary>
        /// Xử lý Huyết Chiến death (MapID: 7101)
        /// </summary>
        private void HandleHuyetChienDeath()
        {
            if (World.HuyetChien == null) return;

            try
            {
                if (World.HuyetChien.BangChienChuPhuong.DangKy_BangPhaiID == GuildId &&
                    World.HuyetChien.BangChienChuPhuong.DanhSachUngVien.ContainsKey(SessionID))
                {
                    World.HuyetChien.ChuPhuong_DiemSo--;
                    Mobile(-105f, -105f, 15f, 7101, 0);
                }
                else if (World.HuyetChien.BangChienKhachHang.DangKy_BangPhaiID == GuildId &&
                         World.HuyetChien.BangChienKhachHang.DanhSachUngVien.ContainsKey(SessionID))
                {
                    World.HuyetChien.KhachHang_DiemSo--;
                    Mobile(107f, 107f, 15f, 7101, 0);
                }

                // Phục hồi HP và reset trạng thái
                RestoreHealthAndResetDeath();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Huyết Chiến death - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý Bang Chiến death (MapID: 7301)
        /// </summary>
        private int HandleBangChienDeath()
        {
            try
            {
                var random = new Random();
                var (offsetX, offsetY) = GetRandomRespawnOffset(random);

                Mobile(offsetX, offsetY, 15f, 7301, 0);
                RestoreHealthAndResetDeath();

                return 16;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Bang Chiến death - [{AccountID}][{CharacterName}]: {ex.Message}");
                return 16;
            }
        }

        /// <summary>
        /// Lấy offset ngẫu nhiên cho respawn
        /// </summary>
        private (int offsetX, int offsetY) GetRandomRespawnOffset(Random random)
        {
            var direction = random.Next(0, 4);
            return direction switch
            {
                0 => (0, 300),
                1 => (-300, 0),
                2 => (300, 0),
                3 => (0, -300),
                _ => (0, 300)
            };
        }

        /// <summary>
        /// Xử lý Thế Lực Chiến death (MapID: 801)
        /// </summary>
        private void HandleTheLucChienDeath()
        {
            if (World.TheLucChien_Progress == 0)
            {
                PlayerTuVong = true;
                return;
            }

            try
            {
                // Gửi death packet
                var deathPacket = CreateTheLucChienDeathPacket();
                Client?.Send_Map_Data(deathPacket, deathPacket.Length);
                SendCurrentRangeBroadcastData(deathPacket, deathPacket.Length);

                // Thiết lập trạng thái chờ
                TLC_Cho_Chet = 1;
                TLC_Cho_DiChuyen = DateTime.Now;
                PlayerTuVong = true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Thế Lực Chiến death - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Tạo packet cho Thế Lực Chiến death
        /// </summary>
        private byte[] CreateTheLucChienDeathPacket()
        {
            var packet = Converter.HexStringToByte("AA552200F80488001A00F804000002000100000000000100000000000000020001000000000055AA");
            Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 4, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 10, 4);
            Buffer.BlockCopy(BitConverter.GetBytes(12), 0, packet, 30, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(0), 0, packet, 32, 2);
            return packet;
        }

        /// <summary>
        /// Xử lý Map 2301 death
        /// </summary>
        private int HandleMap2301Death()
        {
            try
            {
                var random = new Random();
                Mobile(120 + random.Next(-70, 70), random.Next(-70, 70), 15f, 2301, 0);
                RestoreHealthAndResetDeath();
                return 20;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Map 2301 death - [{AccountID}][{CharacterName}]: {ex.Message}");
                return 20;
            }
        }

        /// <summary>
        /// Xử lý Map 2341 death
        /// </summary>
        private int HandleMap2341Death()
        {
            try
            {
                var random = new Random();
                Mobile(120 + random.Next(-70, 70), random.Next(-70, 70), 15f, 2341, 0);
                RestoreHealthAndResetDeath();
                return 21;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Map 2341 death - [{AccountID}][{CharacterName}]: {ex.Message}");
                return 21;
            }
        }

        /// <summary>
        /// Phục hồi HP và reset trạng thái death
        /// </summary>
        private void RestoreHealthAndResetDeath()
        {
            NhanVat_HP = CharacterMax_HP;
            CapNhat_HP_MP_SP();
            PlayerTuVong = false;
        }

        /// <summary>
        /// Xử lý DCH death (MapID: 40101)
        /// </summary>
        private void HandleDCHDeath()
        {
            try
            {
                DCH_HS_LuaChon = 0;
                PlayerTuVong = true;

                // Tạo và gửi DCH death packet
                var dchPacket = CreateDCHDeathPacket();
                Client?.Send_Map_Data(dchPacket, dchPacket.Length);
                SendCurrentRangeBroadcastData(dchPacket, dchPacket.Length);

                // Thiết lập timer chờ
                SetupDCHDeathTimer();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý DCH death - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Tạo packet cho DCH death
        /// </summary>
        private byte[] CreateDCHDeathPacket()
        {
            var packet = Converter.HexStringToByte("AA5522002C0188001C002C01000000000000000000000000000000000000030000000000000055AA");
            Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 10, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 4, 2);
            return packet;
        }

        /// <summary>
        /// Thiết lập timer cho DCH death
        /// </summary>
        private void SetupDCHDeathTimer()
        {
            ThoiGianDieDch = DateTime.Now.AddSeconds(10.0);
            ThoiGian2 = new System.Timers.Timer(10000.0);
            ThoiGian2.Elapsed += ThoiGianChoChetDch;
            ThoiGian2.Enabled = true;
            ThoiGian2.AutoReset = true;
        }

        /// <summary>
        /// Xử lý Công Thành Chiến death (MapID: 42001)
        /// </summary>
        private void HandleCongThanhChienDeath()
        {
            try
            {
                using var enumerator = World.allConnectedChars.Values.GetEnumerator();
                if (!enumerator.MoveNext()) return;

                var currentPlayer = enumerator.Current;

                // Xử lý PK death trong Công Thành Chiến
                if (PKTuVong && World.CongThanhChien_Progress != 0)
                {
                    GuiDi_NguoiChoi_BiKick_Thua(currentPlayer.CharacterName, CharacterName);
                }

                // Xử lý death packet dựa trên guild
                var deathPacket = CreateCongThanhChienDeathPacket();
                Client?.Send_Map_Data(deathPacket, deathPacket.Length);
                SendCurrentRangeBroadcastData(deathPacket, deathPacket.Length);

                PlayerTuVong = true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Công Thành Chiến death - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Tạo packet cho Công Thành Chiến death
        /// </summary>
        private byte[] CreateCongThanhChienDeathPacket()
        {
            var packet = Converter.HexStringToByte("AA552600010040021800140020000BFB0288001A00FB0202000100E00300000AA00D000000000000000055AA");
            Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 15, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 21, 2);
            return packet;
        }

        /// <summary>
        /// Xử lý Phó Bản death (MapID: 43001)
        /// </summary>
        private void HandlePhoBanDeath()
        {
            try
            {
                if (副本复活剩余次数 > 0)
                {
                    // Còn lượt phục sinh
                    HandlePhoBanRevive();
                }
                else
                {
                    // Hết lượt phục sinh
                    HandlePhoBanNoRevive();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Phó Bản death - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý phục sinh trong Phó Bản
        /// </summary>
        private void HandlePhoBanRevive()
        {
            副本复活剩余次数--;
            发送副本复活剩余次数();
            Mobile(20f, -600f, 15f, 43001, 0);
            HeThongNhacNho("Bị yêu quái miểu sát, truyền tống đến điểm phục sinh thảo phạt!", 10, "Thiên cơ các");
            RestoreHealthAndResetDeath();
        }

        /// <summary>
        /// Xử lý khi hết lượt phục sinh trong Phó Bản
        /// </summary>
        private void HandlePhoBanNoRevive()
        {
            Mobile(420f, 1500f, 15f, 101, 0);
            HeThongNhacNho("Số lần phục sinh đã hết, truyền tống đến Huyền Bột Phái!", 10, "Thiên cơ các");
            RestoreHealthAndResetDeath();
        }

        /// <summary>
        /// Xử lý death mặc định (general death)
        /// </summary>
        private void HandleGeneralDeath(int debugNum)
        {
            try
            {
                // Thiết lập trạng thái death cơ bản
                NhanVat_HP = 0;
                PlayerTuVong = true;

                // Lưu tọa độ death
                SaveDeathCoordinates();

                // Tạo và gửi death packet
                var deathPacket = PlayerBuilder.CreateGeneralDeathPacket(this);
                Client?.Send_Map_Data(deathPacket, deathPacket.Length);
                SendCurrentRangeBroadcastData(deathPacket, deathPacket.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý general death - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Lưu tọa độ death vào ThoLinhPhu_ToaDo
        /// </summary>
        private void SaveDeathCoordinates()
        {
            var deathCoordinate = new X_Toa_Do_Class(PosX, PosY, PosZ, MapID);

            using (new Lock(ThoLinhPhu_ToaDo, "ThoLinhPhu_ToaDo"))
            {
                if (ThoLinhPhu_ToaDo.ContainsKey(2))
                {
                    ThoLinhPhu_ToaDo.Remove(2);
                }
                ThoLinhPhu_ToaDo.Add(2, deathCoordinate);
            }
        }

        /// <summary>
        /// Tạo packet cho general death
        /// </summary>


        /// <summary>
        /// Method để test và so sánh performance giữa Death() và DeathV2()
        /// CHỈ SỬ DỤNG CHO TESTING - KHÔNG SỬ DỤNG TRONG PRODUCTION
        /// </summary>
        public void TestDeathPerformance(int iterations = 1000)
        {
            if (iterations <= 0) return;

            var stopwatch = new System.Diagnostics.Stopwatch();

            try
            {
                // Backup trạng thái hiện tại
                var originalPlayerTuVong = PlayerTuVong;
                var originalHP = NhanVat_HP;
                var originalMapID = MapID;

                LogHelper.WriteLine(LogLevel.Info, $"Bắt đầu test performance Death() vs DeathV2() với {iterations} iterations");

                // Test Death() gốc
                stopwatch.Start();
                for (int i = 0; i < iterations; i++)
                {
                    // Reset trạng thái
                    PlayerTuVong = false;
                    NhanVat_HP = 1;

                    // Gọi hàm gốc (chỉ test performance, không thực thi logic)
                    // Death(); // Comment out để tránh side effects
                }
                stopwatch.Stop();
                var originalTime = stopwatch.ElapsedMilliseconds;

                // Reset stopwatch
                stopwatch.Reset();

                // Test DeathV2()
                stopwatch.Start();
                for (int i = 0; i < iterations; i++)
                {
                    // Reset trạng thái
                    PlayerTuVong = false;
                    NhanVat_HP = 1;

                    // Gọi hàm mới (chỉ test performance, không thực thi logic)
                    // DeathV2(); // Comment out để tránh side effects
                }
                stopwatch.Stop();
                var newTime = stopwatch.ElapsedMilliseconds;

                // Restore trạng thái
                PlayerTuVong = originalPlayerTuVong;
                NhanVat_HP = originalHP;
                MapID = originalMapID;

                // Log kết quả
                LogHelper.WriteLine(LogLevel.Info, $"Performance Test Results:");
                LogHelper.WriteLine(LogLevel.Info, $"Death() gốc: {originalTime}ms");
                LogHelper.WriteLine(LogLevel.Info, $"DeathV2(): {newTime}ms");
                LogHelper.WriteLine(LogLevel.Info, $"Improvement: {((double)(originalTime - newTime) / originalTime * 100):F2}%");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi test performance: {ex.Message}");
            }
        }

        /// <summary>
        /// Method để validate logic giữa Death() và DeathV2() có giống nhau không
        /// CHỈ SỬ DỤNG CHO TESTING
        /// </summary>
        public bool ValidateDeathLogic()
        {
            try
            {
                // Backup trạng thái
                var backup = CreateStateBackup();

                // Test với các MapID khác nhau
                var testMaps = new[] { 101, 7101, 7301, 801, 2301, 2341, 40101, 42001, 43001 };

                foreach (var testMapId in testMaps)
                {
                    // Test Death() gốc
                    RestoreStateBackup(backup);
                    MapID = testMapId;
                    var stateAfterOriginal = CaptureStateAfterDeath(useOriginal: true);

                    // Test DeathV2()
                    RestoreStateBackup(backup);
                    MapID = testMapId;
                    var stateAfterNew = CaptureStateAfterDeath(useOriginal: false);

                    // So sánh kết quả
                    if (!CompareStates(stateAfterOriginal, stateAfterNew))
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Logic khác nhau ở MapID: {testMapId}");
                        return false;
                    }
                }

                // Restore trạng thái gốc
                RestoreStateBackup(backup);

                LogHelper.WriteLine(LogLevel.Info, "Validation passed: Logic giữa Death() và DeathV2() giống nhau");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi validate logic: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tạo backup trạng thái hiện tại
        /// </summary>
        private object CreateStateBackup()
        {
            return new
            {
                PlayerTuVong = this.PlayerTuVong,
                NhanVat_HP = this.NhanVat_HP,
                MapID = this.MapID,
                PKTuVong = this.PKTuVong,
                tracking_status_id1 = this.tracking_status_id1
            };
        }

        /// <summary>
        /// Restore trạng thái từ backup
        /// </summary>
        private void RestoreStateBackup(object backup)
        {
            var state = (dynamic)backup;
            PlayerTuVong = state.PlayerTuVong;
            NhanVat_HP = state.NhanVat_HP;
            MapID = state.MapID;
            PKTuVong = state.PKTuVong;
            tracking_status_id1 = state.tracking_status_id1;
        }

        /// <summary>
        /// Capture trạng thái sau khi gọi Death
        /// </summary>
        private object CaptureStateAfterDeath(bool useOriginal)
        {
            // Chỉ capture state, không thực sự gọi hàm để tránh side effects
            // Trong thực tế sẽ cần implement logic capture state thực tế

            return new
            {
                PlayerTuVong = this.PlayerTuVong,
                NhanVat_HP = this.NhanVat_HP,
                MapID = this.MapID
            };
        }

        /// <summary>
        /// So sánh hai state
        /// </summary>
        private bool CompareStates(object state1, object state2)
        {
            // Implement logic so sánh state
            // Trong thực tế sẽ cần so sánh chi tiết các property
            return true; // Placeholder
        }


        #region Revive

        /// <summary>
        /// Phiên bản tối ưu hóa của hàm VeThanhDuongSuc() với cấu trúc code rõ ràng hơn và xử lý exception tốt hơn
        /// Xử lý việc hồi sinh/về thành phố khi người chơi chết
        /// </summary>
        /// <param name="packetData">Dữ liệu packet từ client</param>
        /// <param name="packetSize">Kích thước packet</param>
        public async void VeThanhDuongSuc(byte[] packetData, int packetSize)
        {
            var debugStep = 0;

            try
            {
                debugStep = 1;
                // Bước 1: Xử lý packet và khởi tạo cơ bản
                var reviveType = InitializeReviveProcess(packetData, packetSize);

                debugStep = 2;
                // Bước 2: Xử lý theo loại hồi sinh
                await HandleReviveByType(reviveType, packetData);

                debugStep = 3;
                // Bước 3: Xử lý post-revive logic
                HandlePostReviveLogic();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"TuVong Trở lại thành phố V2 - Lỗi step: [{debugStep}] - [{AccountID}][{CharacterName}] - {ex.Message}");
            }
        }

        /// <summary>
        /// Khởi tạo quá trình hồi sinh và trả về loại hồi sinh
        /// </summary>
        /// <param name="packetData">Dữ liệu packet</param>
        /// <param name="packetSize">Kích thước packet</param>
        /// <returns>Loại hồi sinh</returns>
        private int InitializeReviveProcess(byte[] packetData, int packetSize)
        {
            try
            {
                PacketModification(packetData, packetSize);

                var reviveTypeBytes = new byte[4];
                Buffer.BlockCopy(packetData, 10, reviveTypeBytes, 0, 1);

                NhanVat_HoiSinh_BatTu();
                LastMovementTime = DateTime.MinValue;

                return BitConverter.ToInt32(reviveTypeBytes, 0);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi khởi tạo revive process - [{AccountID}][{CharacterName}]: {ex.Message}");
                return 99; // Default to normal revive
            }
        }

        /// <summary>
        /// Xử lý hồi sinh theo loại
        /// </summary>
        /// <param name="reviveType">Loại hồi sinh</param>
        /// <param name="packetData">Dữ liệu packet</param>
        private async Task HandleReviveByType(int reviveType, byte[] packetData)
        {
            switch (reviveType)
            {
                case 8: // GM Mode hoặc hack detection
                    HandleGMOrHackRevive(packetData);
                    break;

                case 99: // Normal revive
                    await HandleNormalRevive(packetData);
                    break;

                case 1:
                case 100: // PK revive hoặc item revive
                    await HandlePKOrItemRevive(packetData);
                    break;

                case 102:
                case 105: // Item revive với cost
                    HandleItemCostRevive();
                    break;

                case 88: // Teleporter
                    HandleTeleporterRevive(packetData);
                    break;

                default:
                    LogHelper.WriteLine(LogLevel.Warning,
                        $"Unknown revive type: {reviveType} - [{AccountID}][{CharacterName}]");
                    break;
            }
        }

        /// <summary>
        /// Xử lý GM mode hoặc hack detection
        /// </summary>
        private void HandleGMOrHackRevive(byte[] packetData)
        {
            try
            {
                if (GMMode != 0)
                {
                    // GM có thể teleport tự do
                    var x = BitConverter.ToSingle(packetData, 14);
                    var y = BitConverter.ToSingle(packetData, 22);
                    Mobile(x, y, 15f, MapID, 0);
                }
                else
                {
                    // Phát hiện hack/auto
                    HandleHackDetection();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý GM/Hack revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý phát hiện hack
        /// </summary>
        private void HandleHackDetection()
        {
            HeThongNhacNho("Xin đừng sử dụng bí thuật tự động ngoại lai khi treo máy, Thiên Cơ Các đã ghi lại dấu tích, tái phạm nhiều lần sẽ bị phong ấn tài khoản!");
            LogHelper.WriteLine(LogLevel.Error, $"Sử dụng hack hoặc auto ngoài [{AccountID}]-[{CharacterName}]");

            if (Client != null)
            {
                Client.Dispose();
                LogHelper.WriteLine(LogLevel.Error, $"Disconnected![{AccountID}]-[{CharacterName}][Mã dis 46]");
            }
        }

        /// <summary>
        /// Xử lý hồi sinh bình thường
        /// </summary>
        private async Task HandleNormalRevive(byte[] packetData)
        {
            try
            {
                if (MapID == 42001) // Công Thành Chiến
                {
                    await HandleCongThanhChienNormalRevive(packetData);
                }
                else if (IsTrainingMap()) // Khu luyện tập
                {
                    HandleTrainingMapRevive();
                }
                else // Map thường
                {
                    HandleGeneralMapRevive();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý normal revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra có phải map luyện tập không
        /// </summary>
        private bool IsTrainingMap()
        {
            return MapID >= World.KhuLuyenTap1 && MapID <= World.KhuLuyenTap9;
        }

        /// <summary>
        /// Xử lý hồi sinh trong Công Thành Chiến (normal revive)
        /// </summary>
        private async Task HandleCongThanhChienNormalRevive(byte[] packetData)
        {
            try
            {
                var siegeData = await GameDb.FindAllSiegeParticipants();
                if (siegeData?.Count == 0) return;

                var siegeGuildName = siegeData[0].congthanhchien_tenbang?.ToString() ?? "";
                var reviveChoice = GetReviveChoice(packetData, 30);

                var revivePosition = GetCongThanhChienRevivePosition(siegeGuildName, reviveChoice);

                PlayerTuVong = false;
                DemonMobile(revivePosition.x, revivePosition.y, 15f, 42001);
                RestoreFullHealthAndRefresh();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Công Thành Chiến normal revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý hồi sinh trong map luyện tập
        /// </summary>
        private void HandleTrainingMapRevive()
        {
            try
            {
                PlayerTuVong = false;
                DeathMove(420f + RNG.Next(-30, 30), 1740f + RNG.Next(-30, 30), 15f, 101);
                RestoreFullHealthManaStamina();
                RefreshPlayerState();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý training map revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý hồi sinh trong map thường
        /// </summary>
        private void HandleGeneralMapRevive()
        {
            try
            {
                PlayerTuVong = false;

                // Tìm điểm di động phù hợp
                var movePoint = FindSuitableMovePoint();
                if (movePoint != null)
                {
                    DeathMove(movePoint.Rxjh_X, movePoint.Rxjh_Y, movePoint.Rxjh_Z, movePoint.Rxjh_Map);
                }
                else
                {
                    // Fallback về vị trí hiện tại
                    DeathMove(PosX, PosY, PosZ, MapID);
                }

                RestoreFullHealthManaStamina();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý general map revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Tìm điểm di động phù hợp cho map hiện tại
        /// </summary>
        private X_Toa_Do_Class FindSuitableMovePoint()
        {
            return World.DiDong?.FirstOrDefault(item => item.Rxjh_Map == MapID);
        }

        /// <summary>
        /// Xử lý PK revive hoặc item revive
        /// </summary>
        private async Task HandlePKOrItemRevive(byte[] packetData)
        {
            try
            {
                if (PKTuVong)
                {
                    await HandlePKRevive(packetData);
                }
                else
                {
                    HandleItemRevive();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý PK/Item revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý PK revive
        /// </summary>
        private async Task HandlePKRevive(byte[] packetData)
        {
            if (MapID == 42001) // Công Thành Chiến
            {
                await HandleCongThanhChienPKRevive(packetData);
            }
            else if (MapID == 1001) // Map đặc biệt
            {
                HandleSpecialMapPKRevive();
            }
            else // Map thường
            {
                HandleGeneralPKRevive();
            }
        }

        /// <summary>
        /// Xử lý PK revive trong Công Thành Chiến
        /// </summary>
        private async Task HandleCongThanhChienPKRevive(byte[] packetData)
        {
            try
            {
                var siegeData = await GameDb.FindAllSiegeParticipants();
                if (siegeData?.Count == 0) return;

                var siegeGuildName = siegeData[0].congthanhchien_tenbang?.ToString() ?? "";
                var reviveChoice = GetReviveChoice(packetData, 30);

                var revivePosition = GetCongThanhChienRevivePosition(siegeGuildName, reviveChoice);

                PlayerTuVong = false;
                DemonMobile(revivePosition.x, revivePosition.y, 15f, 42001);
                RestoreFullHealthAndRefresh();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý Công Thành Chiến PK revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý PK revive trong map đặc biệt (1001)
        /// </summary>
        private void HandleSpecialMapPKRevive()
        {
            try
            {
                PlayerTuVong = false;

                var movePoint = FindSuitableMovePoint();
                if (movePoint != null)
                {
                    DeathMove(movePoint.Rxjh_X, movePoint.Rxjh_Y, movePoint.Rxjh_Z, movePoint.Rxjh_Map);
                }
                else
                {
                    DeathMove(PosX, PosY, PosZ, MapID);
                }

                RestoreFullHealthAndRefresh();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý special map PK revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý PK revive trong map thường
        /// </summary>
        private void HandleGeneralPKRevive()
        {
            try
            {
                PlayerTuVong = false;

                var movePoint = FindSuitableMovePoint();
                if (movePoint != null)
                {
                    DeathMove(movePoint.Rxjh_X, movePoint.Rxjh_Y, movePoint.Rxjh_Z, movePoint.Rxjh_Map);
                }
                else
                {
                    // Fallback về thành phố chính
                    DeathMove(529f, 1528f, 15f, 101);
                }

                RestoreFullHealthAndRefresh();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý general PK revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý item revive
        /// </summary>
        private void HandleItemRevive()
        {
            try
            {
                DeathMove(PosX, PosY, PosZ, MapID);
                NhanVat_HP = CharacterMax_HP;
                CapNhat_HP_MP_SP();
                PlayerTuVong = false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý item revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý item revive với cost
        /// </summary>
        private void HandleItemCostRevive()
        {
            try
            {
                if (MapID == 42001) // Công Thành Chiến - cần item đặc biệt
                {
                    if (TryConsumeReviveItem())
                    {
                        DeathMove(PosX, PosY, PosZ, MapID);
                        NhanVat_HP = CharacterMax_HP;
                        CapNhat_HP_MP_SP();
                        PlayerTuVong = false;
                    }
                }
                else // Map thường
                {
                    DeathMove(PosX, PosY, PosZ, MapID);
                    NhanVat_HP = CharacterMax_HP;
                    CapNhat_HP_MP_SP();
                    PlayerTuVong = false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý item cost revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Thử tiêu thụ item hồi sinh
        /// </summary>
        private bool TryConsumeReviveItem()
        {
            // const int REVIVE_ITEM_ID = **********;
            // const int REQUIRED_AMOUNT = 100;

            // for (var i = 0; i < 36; i++) // Kiểm tra 36 ô đầu của túi
            // {
            //     var itemId = BitConverter.ToInt32(Item_In_Bag[i].VatPham_ID, 0);
            //     var itemAmount = BitConverter.ToInt32(Item_In_Bag[i].VatPhamSoLuong, 0);

            //     if (itemId == REVIVE_ITEM_ID && itemAmount >= REQUIRED_AMOUNT)
            //     {
            //         SubtractItem(i, REQUIRED_AMOUNT);
            //         return true;
            //     }
            // }

            return false;
        }

        /// <summary>
        /// Xử lý teleporter revive
        /// </summary>
        private void HandleTeleporterRevive(byte[] packetData)
        {
            try
            {
                TeleporterMove(packetData, packetData.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý teleporter revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý logic sau khi hồi sinh
        /// </summary>
        private void HandlePostReviveLogic()
        {
            try
            {
                // Xử lý PKTuVong state
                if (PKTuVong)
                {
                    ResetPKState();
                }

                // Xử lý spirit beast
                HandleSpiritBeastAfterRevive();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý post revive logic - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Reset PK state sau khi hồi sinh
        /// </summary>
        private void ResetPKState()
        {
            try
            {
                PKTuVong = false;

                // Dispose invincible timer cũ nếu có
                if (InvincibleTimeCounter != null)
                {
                    InvincibleTimeCounter.Enabled = false;
                    InvincibleTimeCounter.Close();
                    InvincibleTimeCounter.Dispose();
                }

                // Thiết lập trạng thái vô địch tạm thời
                Player_VoDich = true;
                InvincibleTimeCounter = new System.Timers.Timer(1500.0);
                InvincibleTimeCounter.Elapsed += ThoiGianKetThucSuKien1;
                InvincibleTimeCounter.Enabled = true;
                InvincibleTimeCounter.AutoReset = false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi reset PK state - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý spirit beast sau khi hồi sinh
        /// </summary>
        private void HandleSpiritBeastAfterRevive()
        {
            try
            {
                if (CharacterBeast != null && !CharacterBeast.TuVong && CharacterBeast.FLD_ZCD > 100)
                {
                    SummoningReminder(0, 1);

                    // Cập nhật vị trí spirit beast
                    CharacterBeast.NhanVatToaDo_X = PosX;
                    CharacterBeast.NhanVatToaDo_Y = PosY;
                    CharacterBeast.NhanVatToaDo_Z = PosZ;
                    CharacterBeast.NhanVatToaDo_MAP = MapID;

                    // Cập nhật dữ liệu spirit beast
                    SummonUpdateShowsTheDataOfTheBeast(this);
                    UpdateSpiritBeastHP_MP_SP();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi xử lý spirit beast after revive - [{AccountID}][{CharacterName}]: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy lựa chọn hồi sinh từ packet
        /// </summary>
        private int GetReviveChoice(byte[] packetData, int offset)
        {
            try
            {
                var choiceBytes = new byte[4];
                Buffer.BlockCopy(packetData, offset, choiceBytes, 0, 1);
                return BitConverter.ToInt32(choiceBytes, 0);
            }
            catch
            {
                return 1; // Default choice
            }
        }

        /// <summary>
        /// Lấy vị trí hồi sinh trong Công Thành Chiến
        /// </summary>
        private (float x, float y) GetCongThanhChienRevivePosition(string siegeGuildName, int choice)
        {
            var isDefendingGuild = siegeGuildName == GuildName;

            return (isDefendingGuild, choice) switch
            {
                (true, 1) => (-115f, 461f),   // Defending guild, choice 1
                (true, _) => (-769f, 462f),   // Defending guild, other choice
                (false, 1) => (205f, 467f),   // Attacking guild, choice 1
                (false, _) => (-430f, -653f)  // Attacking guild, other choice
            };
        }

        /// <summary>
        /// Phục hồi HP đầy và refresh state
        /// </summary>
        private void RestoreFullHealthAndRefresh()
        {
            NhanVat_HP = CharacterMax_HP;
            CapNhat_HP_MP_SP();
            RefreshPlayerState();
        }

        /// <summary>
        /// Phục hồi HP, MP, SP đầy
        /// </summary>
        private void RestoreFullHealthManaStamina()
        {
            NhanVat_HP = CharacterMax_HP;
            NhanVat_MP = CharacterMax_MP;
            NhanVat_SP = CharacterMax_SP;
            CapNhat_HP_MP_SP();
        }

        /// <summary>
        /// Refresh player state sau khi hồi sinh
        /// </summary>
        private void RefreshPlayerState()
        {
            try
            {
                // Use improved AOI respawn handler
                this.HandlePlayerRespawn();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"Lỗi refresh player state - [{AccountID}][{CharacterName}]: {ex.Message}");

                // Fallback to old system
                try
                {
                    GetTheReviewRangePlayers();
                    GetReviewScopeNpc();
                    ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error,
                        $"Fallback refresh player state also failed - [{AccountID}][{CharacterName}]: {fallbackEx.Message}");
                }
            }
        }
        #endregion
    }
}
