﻿using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer;

public partial class Players
{
    	public bool FindPlayerByRange(int far, Players playe)
	{
		if (playe.MapID != MapID)
		{
			return false;
		}
		var num = playe.PosX - PosX;
		var num2 = playe.PosY - PosY;
		return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= far;
	}

	public virtual void GetTheReviewRangePlayers(bool skipBS = false)
	{
		var num = 0;
		try
		{


			if (NearbyPlayers == null || Client.TreoMay)
			{
				return;
			}

			// Use AOI system if enabled for this map
			// if (AOI.// AOIConfiguration.Instance. ShouldUseAOI(MapID))
			// {
				GetTheReviewRangePlayers_AOI();
				return;
			// }

			// Fallback to old system
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Get Players tại num: [" + num + "] - [" + AccountID + "][" + CharacterName + "] - " + ex.Message);
		}
	}

	/// <summary>
	/// AOI-based implementation of GetTheReviewRangePlayers
	/// Uses spatial partitioning for efficient player discovery
	/// </summary>
	private void GetTheReviewRangePlayers_AOI()
	{
		try
		{
			// Get players in AOI range using the AOI system
			var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(MapID,PosX, PosY);

			// Track players to remove from PlayerList
			var playersToRemove = new List<(int, int)>();

			// First, check existing players in PlayerList to see if they're still in range
			foreach (var kvp in NearbyPlayers.ToList())
			{
				var (serverID, sessionID) = kvp.Key;
				var player = kvp.Value;

				if (player.Client == null || !player.Client.Running || !FindPlayers(400, player))
				{
					playersToRemove.Add((serverID, sessionID));
					NotifyPlayerExit(this, player);
					player.NearbyPlayers.TryRemove((World.ServerID, SessionID), out _);
					NotifyPlayerExit(player, this);
				}
			}

			// Remove players that are no longer in range
			foreach (var key in playersToRemove)
			{
				NearbyPlayers.TryRemove(key, out _);
			}

			// Add new players from AOI grids
			foreach (var grid in aoiGrids)
			{
				foreach (var player in grid.GetPlayers())
				{
					if (player.Client == null || !player.Client.Running || player.SessionID == SessionID)
					{
						continue;
					}

					if (FindPlayers(400, player))
					{
						// Add to this player's PlayerList
						if (!NearbyPlayers.ContainsKey((World.ServerID, player.SessionID)) && MapID == player.MapID)
						{
							NearbyPlayers.TryAdd((World.ServerID, player.SessionID), player);
							UpdateCharacterData(player);
						}

						// Add this player to the other player's PlayerList
						if (!player.NearbyPlayers.ContainsKey((World.ServerID, SessionID)))
						{
							player.NearbyPlayers.TryAdd((World.ServerID, SessionID), this);
							player.UpdateCharacterData(this);
						}

						// Handle GM mode visibility
						if (player.GMMode != 0 && player.HinhThuc_TangHinh != 0)
						{
							player.HinhThuc_TangHinh = 1;
							player.CheDo_TangHinh(1);
						}
						if (GMMode != 0 && HinhThuc_TangHinh != 0)
						{
							HinhThuc_TangHinh = 1;
							CheDo_TangHinh(1);
						}

						// Handle special game mode interactions
						HandleDaiChienHon(player);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"AOI GetTheReviewRangePlayers error for player [{AccountID}]-[{CharacterName}]: {ex.Message}");

			// Fallback to old system on error
			try
			{
				var queue = Queue.Synchronized(new Queue());
				foreach (var value in World.allConnectedChars.Values)
				{
					queue.Enqueue(value);
				}
				while (queue.Count > 0)
				{
					var players = (Players)queue.Dequeue();
					if (players?.Client == null || !players.Client.Running || players.SessionID == SessionID)
					{
						continue;
					}
					if (FindPlayers(400, players))
					{
						if (!NearbyPlayers.ContainsKey((World.ServerID, players.SessionID)) && MapID == players.MapID)
						{
							NearbyPlayers.TryAdd((World.ServerID, players.SessionID), players);
							UpdateCharacterData(players);
						}
						if (!players.NearbyPlayers.ContainsKey((World.ServerID, SessionID)))
						{
							players.NearbyPlayers.TryAdd((World.ServerID, SessionID), this);
							players.UpdateCharacterData(this);
						}
						HandleDaiChienHon(players);
					}
				}
			}
			catch (Exception fallbackEx)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Fallback GetTheReviewRangePlayers also failed: {fallbackEx.Message}");
			}
		}
	}

	private void HandleDaiChienHon(Players players)
	{
		if (MapID == 40101 && World.DCH_Progress <= 3 && players.DCH_TrangThai_UnCheck != 0)
		{
			if (Player_Zx == 1)
			{
				DCH_DOIXANH_EFFECT_FIRSTGAME(this, players, DCH_TrangThaiTangHinh);
			}
			else
			{
				DCH_DOIDO_EFFECT_FIRSTGAME(this, players, DCH_TrangThaiTangHinh);
			}
			if (players.Player_Zx == 1)
			{
				players.DCH_DOIXANH_EFFECT_FIRSTGAME(players, this, players.DCH_TrangThaiTangHinh);
			}
			else
			{
				players.DCH_DOIDO_EFFECT_FIRSTGAME(players, this, players.DCH_TrangThaiTangHinh);
			}
			if (DCH_CheckTrangThaiTangHinh == 1)
			{
				if (Player_Zx == 1)
				{
					DCH_DOIXANH_ONETOONE(this, players, DCH_CheckTrangThaiTangHinh);
				}
				else if (Player_Zx == 2)
				{
					DCH_DOIDO_ONETOONE(this, players, DCH_CheckTrangThaiTangHinh);
				}
			}
			if (players.DCH_CheckTrangThaiTangHinh == 1)
			{
				if (players.Player_Zx == 1)
				{
					players.DCH_DOIXANH_ONETOONE(players, this, players.DCH_CheckTrangThaiTangHinh);
				}
				else if (players.Player_Zx == 2)
				{
					players.DCH_DOIDO_ONETOONE(players, this, players.DCH_CheckTrangThaiTangHinh);
				}
			}
		}
	}

	public bool LookInNpc(int far, NpcClass npc)
	{
		if (npc.Rxjh_Map != MapID)
		{
			return false;
		}
		if (MapID == 7101)
		{
			far = 1000;
		}
		var num = npc.Rxjh_X - PosX;
		var num2 = npc.Rxjh_Y - PosY;
		var isInRange = (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far;
		if (!isInRange)
		{
			return false;
		}
		return true;
	}

	public bool LookInNpc(int npcid, int far)
	{
		NpcClass npcClass = null;
		foreach (var value in NearbyNpcs.Values)
		{
			if (value.FLD_PID == npcid)
			{
				npcClass = value;
				break;
			}
		}
		if (npcClass != null && npcClass.Rxjh_Map == MapID)
		{
			var num = npcClass.Rxjh_X - PosX;
			var num2 = npcClass.Rxjh_Y - PosY;
			bool isInRange = (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far;
			if (!isInRange)
			{
				return false;
			}
			return true;
		}
		return false;
	}

	public virtual void GetReviewScopeNpc(bool skipBS = false)
	{
		var num = 0;
		try
		{
			// CRITICAL DEBUG: Log NpcList state before processing
			LogHelper.WriteLine(LogLevel.Debug, $"GetReviewScopeNpc called for player {CharacterName}: NpcList count = {NearbyNpcs?.Count ?? 0}");

			if (NearbyNpcs == null || Client.TreoMay)
			{
				LogHelper.WriteLine(LogLevel.Debug, $"GetReviewScopeNpc skipped for player {CharacterName}: NpcList null = {NearbyNpcs == null}, Client offline = {Client?.TreoMay}");
				return;
			}

			GetReviewScopeNpc_AOI();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Kiểm tra phạm vi NPC lỗi tại num: [" + num + "] [" + AccountID + "]-[" + CharacterName + "] - " + ex.ToString());
		}
	}

	/// <summary>
	/// AOI-based implementation of GetReviewScopeNpc
	/// Uses spatial partitioning for efficient NPC discovery
	/// </summary>
	private void GetReviewScopeNpc_AOI()
	{
		try
		{
			// IMPROVED: Add null checks and validation
			if (NearbyNpcs == null || Client?.TreoMay == true)
			{
				return;
			}

			// Get NPCs in AOI range using the AOI system
			var coords = AOI.AOISystem.Instance.GetGridCoords(PosX, PosY, MapID);
			var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(MapID, coords.Item1, coords.Item2);
			if (aoiGrids == null || aoiGrids.Count == 0)
			{
				return;
			}

			Dictionary<int, NpcClass> npcsToSpawn = new();
			Dictionary<int, NpcClass> npcsToDespawn = new();

			// IMPROVED: Use ToArray() to avoid collection modification during enumeration
			var currentNpcs = NearbyNpcs.ToArray();

			// First, check existing NPCs in NpcList to see if they're still in range
			foreach (var kvp in currentNpcs)
			{
				var npcSessionID = kvp.Key;
				var npc = kvp.Value;

				// IMPROVED: Add null check for NPC
				if (npc == null)
				{
					NearbyNpcs.TryRemove(npcSessionID, out _);
					continue;
				}

				// IMPROVED: Check if NPC is dead before distance check
				if (npc.NPCDeath || npc.Rxjh_HP <= 0)
				{
					NearbyNpcs.TryRemove(npcSessionID, out _);
					if (!npcsToDespawn.ContainsKey(npcSessionID))
					{
						npcsToDespawn.Add(npcSessionID, npc);
					}
					continue;
				}

				if (!LookInNpc(512, npc))
				{
					NearbyNpcs.TryRemove(npcSessionID, out _);
					if (!npcsToDespawn.ContainsKey(npcSessionID))
					{
						npcsToDespawn.Add(npcSessionID, npc);
					}
				}
			}

			// IMPROVED: Add new NPCs from AOI grids with better validation
			foreach (var grid in aoiGrids)
			{
				if (grid?.GetNPCs() == null) continue;

				foreach (var npc in grid.GetNPCs())
				{
					// IMPROVED: Add comprehensive NPC validation
					if (npc == null || npc.NPCDeath || npc.Rxjh_HP <= 0)
					{
						continue;
					}

					// IMPROVED: Check map consistency
					if (npc.Rxjh_Map != MapID)
					{
						continue;
					}

					// IMPROVED: Check if player can see this NPC (GM mode, death status, etc.)
					if (PlayerTuVong || GMMode == 8)
					{
						continue; // Dead players or GM mode players shouldn't see NPCs
					}

					if (LookInNpc(512, npc))
					{
						if (!NearbyNpcs.ContainsKey(npc.NPC_SessionID))
						{
							if (NearbyNpcs.TryAdd(npc.NPC_SessionID, npc))
							{
								if (!npcsToSpawn.ContainsKey(npc.NPC_SessionID))
								{
									npcsToSpawn.Add(npc.NPC_SessionID, npc);
								}
							}
						}
					}
				}
			}
			// Form1.WriteLine(1, $"Spawn {npcsToSpawn.Count} Despawn {npcsToDespawn.Count}");

			// Send spawn/despawn updates to client
			NpcClass.UpdateNPC_Despawn(npcsToDespawn, this);
			NpcClass.UpdateNPC_Spawn(npcsToSpawn, this);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"AOI GetReviewScopeNpc error for player [{AccountID}]-[{CharacterName}]: {ex.Message}");

			// Fallback to old system on error
			try
			{
				Dictionary<int, NpcClass> dictionary = new();
				Dictionary<int, NpcClass> dictionary2 = new();
				NpcClass value;

				foreach (var value2 in NearbyNpcs.Values)
				{
					if (!LookInNpc(512, value2))
					{
						NearbyNpcs.TryRemove(value2.NPC_SessionID, out _);
						if (!dictionary2.TryGetValue(value2.NPC_SessionID, out value))
						{
							dictionary2.Add(value2.NPC_SessionID, value2);
						}
						if (Client != null && value2.Contains(this))
						{
							// No need to call PlayList_Remove with AOI Grid system
						}
					}
				}

				foreach (var value3 in MapClass.GetnpcTemplate(MapID).Values)
				{
					if (LookInNpc(512, value3))
					{
						if (!NearbyNpcs.TryGetValue(value3.NPC_SessionID, out value))
						{
							if (NearbyNpcs.TryAdd(value3.NPC_SessionID, value3))
							{
								// No need to call PlayList_Add with AOI Grid system
								if (!dictionary.TryGetValue(value3.NPC_SessionID, out value))
								{
									dictionary.Add(value3.NPC_SessionID, value3);
								}
							}
						}
					}
				}

				NpcClass.UpdateNPC_Despawn(dictionary2, this);
				NpcClass.UpdateNPC_Spawn(dictionary, this);
			}
			catch (Exception fallbackEx)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Fallback GetReviewScopeNpc also failed: {fallbackEx.Message}");
			}
		}
	}
	public bool FindGroundItems(int far, GroundItem vatPhamMatDat)
	{
		if (vatPhamMatDat.MapID != MapID)
		{
			return false;
		}
		var num = vatPhamMatDat.PosX - PosX;
		var num2 = vatPhamMatDat.PosY - PosY;
		return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far;
	}

	public NpcClass NpcListContains(int index)
	{
		if (NearbyNpcs != null && NearbyNpcs.Count != 0)
		{
			if (NearbyNpcs.TryGetValue(index, out var value))
			{
				return value;
			}
			return null;
		}
		return null;
	}

	public void ScanGroundItems()
	{
		if (ListOfGroundItems == null)
		{
			return;
		}
		try
		{
			// Use AOI system if enabled for this map
			// if (AOI.// AOIConfiguration.Instance. ShouldUseAOI(MapID))
			// {
				ScanGroundItems_AOI();
				return;
			// }

			// Fallback to old system
			var queue = Queue.Synchronized(new Queue());
			Dictionary<long, GroundItem> dictionary = new();
			foreach (var value in ListOfGroundItems.Values)
			{
				if (!World.GroundItemList.ContainsKey(value.id))
				{
					queue.Enqueue(value.id);
					if (ListOfGroundItems.ContainsKey(value.id))
					{
						ListOfGroundItems.TryRemove(value.id, out _);
					}
				}
			}
			foreach (var value2 in World.GroundItemList.Values)
			{
				if (FindGroundItems(400, value2))
				{
					if (ListOfGroundItems.ContainsKey(value2.id))
					{
						continue;
					}
					ListOfGroundItems.TryAdd(value2.id, value2);
					if (value2.PlayerList != null)
					{
						if (!value2.PlayerList.ContainsKey(SessionID))
						{
							value2.PlayerList.Add(SessionID, this);
						}
						if (!dictionary.ContainsKey(value2.id))
						{
							dictionary.Add(value2.id, value2);
						}
					}
				}
				else if (ListOfGroundItems.ContainsKey(value2.id))
				{
					queue.Enqueue(value2.id);
					ListOfGroundItems.TryRemove(value2.id, out _);
				}
			}
			while (queue.Count > 0)
			{
				RemoveGroundItem((long)queue.Dequeue());
			}
			SendGroundItem(dictionary);
		}
		catch
		{
		}
	}

	/// <summary>
	/// AOI-based implementation of ScanGroundItems
	/// Uses spatial partitioning for efficient ground item discovery
	/// </summary>
	private void ScanGroundItems_AOI()
	{
		try
		{
			// Get ground items in AOI range using the AOI system
			var aoiGrids = AOI.AOISystem.Instance.GetNearbyGrids(MapID,PosX, PosY);

			var queue = Queue.Synchronized(new Queue());
			Dictionary<long, GroundItem> itemsToShow = new();

			// First, check existing items in ListOfGroundItems to see if they're still valid
			foreach (var value in ListOfGroundItems.Values)
			{
				if (!World.GroundItemList.ContainsKey(value.id))
				{
					queue.Enqueue(value.id);
					if (ListOfGroundItems.ContainsKey(value.id))
					{
						ListOfGroundItems.TryRemove(value.id, out _);
					}
				}
			}

			// Add new ground items from AOI grids
			foreach (var grid in aoiGrids)
			{
				foreach (var groundItem in grid.GroundItems)
				{
					if (FindGroundItems(400, groundItem))
					{
						if (ListOfGroundItems.ContainsKey(groundItem.id))
						{
							continue;
						}
						ListOfGroundItems.TryAdd(groundItem.id, groundItem);
						if (groundItem.PlayerList != null)
						{
							if (!groundItem.PlayerList.ContainsKey(SessionID))
							{
								groundItem.PlayerList.Add(SessionID, this);
							}
							if (!itemsToShow.ContainsKey(groundItem.id))
							{
								itemsToShow.Add(groundItem.id, groundItem);
							}
						}
					}
					else if (ListOfGroundItems.ContainsKey(groundItem.id))
					{
						queue.Enqueue(groundItem.id);
						ListOfGroundItems.TryRemove(groundItem.id, out _);
					}
				}
			}

			// Remove items that are no longer visible
			while (queue.Count > 0)
			{
				RemoveGroundItem((long)queue.Dequeue());
			}

			// Send new items to client
			SendGroundItem(itemsToShow);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"AOI ScanGroundItems error for player [{AccountID}]-[{CharacterName}]: {ex.Message}");

			// Fallback to old system on error
			try
			{
				var queue = Queue.Synchronized(new Queue());
				Dictionary<long, GroundItem> dictionary = new();
				foreach (var value in ListOfGroundItems.Values)
				{
					if (!World.GroundItemList.ContainsKey(value.id))
					{
						queue.Enqueue(value.id);
						if (ListOfGroundItems.ContainsKey(value.id))
						{
							ListOfGroundItems.TryRemove(value.id, out _);
						}
					}
				}
				foreach (var value2 in World.GroundItemList.Values)
				{
					if (FindGroundItems(400, value2))
					{
						if (ListOfGroundItems.ContainsKey(value2.id))
						{
							continue;
						}
						ListOfGroundItems.TryAdd(value2.id, value2);
						if (value2.PlayerList != null)
						{
							if (!value2.PlayerList.ContainsKey(SessionID))
							{
								value2.PlayerList.Add(SessionID, this);
							}
							if (!dictionary.ContainsKey(value2.id))
							{
								dictionary.Add(value2.id, value2);
							}
						}
					}
				}
				while (queue.Count > 0)
				{
					RemoveGroundItem((long)queue.Dequeue());
				}
				SendGroundItem(dictionary);
			}
			catch (Exception fallbackEx)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Fallback ScanGroundItems also failed: {fallbackEx.Message}");
			}
		}
	}
	
	public List<Players> TraTim_PhamVi_Player(int traTimPhamVi)
	{
		List<Players> list = new();
		foreach (var value in World.allConnectedChars.Values)
		{
			if (FindPlayers(traTimPhamVi, value))
			{
				list.Add(value);
			}
		}
		return list;
	}

	
	public void Click_Check_Info_NPC_Monster(byte[] data, int lenght)
	{
		try
		{
			if (data.Length < 12)
			{
				throw new Exception("Data length is not enough");
			}
			// var hex = new StringBuilder(data.Length * 3);
			// foreach (byte b in data)
			// {
			// 	hex.AppendFormat("{0:X2} ", b);
			// }
			// LogHelper.WriteLine(LogLevel.Debug, $"data hex {hex}");
			//LogHelper.WriteLine(LogLevel.Debug, $"{data.ToString()}");
			var targetId = BitConverter.ToInt16(data, 10);
			CurrentTarget = targetId;
			//  LogHelper.WriteLine(LogLevel.Info, "Select Target  " + targetId);
			var npc = NearbyNpcs.TryGetValue(targetId, out var value) ? value : null;
			if (npc != null)
			{
				TargetSessionID = targetId;
				HeThongNhacNho($"NPC {npc.NPC_SessionID} {npc.ID} {npc.FLD_PID} {targetId}");
			}
			// if (npc.IsNpc == 0)
			// {
			// 	if (GMMode != 0)
			// 	{
			// 		HeThongNhacNho("[" + npc.NPC_SessionID + "][" + npc.FLD_PID + "] -[Cấp: " + npc.Level + "] [HP :" + npc.Rxjh_HP + "/"+npc.Max_Rxjh_HP+"] [Công: " + npc.FLD_AT + "] [Thủ: " + npc.FLD_DF + "] [Kinh nghiệm: " + npc.Rxjh_Exp + "] [Bản đồ: " + npc.Rxjh_Map + "] [Tọa độ gốc: " + npc.Rxjh_cs_X + "," + npc.Rxjh_cs_Y + "] [Diện mạo: " + npc.FLD_FACE1 + "," + npc.FLD_FACE2 + "] [Tọa độ di chuyển: " + npc.Rxjh_X + "," + npc.Rxjh_Y + "]", 10, "ID");
			// 	}
			// }
			// Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, data, 4, 2);
			// Client?.Send_Map_Data(data, lenght);
			SendingClass w = new();
			w.Write4(targetId);
			w.Write4(0);
			Client?.SendPak(w, 0x8910, SessionID);

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Click_Check_Info_NPC_Monster error " + Client.PlayerSessionID + "|" + Client.ToString() + "  " + ex.Message);
		}
	}

}

