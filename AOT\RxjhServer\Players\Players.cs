using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Timers;

using Akka.Actor;
using RxjhServer.GroupQuest;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using Microsoft.Data.SqlClient;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer.AOI;
using System.Threading.Tasks;
using HeroYulgang.Utils;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Game;
using RxjhServer.PacketBuilder.Player;
using HeroYulgang.Constants;

namespace RxjhServer;

public partial class Players : PlayersBes, IDisposable
{

	public readonly object _attackLock = new();
	public DateTime _lastAttackTime = DateTime.MinValue;

	public double projecties_pelnaty = 1;

	// public int Pet_CuongKhi { get; private set; }
	public int CurrentTarget { get; set; }
	public DateTime LoginTime { get; private set; }

	private List<Players> _listLgbtCache = new();

	public System.Timers.Timer DchPlayerTimer;

	public System.Timers.Timer TlcPlayerTimer;

	public System.Timers.Timer ThoiGian2;

	public DateTime ThoiGianDieDch;

	public float TargetPositionX;

	public float TargetPositionY;

	private int _whetherTheYuanbaoPersonalStoreIsOpen = 1;

	private Dictionary<int, HcItimesClass> _hopThanhVatPhamTable = new();

	public DateTime LionRoarTime = DateTime.Now;

	public DateTime SendMessageTime = DateTime.Now;

	private DateTime _movingTime = DateTime.Now;


	public DateTime Gdtime = DateTime.Now;

	private static readonly string FilePath = "lastUsedDate.txt";

	public int CheckDaTraQuestHayChua;


	public int WorkingItemIndex;

	private DateTime _moRuongThoiGian = DateTime.Now;

	private MissionClass _mission;

	public int PluginNhacNhoSoLan;

	public bool HuanRandom;

	private bool _disposed;

	public bool IsJoinWorld;

	public bool verifyVersion;

	public DateTime XTtime;

	public int Times;

	public int TimesTang1Lan;

	public int TimesLog;

	public int CheckSoLanSk1CongDon;

	public int ThoiGianXacNhanTanCong;

	public int SoLanThangCuocOanTuTi;

	public System.Timers.Timer PreliminaryApplicationCeremonyTimer;

	private DateTime _preparationTime;

	public int WeddingMap;

	public int WeddingAdmissionTicket;

	public int WeddingPattern;

	private int _namePackPetType;

	private long _namePackagePetId;

	public DateTime UseDatuTime;


	public int NguyenBaoHopThanhMoRa;

	private int _phiHopThanh;

	private float _toaDoCuoiCungX;

	private float _toaDoCuoiCungY;

	private int _yxsl;

	public int MucDoQuaTai;

	public bool NhanVatDangDiChuyen;


	public int XacDinhXemCoDangNhapBangPacketHayKhong;

	public bool CheckGiaNhapBang;

	public string MacAddress = "";

	// TODO: Remove
	// public System.Timers.Timer TimerOneSec = new(100.0);

	public int PartyThaoPhatToDoiIdDragon;

	private object _lockCaculatorRecord;

	public int 副本复活剩余次数;

	public Players()
	{
		times = 0;
		DchPlayerTimer = null;
		_lockCaculatorRecord = new object();
		副本复活剩余次数 = 0;
		PartyThaoPhatToDoiIdDragon = 0;
		TanCongPacketXacNhan = new byte[38];
		dateTime_6789 = DateTime.Now;
		_mission = new MissionClass(this);
		NearbyPlayers = new();
		XTtime = DateTime.Now;
		SwitchPKTime = DateTime.Now;
		UseDatuTime = DateTime.Now;
		PluginNhacNhoSoLan = 0;
		Times = 0;
		TimesTang1Lan = 0;
		ThoiGian_Speed = 0;
		TimesLog = 0;
		CheckSoLanSk1CongDon = 0;
		ThoiGianXacNhanTanCong = 0;
		Pktime = DateTime.Now;
		ThoiGianTanCong = DateTime.Now;
		MucDoQuaTai = 0;
		ThanNuSuyYeu_ID = 0;
		NhanVatDangDiChuyen = false;
		KhuVuc_HoiSinh_Trong_TheLucChien = false;
		KhuVuc_HoiSinh_Ngoai_TheLucChien = false;
		DanQuaiVat_SoLan = 0;
		Kiemtra_KepSkill = 0;
		CheckDaTraQuestHayChua = 0;
		_movingTime = DateTime.Now;
		CoupleMobileTime = DateTime.Now.AddMinutes(-10.0);
		OffLine_TreoMay_CongKich_QuaiVat = 0;
		// TimerOneSec.Elapsed += Timer_Player_OneSec;
		// TimerOneSec.AutoReset = true;
		// TimerOneSec.Enabled = true;
		timeDelaySkill = new ThreadSafeDictionary<int, KeyValuePair<int, DateTime>>();

	}



	~Players()
	{
		Dispose(disposing: false);
	}

	public void Display()
	{
		var array = Converter.HexStringToByte("AA550600B2012000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void GsAddGuildPoint(int point)
	{
		GameDb.UpdateGuildPoint(GuildName, point);
		//RxjhClass.AddGuildPoint(point, CharacterName);
		HeThongNhacNho("Bạn cống hiến [" + point + "] vào bang hội", 7, "Thiên cơ các");
	}

	public void TurnOnItemTrigger(byte[] packetData)
	{
		try
		{
			if (PlayerTuVong)
			{
				return;
			}
			int itemPosition = packetData[11];
			var array = new byte[4];
			var array2 = new byte[4];
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			Buffer.BlockCopy(packetData, 22, array2, 0, 4);
			var itemId = BitConverter.ToInt32(array, 0);
			BitConverter.ToInt32(array2, 0);
			if (BitConverter.ToInt32(Item_In_Bag[itemPosition].VatPham_ID, 0) != itemId || !World.ItemList.TryGetValue(itemId, out var _) || BitConverter.ToInt32(Item_In_Bag[itemPosition].VatPham_ID, 0) == 0)
			{
				return;
			}
			try
			{
				//World.ScriptClass.OnItemOpen(SessionID, itemId, itemPosition);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "Open Itme TrigGer (" + SessionID + "," + itemId + "," + itemPosition + "," + BitConverter.ToInt32(Item_In_Bag[itemPosition].VatPhamSoLuong, 0) + ")");
				LogHelper.WriteLine(LogLevel.Error, "TurnOnItemTrigger error " + ex.Message);
			}
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "TurnOnItemTrigger2 error " + ex2.Message);
		}
	}

	public void FourGodsChangePrompt(int vatPhamViTri, int daoCuId, int nhacNhoId)
	{
		try
		{
			SendingClass sendingClass = new();
			sendingClass.Write1(1);
			sendingClass.Write1(vatPhamViTri);
			sendingClass.Write2(0);
			sendingClass.Write4(daoCuId);
			sendingClass.Write4(0);
			sendingClass.Write4(nhacNhoId);
			sendingClass.Write2(1);
			Client?.SendPak(sendingClass, 15104, SessionID);
		}
		catch
		{
		}
	}

	public void GuiDi_Move_SoLieu(float x, float y, float z, int map)
	{
		PlayerLeaveMap(map);
		AOIExtensions.UpdateAOIPosition(this, x, y);
		PosZ = z;
		MapID = map;
		TargetPositionX = x;
		TargetPositionY = y;
		SendingClass sendingClass = new();
		sendingClass.Write4(0);
		sendingClass.Write(PosX);
		sendingClass.Write(PosZ);
		sendingClass.Write(PosY);
		sendingClass.Write4(MapID);
		sendingClass.Write4(0);
		sendingClass.Write4(1);
		Client?.SendPak(sendingClass, 30976, SessionID);
		GetTheReviewRangePlayers();
		GetReviewScopeNpc();
		ScanGroundItems();
	}

	public void ThuThanh_BangChu_BaoVe_PhoTuongPhuCan()
	{
		try
		{
			if (MapID != 42001)
			{
				return;
			}
			var flag = false;
			if (GangCharacterLevel == 6 && GuildName == World.NguoiChiemGiu_Den_Tenma)
			{
				if (World.CongThanhChien_BatDau.ThuThanh_PhoTuong.FindPlayers(100, this))
				{
					if (!World.CongThanhChien_BatDau.HoaLong_ChiLuc_GiaiPhong)
					{
						flag = true;
						World.CongThanhChien_BatDau.ThuThanh_PhoTuong.GuiDi_QuaiVat_TrenDau_DoTieu(1);
					}
					World.CongThanhChien_BatDau.HoaLong_ChiLuc_GiaiPhong = true;
				}
				else
				{
					if (World.CongThanhChien_BatDau.HoaLong_ChiLuc_GiaiPhong)
					{
						flag = true;
						World.CongThanhChien_BatDau.ThuThanh_PhoTuong.GuiDi_QuaiVat_TrenDau_DoTieu(0);
					}
					World.CongThanhChien_BatDau.HoaLong_ChiLuc_GiaiPhong = false;
				}
				if (!flag)
				{
					return;
				}
				var text = "AA551A00150183031400040000000100000000000000000000000000000055AA";
				var array = Converter.HexStringToByte(text);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				if (World.CongThanhChien_BatDau.HoaLong_ChiLuc_GiaiPhong)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 14, 1);
				}
				else
				{
					Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 14, 1);
				}
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						if (value.MapID == 42001 && value.Client != null)
						{
							value.Client.Send_Map_Data(array, array.Length);
						}
					}
					return;
				}
			}
			if (World.CongThanhChien_BatDau.HoaLong_ChiLuc_GiaiPhong)
			{
				var text2 = "AA551200E9478B100C0046050000E94700000100000055AA";
				var array2 = Converter.HexStringToByte(text2);
				Buffer.BlockCopy(BitConverter.GetBytes(World.CongThanhChien_BatDau.ThuThanh_PhoTuong.NPC_SessionID), 0, array2, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array2, 18, 4);
				Client?.Send_Map_Data(array2, array2.Length);
			}
		}
		catch (Exception)
		{
		}
	}

	public void BuildAttackMultiTargetRes(Players Play, List<X_Quan_Cong_Kich_Loai> NhomCongKich,
		int NPCFullServiceID, int VoCong_ID, int CongKichLuc, int CongKichLoaiHinh, int Pet_CongKichLuc)
	{
		try
		{
			var x_Vo_Cong_Loai = World.MagicList[VoCong_ID];
			Random random = new(World.GetRandomSeed());
			using SendingClass w = new();
			w.Write2(NPCFullServiceID);
			w.Write2(0);
			w.Write2(VoCong_ID);
			w.Write4(NhomCongKich.Count);
			w.Write4(VoCong_ID);
			if (Play.Player_Job == 4 && VoCong_ID == 400001)
			{
				w.Write4(CongKichLoaiHinh);//
				CungPhatDong_TanCongNhom = false;
			}
			else
			{
				w.Write4(x_Vo_Cong_Loai.FLD_EFFERT);
			}

			w.Write(Play.PosX);
			w.Write(15f);
			w.Write(Play.PosY);
			w.Write(new byte[40]);
			if (Play.Player_Job == 4 || Play.Player_Job == 11)
			{
				//w.Write2(BitConverter.ToInt16(Item_Wear[12].VatPhamSoLuong, 0));
				w.Write2(1);
			}
			else
			{
				w.Write2(0);
			}
			w.Write2(0);
			SendingClass sendingClass2 = new();
			SendingClass sendingClass3 = new();
			SendingClass sendingClass4 = new();
			SendingClass sendingClass5 = new();
			SendingClass sendingClass6 = new();
			SendingClass sendingClass7 = new();
			var x = 0;
			foreach (var item in NhomCongKich)
			{
				sendingClass2.Write4(Trigger_NhatDiemNguThanh && x == 0 ? NPCFullServiceID : item.NhanVat_ID);
				sendingClass3.Write4(item.CongKichLuc);
				sendingClass4.Write4(item.SoLuongHPConDuLai);
				sendingClass5.Write1(1);
				sendingClass6.Write2(0);
				sendingClass7.Write4(item.TongSoLuongHP);
				x++;
			}

			for (var j = 0; j < 20 - NhomCongKich.Count; j++)
			{
				sendingClass2.Write4(0);
				sendingClass3.Write4(0);
				sendingClass4.Write4(0);
				sendingClass6.Write2(0);
				sendingClass7.Write4(0);
				//sendingClass2.Write4(0);
				//sendingClass4.Write8(0L);
				//sendingClass5.Write1(0);
				//sendingClass6.Write2(0);
				//sendingClass7.Write4(0);
			}

			for (var k = 0; k < 100 - NhomCongKich.Count; k++) sendingClass5.Write(0);
			var array = sendingClass2.ToArray3();
			w.Write(array, 0, array.Length);
			var array2 = sendingClass3.ToArray3();
			w.Write(array2, 0, array2.Length);
			var array3 = sendingClass5.ToArray3();
			w.Write(array3, 0, array3.Length);
			var array4 = sendingClass4.ToArray3();
			w.Write(array4, 0, array4.Length);
			var array5 = sendingClass7.ToArray3();
			w.Write(array5, 0, array5.Length);
			if (Pet_CongKichLuc != -1)
			{
				if (random.Next(0, 100) < 80)
					w.Write4(Pet_CongKichLuc);
				else
					w.Write4(0);
			}
			else
			{
				w.Write4(Pet_CongKichLuc);
			}

			w.Write4(0);
			w.Write4(0);
			w.Write4(0);
			w.Write4(0);
			w.Write4(0);
			w.Write(0);
			w.Write2(0); // 503 Daamge pet
			w.Write4(0);
			w.Write4(0);

			w.Write4(0); // + 36
			w.Write2(0); // 451
			if (Play.Player_Job == 12 && PhatDong_LuuTinhManThien)
			{
				w.Write(Trigger_NhatDiemNguThanh ? 2 : 1);
			}
			else if (Play.Player_Job == 13 && TriggerKillingStarLoyaltyKill)
			{
				w.Write(3);
			}
			else if (Play.Player_Job == 13 && TriggerKillingStarYiqiTiger)
			{
				w.Write(4);
			}
			else
			{
				w.Write(1);
			}
			w.Write4(0);
			Play.Client?.SendPak(w, 12032, Play.SessionID);
			SendCurrentRangeBroadcastData(w, 12032, Play.SessionID);
			// var i = 1;
			// foreach (var item in NhomCongKich)
			// {
			// 	SendDamageNumber(this, item.NhanVat_ID, item.CongKichLuc, item.CongKichLoaiHinh, VoCong_ID, i);
			// 	i++;
			// }
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GroupAttackError [" + AccountID + "][" + CharacterName + "] " + ex.Message);
		}
	}

	public void SendGoddessGroupAttackData(Players play, List<X_Quan_Cong_Kich_Loai> multiTarget, int NpcSessionID, int magicId, int congKichLuc, int congKichLoaiHinh, int petCongKichLuc)
	{
		try
		{
			var xVoCongLoai = World.MagicList[magicId];
			Random random = new(World.GetRandomSeed());
			using SendingClass sendingClass = new();
			sendingClass.Write2(NpcSessionID);
			sendingClass.Write2(0);
			sendingClass.Write2(magicId);
			sendingClass.Write4(multiTarget.Count);
			sendingClass.Write4(magicId);
			if (play.Player_Job == 4 && xVoCongLoai.FLD_TYPE == 4 && CungPhatDong_TanCongNhom)
			{
				checkdelaydamexanh = true;
				sendingClass.Write4(136);
				CungPhatDong_TanCongNhom = false;
			}
			else
			{
				sendingClass.Write4(xVoCongLoai.FLD_EFFERT);
			}
			sendingClass.Write(play.PosX);
			sendingClass.Write(15f);
			sendingClass.Write(play.PosY);
			// for (var i = 0; i < 5; i++)
			// {
			// 	sendingClass.Write4(0);
			// 	sendingClass.Write4(0);
			// }
			sendingClass.Write(new byte[40]);
			if (play.Player_Job != 4 && play.Player_Job != 11)
			{
				sendingClass.Write2(0);
			}
			else if (BitConverter.ToInt32(play.Item_Wear[12].VatPham_ID, 0) != 0)
			{
				var num = BitConverter.ToInt32(play.Item_Wear[12].VatPhamSoLuong, 0);
				play.Item_Wear[12].VatPhamSoLuong = BitConverter.GetBytes(num);
				if (num == 0)
				{
					play.Item_Wear[12].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				sendingClass.Write2(1);
			}
			else
			{
				sendingClass.Write2(0);
			}
			sendingClass.Write2(0);
			SendingClass sendingClass2 = new();
			SendingClass sendingClass3 = new();
			SendingClass sendingClass4 = new();
			SendingClass sendingClass5 = new();
			SendingClass sendingClass6 = new();
			SendingClass sendingClass7 = new();
			foreach (var item in multiTarget)
			{
				sendingClass2.Write4(item.NhanVat_ID);
				sendingClass3.Write4(item.CongKichLuc);
				sendingClass4.Write4(item.SoLuongHPConDuLai);
				sendingClass5.Write1(1);
				sendingClass6.Write1(0);
				sendingClass7.Write4(item.TongSoLuongHP);
			}
			for (var j = 0; j < 20 - multiTarget.Count; j++)
			{
				sendingClass2.Write4(0);
				sendingClass3.Write4(0);
				sendingClass4.Write4(0);
				sendingClass6.Write2(0);
				sendingClass7.Write4(0);
				// sendingClass2.Write4(0);
				// sendingClass3.Write4(0);
				// if (Player_Job == 4)
				// {
				// 	sendingClass4.Write2(120);
				// 	sendingClass4.Write2(120);
				// }
				// else
				// {
				// 	sendingClass3.Write4(0);
				// }
				// sendingClass6.Write2(0);
				// sendingClass7.Write4(0);
			}
			for (var k = 0; k < 100 - multiTarget.Count; k++)
			{
				sendingClass5.Write1(0);
			}
			if (petCongKichLuc != -1)
			{
				if (random.Next(0, 100) < 80)
				{
					sendingClass.Write4(petCongKichLuc);
				}
				else
				{
					sendingClass.Write4(0);
				}
			}
			else
			{
				sendingClass.Write4(petCongKichLuc);
			}
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write1(1);
			if (Trigger_NhatDiemNguThanh)
			{
				sendingClass.Write1(2);
			}
			else if (PhatDong_LuuTinhManThien)
			{
				sendingClass.Write1(1);
			}
			else if (TriggerKillingStarLoyaltyKill)
			{
				sendingClass.Write1(3);
			}
			else if (TriggerKillingStarYiqiTiger)
			{
				sendingClass.Write1(4);
			}
			else
			{
				sendingClass.Write1(0);
			}
			sendingClass.Write1(0);
			play.Client?.SendPak(sendingClass, 12032, play.SessionID);
			SendCurrentRangeBroadcastData(sendingClass, 12032, play.SessionID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đánh Nhiều Mục Tiêu CongKich SoLieu error 11 [" + AccountID + "][" + CharacterName + "] " + ex.Message);
		}
	}

	public int PhanDoanQuyenSuCombo(int voCongId)
	{
		if (voCongId != 3000101 && voCongId != 3000105 && Player_Job != 10)
		{
			QuyenSu_KiemSoat_ComBo = 0;
			return 0;
		}
		if (voCongId != 3000101 && voCongId != 3000105 && Player_Job == 10)
		{
			return 0;
		}
		if (QuyenSu_KiemSoat_ComBo == 0)
		{
			QuyenSu_KiemSoat_ComBo = 1;
			return voCongId;
		}
		if (QuyenSu_KiemSoat_ComBo == 1)
		{
			QuyenSu_KiemSoat_ComBo = 2;
			return voCongId + 1;
		}
		if (QuyenSu_KiemSoat_ComBo == 2)
		{
			QuyenSu_KiemSoat_ComBo = 3;
			return voCongId + 2;
		}
		if (QuyenSu_KiemSoat_ComBo == 3)
		{
			QuyenSu_KiemSoat_ComBo = 4;
			return voCongId + 3;
		}
		if (QuyenSu_KiemSoat_ComBo == 4)
		{
			QuyenSu_KiemSoat_ComBo = 5;
			return voCongId + 4;
		}
		if (QuyenSu_KiemSoat_ComBo == 5)
		{
			QuyenSu_KiemSoat_ComBo = 1;
			return voCongId;
		}
		QuyenSu_KiemSoat_ComBo = 1;
		return voCongId;
	}

	public void SendSpiritBeastAttackCharacterData(Players play, int congKichDoiTuongCharacterFullServerId, int voCongId, int congKichLuc, int congKichLoaiHinh, int soLuongHpCuoiCung)
	{
		try
		{
			if (World.MagicList.TryGetValue(voCongId, out var value))
			{
				using (SendingClass sendingClass = new())
				{
					sendingClass.Write2(congKichDoiTuongCharacterFullServerId);
					sendingClass.Write2(0);
					sendingClass.Write2(1);
					sendingClass.Write2(voCongId);
					sendingClass.Write2(0);
					sendingClass.Write4(congKichLuc);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(voCongId);
					sendingClass.Write4(value.FLD_EFFERT);
					sendingClass.Write(play.CharacterBeast.NhanVatToaDo_X);
					sendingClass.Write(15f);
					sendingClass.Write(play.CharacterBeast.NhanVatToaDo_Y);
					sendingClass.Write(0);
					sendingClass.Write(1);
					sendingClass.Write2(0);
					sendingClass.Write4(soLuongHpCuoiCung);
					if (congKichLuc < 1)
					{
						sendingClass.Write4(1);
					}
					else
					{
						sendingClass.Write4(0);
					}
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(-1);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write1(1);
					sendingClass.Write4(play.Num_Attack);
					sendingClass.Write1(0);
					play.Client?.SendPak(sendingClass, 2560, play.CharacterBeastFullServiceID);
					SendCurrentRangeBroadcastData(sendingClass, 2560, play.CharacterBeastFullServiceID);
					return;
				}
			}
			var value2 = 1;
			var num = congKichLuc;
			var num2 = 0;
			var num3 = 0;
			var num4 = 0;
			var value3 = 0;
			switch (congKichLoaiHinh)
			{
				default:
					if (play.Player_Job == 6)
					{
						num = 0;
						num2 = 0;
						if (congKichLuc != 0)
						{
							num = new Random().Next(congKichLuc / 2 - 10, congKichLuc / 2 + 10);
							num2 = congKichLuc - num;
						}
						value2 = 2;
					}
					break;
				case 130:
				case 131:
				case 132:
				case 133:
					if (congKichLuc != 0)
					{
						Random random3 = new();
						num = random3.Next(congKichLuc / 5 - 10, congKichLuc / 5 + 10);
						num2 = random3.Next((congKichLuc - num) / 4 - 10, (congKichLuc - num) / 4 + 10);
						num3 = random3.Next((congKichLuc - num - num2) / 3 - 10, (congKichLuc - num - num2) / 3 + 10);
						num4 = random3.Next((congKichLuc - num - num2 - num3) / 2 - 10, (congKichLuc - num - num2 - num3) / 2 + 10);
						value3 = congKichLuc - num - num2 - num3 - num4;
						value2 = 5;
					}
					break;
				case 128:
				case 129:
					if (play.Player_Job == 6)
					{
						if (congKichLuc != 0)
						{
							Random random = new();
							num = random.Next(congKichLuc / 5 - 10, congKichLuc / 5 + 10);
							num2 = random.Next((congKichLuc - num) / 4 - 10, (congKichLuc - num) / 4 + 10);
							num3 = random.Next((congKichLuc - num - num2) / 3 - 10, (congKichLuc - num - num2) / 3 + 10);
							num4 = random.Next((congKichLuc - num - num2 - num3) / 2 - 10, (congKichLuc - num - num2 - num3) / 2 + 10);
							value3 = congKichLuc - num - num2 - num3 - num4;
							value2 = 5;
						}
					}
					else
					{
						Random random2 = new();
						num = random2.Next(congKichLuc / 3 - 10, congKichLuc / 3 + 10);
						num2 = random2.Next((congKichLuc - num) / 2 - 10, (congKichLuc - num) / 2 + 10);
						num3 = congKichLuc - num - num2;
						value2 = 3;
					}
					break;
			}
			using SendingClass sendingClass2 = new();
			sendingClass2.Write2(congKichDoiTuongCharacterFullServerId);
			sendingClass2.Write2(0);
			sendingClass2.Write2(1);
			sendingClass2.Write4(0);
			sendingClass2.Write4(num);
			sendingClass2.Write4(num2);
			sendingClass2.Write4(num3);
			sendingClass2.Write4(num4);
			sendingClass2.Write4(value3);
			sendingClass2.Write4(voCongId);
			sendingClass2.Write4(congKichLoaiHinh);
			sendingClass2.Write(play.CharacterBeast.NhanVatToaDo_X);
			sendingClass2.Write(15f);
			sendingClass2.Write(play.CharacterBeast.NhanVatToaDo_Y);
			sendingClass2.Write(0);
			sendingClass2.Write(value2);
			sendingClass2.Write2(0);
			sendingClass2.Write4(soLuongHpCuoiCung);
			if (congKichLuc == 0)
			{
				sendingClass2.Write4(1);
			}
			else
			{
				sendingClass2.Write4(0);
			}
			sendingClass2.Write4(0);
			sendingClass2.Write4(0);
			sendingClass2.Write4(0);
			sendingClass2.Write4(0);
			sendingClass2.Write4(uint.MaxValue);
			sendingClass2.Write4(0);
			sendingClass2.Write4(0);
			sendingClass2.Write4(0);
			sendingClass2.Write4(0);
			sendingClass2.Write1(1);
			sendingClass2.Write4(play.Num_Attack);
			sendingClass2.Write1(0);
			play.Client?.SendPak(sendingClass2, 2560, play.CharacterBeastFullServiceID);
			SendCurrentRangeBroadcastData(sendingClass2, 2560, play.CharacterBeastFullServiceID);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Transmit CongKich nhân vâ\u0323t SoLieu error 11 [" + AccountID + "][" + CharacterName + "]      " + ex.Message);
		}
	}
	public void UpdateEquipmentEffectsTest(int slot)
	{
		var array = Converter.HexStringToByte("AA55060F860076001A0F000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		for (var i = 0; i < 42; i++)
		{
			if (i >= 16)
			{
				if (i == 30)
				{
					if (Item_Wear[15].FLD_MAGIC1 == 220000001)
					{
						Item_Wear[15].FLD_MAGIC2 = 1;
					}
					else if (Item_Wear[15].FLD_MAGIC1 == 220000002)
					{
						Item_Wear[15].FLD_MAGIC2 = 2;
					}
					else if (Item_Wear[15].FLD_MAGIC1 == 220000003)
					{
						Item_Wear[15].FLD_MAGIC2 = 3;
					}
					else if (Item_Wear[15].FLD_MAGIC1 == 220000004)
					{
						Item_Wear[15].FLD_MAGIC2 = 4;
					}
					else if (Item_Wear[15].FLD_MAGIC1 == 220000005)
					{
						Item_Wear[15].FLD_MAGIC2 = 5;
					}
					else if (Item_Wear[15].FLD_MAGIC1 == 220000006)
					{
						Item_Wear[15].FLD_MAGIC2 = 6;
					}
					else if (Item_Wear[15].FLD_MAGIC1 == 220000007)
					{
						Item_Wear[15].FLD_MAGIC2 = 7;
					}
					Buffer.BlockCopy(Item_Wear[15].VatPham_byte, 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Db_Byte_Length);
				}
				else if (i ==slot)
				{
					Buffer.BlockCopy(Item_Wear[17].VatPham_byte, 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Db_Byte_Length);
				}
				else
				{
					Buffer.BlockCopy(new byte[World.Item_Byte_Length_92], 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
				}
			}
			else if (i == 15)
			{
				Buffer.BlockCopy(new byte[World.Item_Byte_Length_92], 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
			}
			
			else if (MapID == 801 && i == 11 && World.tmc_flag)
			{
				var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
				var array2 = new byte[12];
				var array3 = new byte[60];
				Buffer.BlockCopy(Item_Wear[i].VatPham_byte, 0, array2, 0, 12);
				var dst = array2;
				Buffer.BlockCopy(bytes, 0, dst, 0, 8);
				Buffer.BlockCopy(Item_Wear[i].VatPham_byte, 12, array3, 0, 60);
				if (TheLucChien_PhePhai == "CHINH_PHAI")
				{
					if (Player_Sex == 1)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Chinh_Nam), 0, array2, 8, 4);
					}
					else
					{
						Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Chinh_Nu), 0, array2, 8, 4);
					}
				}
				else if (Player_Sex == 1)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Ta_Nam), 0, array2, 8, 4);
				}
				else
				{
					Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Ta_Nu), 0, array2, 8, 4);
				}
				if (MapID >= World.KhuLuyenTap1 && MapID <= World.KhuLuyenTap9)
				{
					var value = RNG.Next(16900613, 16900616);
					var value2 = RNG.Next(26900613, 26900616);
					if (Player_Zx == 2)
					{
						if (Player_Sex == 1)
						{
							Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array2, 8, 4);
						}
						else
						{
							Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array2, 8, 4);
						}
					}
					else if (Player_Sex == 1)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array2, 8, 4);
					}
					else
					{
						Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array2, 8, 4);
					}
				}
				Buffer.BlockCopy(array2, 0, array, 10 + i * World.Item_Byte_Length_92, 12);
				Buffer.BlockCopy(array3, 0, array, 26 + i * World.Item_Byte_Length_92, 60);
			}
			else if ((MapID == 9001 || MapID == 9101 || MapID == 9201) && i == 3)
			{
				Buffer.BlockCopy(new byte[World.Item_Byte_Length_92], 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
			}
			else
			{
				if (Item_Wear[i].GetVatPham_ID == 0)
				{
					continue;
				}
				var @byte = Item_Wear[i].GetByte();
				var num = (int)Item_Wear[i].GetVatPham_ID;
				if (i == 11)
				{
					if (Item_Wear[i].FLD_SERIES == 1)
					{
						num = int.Parse(num.ToString().Remove(7) + "0");
						for (var j = 0; j < 7; j++)
						{
							if (!World.ItemList.TryGetValue(num, out var value3) || value3.FLD_SERIES != 2)
							{
								num++;
								continue;
							}
							num = value3.FLD_PID;
							break;
						}
						if (num >= 16900830 && num <= 16900836)
						{
							num = 16900832;
						}
					}
				}
				else if (Item_Wear[i].FLD_Intrgration == 1)
				{
					num -= 5000;
				}
				Buffer.BlockCopy(BitConverter.GetBytes(num), 0, @byte, 8, 4);
				Buffer.BlockCopy(@byte, 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.SendMultiplePackage(array, array.Length);
		Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
	}

	public void UpdateEquipmentEffects()
	{
		Update_HieuUng_TrangBi();
		return;
		// var array = Converter.HexStringToByte("AA55060F86007600018F000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		// for (var i = 0; i < 42; i++)
		// {
		// 	if (i >= 16)
		// 	{
		// 		if (i == 30)
		// 		{
		// 			if (Item_Wear[15].FLD_MAGIC1 == 220000001)
		// 			{
		// 				Item_Wear[15].FLD_MAGIC2 = 1;
		// 			}
		// 			else if (Item_Wear[15].FLD_MAGIC1 == 220000002)
		// 			{
		// 				Item_Wear[15].FLD_MAGIC2 = 2;
		// 			}
		// 			else if (Item_Wear[15].FLD_MAGIC1 == 220000003)
		// 			{
		// 				Item_Wear[15].FLD_MAGIC2 = 3;
		// 			}
		// 			else if (Item_Wear[15].FLD_MAGIC1 == 220000004)
		// 			{
		// 				Item_Wear[15].FLD_MAGIC2 = 4;
		// 			}
		// 			else if (Item_Wear[15].FLD_MAGIC1 == 220000005)
		// 			{
		// 				Item_Wear[15].FLD_MAGIC2 = 5;
		// 			}
		// 			else if (Item_Wear[15].FLD_MAGIC1 == 220000006)
		// 			{
		// 				Item_Wear[15].FLD_MAGIC2 = 6;
		// 			}
		// 			else if (Item_Wear[15].FLD_MAGIC1 == 220000007)
		// 			{
		// 				Item_Wear[15].FLD_MAGIC2 = 7;
		// 			}
		// 			Buffer.BlockCopy(Item_Wear[15].VatPham_byte, 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Db_Byte_Length);
		// 		}
		// 		else if (i ==40)
		// 		{
		// 			Buffer.BlockCopy(Item_Wear[17].VatPham_byte, 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Db_Byte_Length);
		// 		}
		// 		else
		// 		{
		// 			Buffer.BlockCopy(new byte[World.Item_Byte_Length_92], 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
		// 		}
		// 	}
		// 	else if (i == 15)
		// 	{
		// 		Buffer.BlockCopy(new byte[World.Item_Byte_Length_92], 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
		// 	}
			
		// 	else if (MapID == 801 && i == 11 && World.tmc_flag)
		// 	{
		// 		var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		// 		var array2 = new byte[12];
		// 		var array3 = new byte[60];
		// 		Buffer.BlockCopy(Item_Wear[i].VatPham_byte, 0, array2, 0, 12);
		// 		var dst = array2;
		// 		Buffer.BlockCopy(bytes, 0, dst, 0, 8);
		// 		Buffer.BlockCopy(Item_Wear[i].VatPham_byte, 12, array3, 0, 60);
		// 		if (TheLucChien_PhePhai == "CHINH_PHAI")
		// 		{
		// 			if (Player_Sex == 1)
		// 			{
		// 				Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Chinh_Nam), 0, array2, 8, 4);
		// 			}
		// 			else
		// 			{
		// 				Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Chinh_Nu), 0, array2, 8, 4);
		// 			}
		// 		}
		// 		else if (Player_Sex == 1)
		// 		{
		// 			Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Ta_Nam), 0, array2, 8, 4);
		// 		}
		// 		else
		// 		{
		// 			Buffer.BlockCopy(BitConverter.GetBytes(World.Hieu_Ung_Ao_Choang_Ta_Nu), 0, array2, 8, 4);
		// 		}
		// 		if (MapID >= World.KhuLuyenTap1 && MapID <= World.KhuLuyenTap9)
		// 		{
		// 			var value = RNG.Next(16900613, 16900616);
		// 			var value2 = RNG.Next(26900613, 26900616);
		// 			if (Player_Zx == 2)
		// 			{
		// 				if (Player_Sex == 1)
		// 				{
		// 					Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array2, 8, 4);
		// 				}
		// 				else
		// 				{
		// 					Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array2, 8, 4);
		// 				}
		// 			}
		// 			else if (Player_Sex == 1)
		// 			{
		// 				Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array2, 8, 4);
		// 			}
		// 			else
		// 			{
		// 				Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array2, 8, 4);
		// 			}
		// 		}
		// 		Buffer.BlockCopy(array2, 0, array, 10 + i * World.Item_Byte_Length_92, 12);
		// 		Buffer.BlockCopy(array3, 0, array, 26 + i * World.Item_Byte_Length_92, 60);
		// 	}
		// 	else if ((MapID == 9001 || MapID == 9101 || MapID == 9201) && i == 3)
		// 	{
		// 		Buffer.BlockCopy(new byte[World.Item_Byte_Length_92], 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
		// 	}
		// 	else
		// 	{
		// 		if (Item_Wear[i].GetVatPham_ID == 0)
		// 		{
		// 			continue;
		// 		}
		// 		var @byte = Item_Wear[i].GetByte();
		// 		var num = (int)Item_Wear[i].GetVatPham_ID;
		// 		if (i == 11)
		// 		{
		// 			if (Item_Wear[i].FLD_SERIES == 1)
		// 			{
		// 				num = int.Parse(num.ToString().Remove(7) + "0");
		// 				for (var j = 0; j < 7; j++)
		// 				{
		// 					if (!World.ItemList.TryGetValue(num, out var value3) || value3.FLD_SERIES != 2)
		// 					{
		// 						num++;
		// 						continue;
		// 					}
		// 					num = value3.FLD_PID;
		// 					break;
		// 				}
		// 				if (num >= 16900830 && num <= 16900836)
		// 				{
		// 					num = 16900832;
		// 				}
		// 			}
		// 		}
		// 		else if (Item_Wear[i].FLD_Intrgration == 1)
		// 		{
		// 			num -= 5000;
		// 		}
		// 		Buffer.BlockCopy(BitConverter.GetBytes(num), 0, @byte, 8, 4);
		// 		Buffer.BlockCopy(@byte, 0, array, 10 + i * World.Item_Byte_Length_92, World.Item_Byte_Length_92);
		// 	}
		// }
		// Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		// Client?.SendMultiplePackage(array, array.Length);
		// Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
	}
	public void Update_HieuUng_TrangBi()
	{
		SendingClass sendingClass = new();
		for (var i = 0; i < 42; i++)
		{
			if (i == 30)
			{
				if (Item_Wear[15].GetVatPham_ID != 0)
				{
					sendingClass.Write(Item_Wear[15].GetByte(), 0, World.Item_Byte_Length_92);
				}
				else
				{
					sendingClass.Write(new byte[World.Item_Byte_Length_92], 0, World.Item_Byte_Length_92);
				}
			}
			else if (i == 40)
			{
				sendingClass.Write(Item_Wear[17].GetByte(), 0, World.Item_Byte_Length_92);
			}
			else if (i >= 15)
			{
				sendingClass.Write(new byte[World.Item_Byte_Length_92], 0, World.Item_Byte_Length_92);
			}
			else if (MapID == 801 && i == 11)
			{
				var bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
				var array = new byte[96];
				Buffer.BlockCopy(bytes, 0, array, 0, 8);
				Buffer.BlockCopy(Item_Wear[i].VatPham_byte, 12, array, 0, 60);
				if (TheLucChien_PhePhai == "CHINH_PHAI")
				{
					if (Player_Sex == 1)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(16900001), 0, array, 8, 4);
					}
					else
					{
						Buffer.BlockCopy(BitConverter.GetBytes(26900075), 0, array, 8, 4);
					}
				}
				else if (Player_Sex == 1)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(16900672), 0, array, 8, 4);
				}
				else
				{
					Buffer.BlockCopy(BitConverter.GetBytes(26900672), 0, array, 8, 4);
				}
				sendingClass.Write(array, 0, World.Item_Byte_Length_92);
			}
			else
			{
				sendingClass.Write(Item_Wear[i].GetByte(), 0, World.Item_Byte_Length_92);
			}
		}

		Client?.SendPak(sendingClass, 30208, SessionID);
		SendCurrentRangeBroadcastData(sendingClass, 30208, SessionID);
	}

	public void WalkingStateCharacterSpiritBeast(byte[] wgId, int trangThaiId)
	{
		var array = Converter.HexStringToByte("AA55260000003D0018000E2C09000101000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(wgId, 0, array, 11, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(trangThaiId), 0, array, 15, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(CharacterBeastFullServiceID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		SendCurrentRangeBroadcastData(array, array.Length);
	}

	public void ExplosiveDataPacket(int packagePosition)
	{
		var array = Converter.HexStringToByte("AA5526006B053B0018000105000000000000FF000000120000000000000000000000000000000000AE8B55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(packagePosition), 0, array, 11, 4);
		Buffer.BlockCopy(Item_In_Bag[packagePosition].VatPhamSoLuong, 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void AddAttributes(int diemTichLuy, int voHuan)
	{
		KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
		Player_WuXun += voHuan;
		KiemSoatNguyenBao_SoLuong(diemTichLuy, 1);
		Save_NguyenBaoData();
		TinhToan_NhanVatCoBan_DuLieu();
		UpdateMartialArtsAndStatus();
		CapNhat_HP_MP_SP();
	}

	private void Timer_Player_OneSec(object sender, ElapsedEventArgs e)
	{
		if (MapID != 801 || TLC_Cho_Chet != 1 || (int)DateTime.Now.Subtract(TLC_Cho_DiChuyen).TotalMilliseconds <= 5000)
		{
			return;
		}
		if (NhanVat_HP <= 0L || PlayerTuVong)
		{
			TLC_DaTinhDiem_ChoMayRoi = false;
			if (TheLucChien_PhePhai == "CHINH_PHAI")
			{
				DeathMove(0f, -232f, 15f, 801);
				PlayerTuVong = false;
				NhanVat_HP = CharacterMax_HP;
				CapNhat_HP_MP_SP();
			}
			else
			{
				DeathMove(0f, 228f, 15f, 801);
				PlayerTuVong = false;
				NhanVat_HP = CharacterMax_HP;
				CapNhat_HP_MP_SP();
			}
		}
		TLC_Cho_DiChuyen = DateTime.Now;
		TheLucChien_LuaChonHoiSinh();
		TLC_Cho_Chet = 0;
	}

	public void ThoiGianChoChetDch(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)ThoiGianDieDch.Subtract(DateTime.Now).TotalSeconds != 0)
			{
				return;
			}
			if (DCH_HS_LuaChon == 0)
			{
				if (Player_Zx == 1)
				{
					DeathMove(222f, -688f, 15f, 40101);
				}
				else if (Player_Zx == 2)
				{
					DeathMove(205f, 700f, 15f, 40101);
				}
				NhanVat_HP = CharacterMax_HP;
				CapNhat_HP_MP_SP();
				PlayerTuVong = false;
			}
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
		}
		catch (Exception ex)
		{
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			LogHelper.WriteLine(LogLevel.Error, "Thế lực chiến ThoiGianKetThucSuKien1 error：" + ex.ToString());
		}
	}


	public void SendAnInvitationToTheDemonShrine()
	{
		try
		{
			var array = Converter.HexStringToByte("AA551C00280305510E000000000001000000020011A40000000000000000000055AA");
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
		}
	}

	public void SendTheCelestialDemonShrineInvitationSilverCoins()
	{
		var array = Converter.HexStringToByte("AA5514006B0203510E0000000000FFFFFFFFFFFF0700000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}
	
	public void OpClient(int iOpCode)
	{
		var array = Converter.HexStringToByte("AA5512000100BB00040001000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(iOpCode), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
		Thread.Sleep(100);
	}

}
