using HeroYulgang.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RxjhServer.AOI
{
    public static class AOIExtensions
    {
        #region Player Extensions
        public static void UpdateAOI(this Players player)
        {
            try
            {
                if (player?.Client?.Running == true)
                    AOISystem.Instance.UpdatePlayerAOI(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI update failed for {player?.CharacterName}: {ex.Message}");
                try
                {
                    player?.GetTheReviewRangePlayers();
                    player?.GetReviewScopeNpc();
                    player?.ScanGroundItems();
                }
                catch { /* Ignore fallback errors */ }
            }
        }

        public static void AddToAOI(this Players player) => AOISystem.Instance.AddPlayer(player);
        public static void RemoveFromAOI(this Players player) => AOISystem.Instance.RemovePlayer(player.SessionID);
        public static void UpdateAOIPosition(this Players player, float newX, float newY) => AOISystem.Instance.MovePlayer(player, newX, newY);
        
        // Legacy compatibility methods
        public static void ForceUpdateAOI(this Players player) => player.UpdateAOI();
        public static void UpdateAOIImmediate(this Players player) => player.UpdateAOI();
        public static void UpdateVisibilityWithOverlap(this Players player) => player.UpdateAOI();
        public static void SynchronizeMapTransitionAOI(this Players player) => player.UpdateAOI();
        public static void HandlePlayerDeath(this Players player) => player.UpdateAOI();
        public static void HandlePlayerRespawn(this Players player) => player.UpdateAOI();
        public static void UpdateMovementAOI(this Players player) => player.UpdateAOI();
        #endregion
        
        #region NPC Extensions
        public static void AddToAOI(this NpcClass npc) => AOISystem.Instance.AddNPC(npc);
        public static void RemoveFromAOI(this NpcClass npc) => AOISystem.Instance.RemoveNPC(npc.NPC_SessionID, npc.Rxjh_Map);
        #endregion
        
        #region Ground Item Extensions
        public static void AddToAOI(this GroundItem item) => AOISystem.Instance.AddGroundItem(item);
        public static void RemoveFromAOI(this GroundItem item) => AOISystem.Instance.RemoveGroundItem(item.id, item.MapID);
        #endregion
        
        #region Batch Operations
        public static void BatchUpdateAOI(this IEnumerable<Players> players)
        {
            var playerList = players?.Where(p => p?.Client?.Running == true).ToList();
            if (playerList?.Count > 0)
                AOISystem.Instance.BatchUpdate(playerList);
        }
        #endregion
        
        #region Utility
        public static (int x, int y) GetGridCoords(float posX, float posY, int mapID) => AOISystem.Instance.GetGridCoords(posX, posY, mapID);
        public static int GetEntityCount(int mapID) => AOISystem.Instance.GetEntityCount(mapID);
        #endregion
    }
}