﻿using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;


namespace RxjhServer;

public partial class Players
{
    
	public void AddTlcBangSo(string userName, int playerLevel, int playerJob, string theLucChienPhePhai, string gangName, int gietNguoiSoLuong, int tuVongSoLuong)
	{
		World.EventTop.TryGetValue(userName, out var value);
		if (value == null)
		{
			EventTopClass eventTopClass = new();
			eventTopClass.TenNhanVat = userName;
			eventTopClass.DangCap = playerLevel;
			eventTopClass.NgheNghiep = playerJob;
			eventTopClass.TheLuc = theLucChienPhePhai;
			eventTopClass.BangPhai = gangName;
			eventTopClass.GietNguoiSoLuong = gietNguoiSoLuong;
			eventTopClass.TuVongSoLuong = tuVongSoLuong;
			World.EventTop.Add(userName, eventTopClass);
		}
	}

	public void AddDchBangSo(string userName, int playerLevel, int playerJob, string theLucChienPhePhai, string gangName, int gietNguoiSoLuong, int tuVongSoLuong)
	{
		World.EventTopDCH.TryGetValue(userName, out var value);
		if (value == null)
		{
			EventTopDCHClass eventTopDchClass = new();
			eventTopDchClass.TenNhanVat = userName;
			eventTopDchClass.DangCap = playerLevel;
			eventTopDchClass.NgheNghiep = playerJob;
			eventTopDchClass.TheLuc = theLucChienPhePhai;
			eventTopDchClass.BangPhai = gangName;
			eventTopDchClass.GietNguoiSoLuong = gietNguoiSoLuong;
			eventTopDchClass.TuVongSoLuong = tuVongSoLuong;
			World.EventTopDCH.Add(userName, eventTopDchClass);
		}
	}

	public void RemoveDchBangSo(string userName)
	{
		World.EventTopDCH.Remove(userName);
		foreach (var value in World.allConnectedChars.Values)
		{
			value.Client?.Dispose();
		}
	}

	public void RemoveTlcBangSo(string userName)
	{
		World.EventTop.Remove(userName);
		foreach (var value in World.allConnectedChars.Values)
		{
			value.Client?.Dispose();
		}
	}

	public void TheLucChien_HeThong(byte[] packetData, int length)
	{
		PacketModification(packetData, length);
		if (MapID == 9001 || MapID == 9101 || MapID == 9201 || (PlayerTuVong && MapID != 801) || GiaoDich.GiaoDichBenTrong || Exiting || OpenWarehouse)
		{
			return;
		}
		if (CharacterPKMode != 0)
		{
			SwitchPkMode(0);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, packetData, 3, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packetData, 4, 2);
		var array = new byte[2];
		var array2 = new byte[2];
		Buffer.BlockCopy(packetData, 10, array, 0, 2);
		Buffer.BlockCopy(packetData, 18, array2, 0, 2);
		switch (BitConverter.ToInt16(array, 0))
		{
			default:
				Buffer.BlockCopy(array, 0, packetData, 10, 2);
				Buffer.BlockCopy(array, 0, packetData, 14, 2);
				break;
			case 11:
				Mobile(499f, 2215f, 15f, 101, 0);
				GetTheReviewRangePlayers();
				GetReviewScopeNpc();
				ScanGroundItems();
				break;
			case 2:
				{
					if (BitConverter.ToInt16(array2, 0) != 1)
					{
						break;
					}
					if (MapID == 801)
					{
						return;
					}
					var num = 0;
					if (FLD_VIP != World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han)
					{
						foreach (var value in World.allConnectedChars.Values)
						{
							if (MacAddress == value.MacAddress && value.MapID == 801)
							{
								num++;
								LogHelper.WriteLine(LogLevel.Error, "Giới hạn MAC Address [" + AccountID + "][" + CharacterName + "][" + MacAddress + "][" + num + "] part 1");
							}
						}
					}
					if (num < World.Gioi_han_acc_vao_TLC)
					{
						var item = World.DiDong.FirstOrDefault(i => i.Rxjh_Map == 801);
						if (item == null)
						{
							HeThongNhacNho("Bản đồ Thế Lực Chiến đang được bảo trì, vui lòng quay lại sau.", 10, "Thiên cơ các");
							return;
						}
						var numberOfRebirths = NumberOfRebirths;
						if (numberOfRebirths < World.ResetNhoHon_KhongTheVaoTLC_OLD)
						{
							HeThongNhacNho("Dưới Trùng Sinh [" + World.ResetNhoHon_KhongTheVaoTLC_OLD + "] không thể tiến vào bản đồ Thế Lực Chiến!", 10, "Thiên cơ các");
							return;
						}
						if (numberOfRebirths > World.ResetLonHon_KhongTheVaoTLC_NEW)
						{
							HeThongNhacNho("Trùng Sinh từ [" + World.ResetLonHon_KhongTheVaoTLC_NEW + "] trở lên không thể tiến vào bản đồ Thế Lực Chiến Mới!", 10, "Thiên cơ các");
							return;
						}
						if (Player_Job_level < 2 || MapID == 801)
						{
							return;
						}
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, packetData, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, packetData, 14, 2);
						if (World.tmc_flag)
						{
							if (World.TheLucChien_Progress > 0 && World.TheLucChien_Progress < 4)
							{
								if (TheLucChien_PhePhai == "CHINH_PHAI")
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									Mobile(520f, 435f, 15f, 801, 0);
								}
								else if (TheLucChien_PhePhai == "TA_PHAI")
								{
									World.TheLucChien_TaPhai_SoNguoi++;
									TheLucChien_PhePhai = "TA_PHAI";
									Mobile(-520f, 435f, 15f, 801, 0);
								}
								else if (World.TheLucChien_ChinhPhai_SoNguoi == World.TheLucChien_TaPhai_SoNguoi)
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									TheLucChien_PhePhai = "CHINH_PHAI";
									Mobile(520f, 435f, 15f, 801, 0);
								}
								else if (World.TheLucChien_ChinhPhai_SoNguoi < World.TheLucChien_TaPhai_SoNguoi)
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									TheLucChien_PhePhai = "CHINH_PHAI";
									Mobile(520f, 435f, 15f, 801, 0);
								}
								else
								{
									World.TheLucChien_TaPhai_SoNguoi++;
									TheLucChien_PhePhai = "TA_PHAI";
									Mobile(-520f, 435f, 15f, 801, 0);
								}
							}
						}
						else if (Player_Zx == 1)
						{
							if (World.TheLucChien_ChinhPhai_SoNguoi == World.TheLucChien_TaPhai_SoNguoi)
							{
								World.TheLucChien_ChinhPhai_SoNguoi++;
								TheLucChien_PhePhai = "CHINH_PHAI";
								if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
								{
									Mobile(520f, 435f, 15f, 801, 0);
								}
							}
							else if (World.TheLucChien_ChinhPhai_SoNguoi < World.TheLucChien_TaPhai_SoNguoi)
							{
								World.TheLucChien_ChinhPhai_SoNguoi++;
								TheLucChien_PhePhai = "CHINH_PHAI";
								if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
								{
									Mobile(520f, 435f, 15f, 801, 0);
								}
							}
							else if (World.TheLucChien_ChinhPhai_SoNguoi > 0 && World.TheLucChien_ChinhPhai_SoNguoi - World.TheLucChien_TaPhai_SoNguoi > 5)
							{
								HeThongNhacNho("Số lượng bên Chính vượt Tà 5 người, xin chờ một lát rồi thử lại!", 10, "Thiên cơ các");
							}
							else
							{
								World.TheLucChien_ChinhPhai_SoNguoi++;
								TheLucChien_PhePhai = "CHINH_PHAI";
								if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
								{
									Mobile(520f, 435f, 15f, 801, 0);
								}
							}
						}
						else if (World.TheLucChien_ChinhPhai_SoNguoi == World.TheLucChien_TaPhai_SoNguoi)
						{
							World.TheLucChien_TaPhai_SoNguoi++;
							TheLucChien_PhePhai = "TA_PHAI";
							if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
							{
								Mobile(-520f, 435f, 15f, 801, 0);
							}
						}
						else if (World.TheLucChien_ChinhPhai_SoNguoi > World.TheLucChien_TaPhai_SoNguoi)
						{
							World.TheLucChien_TaPhai_SoNguoi++;
							TheLucChien_PhePhai = "TA_PHAI";
							if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
							{
								Mobile(-520f, 435f, 15f, 801, 0);
							}
						}
						else if (World.TheLucChien_TaPhai_SoNguoi > 0 && World.TheLucChien_TaPhai_SoNguoi - World.TheLucChien_ChinhPhai_SoNguoi > World.Gioi_han_chenh_lech_2_ben_vao_TLC)
						{
							HeThongNhacNho("Số lượng bên Tà vượt Chính [" + World.Gioi_han_chenh_lech_2_ben_vao_TLC + "] người, xin chờ một lát rồi thử lại!", 10, "Truyền Âm Các");
						}
						else
						{
							World.TheLucChien_TaPhai_SoNguoi++;
							TheLucChien_PhePhai = "TA_PHAI";
							if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
							{
								Mobile(-520f, 435f, 15f, 801, 0);
							}
						}

					}
					else
					{
						HeThongNhacNho("Giới hạn Thế Lực Chiến: [" + World.Gioi_han_acc_vao_TLC + "] tài khoản trên 1 IP!!!", 10, "Truyền Âm Các");
					}
					break;
				}
			case 3:
				{
					if (BitConverter.ToInt16(array2, 0) != 1)
					{
						break;
					}
					if (MapID == 801)
					{
						return;
					}
					var num2 = 0;
					if (FLD_VIP != World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han)
					{
						foreach (var value2 in World.allConnectedChars.Values)
						{
							if (MacAddress == value2.MacAddress && value2.MapID == 801)
							{
								num2++;
								LogHelper.WriteLine(LogLevel.Error, "Giới hạn MAC Address [" + AccountID + "][" + CharacterName + "][" + MacAddress + "][" + num2 + "] part 2");
							}
						}
					}
					if (num2 < World.Gioi_han_acc_vao_TLC)
					{
						foreach (var item2 in World.DiDong)
						{
							if (item2.Rxjh_Map != 801)
							{
								continue;
							}
							var numberOfRebirths2 = NumberOfRebirths;
							if (numberOfRebirths2 < World.ResetNhoHon_KhongTheVaoTLC_OLD)
							{
								HeThongNhacNho("Dưới Trùng Sinh [" + World.ResetNhoHon_KhongTheVaoTLC_OLD + "] không thể tiến vào bản đồ Thế Lực Chiến!", 10, "Thiên cơ các");
								return;
							}
							if (numberOfRebirths2 > World.ResetLonHon_KhongTheVaoTLC_NEW)
							{
								HeThongNhacNho("Trùng Sinh từ [" + World.ResetLonHon_KhongTheVaoTLC_NEW + "] trở lên không thể tiến vào bản đồ Thế Lực Chiến Mới!", 10, "Thiên cơ các");
								return;
							}
							if (Player_Job_level < 2 || MapID == 801)
							{
								continue;
							}
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, packetData, 10, 2);
							Buffer.BlockCopy(BitConverter.GetBytes(2), 0, packetData, 14, 2);
							if (World.tmc_flag)
							{
								if (TheLucChien_PhePhai == "CHINH_PHAI")
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(520f, 435f, 15f, 801, 0);
									}
								}
								else if (TheLucChien_PhePhai == "TA_PHAI")
								{
									World.TheLucChien_TaPhai_SoNguoi++;
									TheLucChien_PhePhai = "TA_PHAI";
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(-520f, 435f, 15f, 801, 0);
									}
								}
								else if (World.TheLucChien_ChinhPhai_SoNguoi == World.TheLucChien_TaPhai_SoNguoi)
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									TheLucChien_PhePhai = "CHINH_PHAI";
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(520f, 435f, 15f, 801, 0);
									}
								}
								else if (World.TheLucChien_ChinhPhai_SoNguoi < World.TheLucChien_TaPhai_SoNguoi)
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									TheLucChien_PhePhai = "CHINH_PHAI";
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(520f, 435f, 15f, 801, 0);
									}
								}
								else
								{
									World.TheLucChien_TaPhai_SoNguoi++;
									TheLucChien_PhePhai = "TA_PHAI";
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(-520f, 435f, 15f, 801, 0);
									}
								}
							}
							else if (Player_Zx == 1)
							{
								if (World.TheLucChien_ChinhPhai_SoNguoi == World.TheLucChien_TaPhai_SoNguoi)
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									TheLucChien_PhePhai = "CHINH_PHAI";
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(520f, 435f, 15f, 801, 0);
									}
								}
								else if (World.TheLucChien_ChinhPhai_SoNguoi < World.TheLucChien_TaPhai_SoNguoi)
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									TheLucChien_PhePhai = "CHINH_PHAI";
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(520f, 435f, 15f, 801, 0);
									}
								}
								else if (World.TheLucChien_ChinhPhai_SoNguoi > 0 && World.TheLucChien_ChinhPhai_SoNguoi - World.TheLucChien_TaPhai_SoNguoi >= World.Gioi_han_chenh_lech_2_ben_vao_TLC)
								{
									HeThongNhacNho("Số lượng bên Chính vượt Tà [" + World.Gioi_han_chenh_lech_2_ben_vao_TLC + "] người, xin chờ một lát rồi thử lại!", 10, "Truyền Âm Các");
								}
								else
								{
									World.TheLucChien_ChinhPhai_SoNguoi++;
									TheLucChien_PhePhai = "CHINH_PHAI";
									if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
									{
										Mobile(520f, 435f, 15f, 801, 0);
									}
								}
							}
							else if (World.TheLucChien_ChinhPhai_SoNguoi == World.TheLucChien_TaPhai_SoNguoi)
							{
								World.TheLucChien_TaPhai_SoNguoi++;
								TheLucChien_PhePhai = "TA_PHAI";
								if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
								{
									Mobile(-520f, 435f, 15f, 801, 0);
								}
							}
							else if (World.TheLucChien_ChinhPhai_SoNguoi > World.TheLucChien_TaPhai_SoNguoi)
							{
								World.TheLucChien_TaPhai_SoNguoi++;
								TheLucChien_PhePhai = "TA_PHAI";
								if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
								{
									Mobile(-520f, 435f, 15f, 801, 0);
								}
							}
							else if (World.TheLucChien_TaPhai_SoNguoi > 0 && World.TheLucChien_TaPhai_SoNguoi - World.TheLucChien_ChinhPhai_SoNguoi >= World.Gioi_han_chenh_lech_2_ben_vao_TLC)
							{
								HeThongNhacNho("Số lượng bên Tà vượt Chính [" + World.Gioi_han_chenh_lech_2_ben_vao_TLC + "] người, xin chờ một lát rồi thử lại!", 10, "Truyền Âm Các");
							}
							else
							{
								World.TheLucChien_TaPhai_SoNguoi++;
								TheLucChien_PhePhai = "TA_PHAI";
								if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
								{
									Mobile(-520f, 435f, 15f, 801, 0);
								}
							}
						}
					}
					else
					{
						HeThongNhacNho("Giới hạn Thế Lực Chiến: [" + World.Gioi_han_acc_vao_TLC + "] tài khoản trên 1 IP (CX)!!!", 10, "Truyền Âm Các");
					}
					break;
				}
			case 4:
				if (World.TheLucChien_Progress <= 0 || World.TheLucChien_Progress > 5 || MapID != 801)
				{
					break;
				}
				switch (BitConverter.ToInt16(array2, 0))
				{
					case 2:
						KhuVuc_HoiSinh_Ngoai_TheLucChien = true;
						if (World.tmc_flag)
						{
							if (TheLucChien_PhePhai == "CHINH_PHAI")
							{
								DeathMove(160f, 0f, 15f, 801);
							}
							else
							{
								DeathMove(-160f, 0f, 15f, 801);
							}
							Skill_KinhCong_10x();
						}
						else
						{
							if (Player_Zx == 1)
							{
								DeathMove(160f, 0f, 15f, 801);
							}
							else
							{
								DeathMove(-160f, 0f, 15f, 801);
							}
							Skill_KinhCong_10x();
						}
						break;
					case 1:
						KhuVuc_HoiSinh_Trong_TheLucChien = true;
						if (TheLucChien_PhePhai == "CHINH_PHAI")
						{
							DeathMove(520f, 0f, 15f, 801);
							Skill_KinhCong_10x();
						}
						else
						{
							DeathMove(-520f, 0f, 15f, 801);
							Skill_KinhCong_10x();
						}
						break;
				}
				if (Player_Job == 11)
				{
					NhanVat_AP = CharacterMax_AP;
				}
				NhanVat_HP = CharacterMax_HP;
				CapNhat_HP_MP_SP();
				PlayerTuVong = false;
				break;
			case 9:
				GUI_DI_THE_LUC_BATTLE_RECORD(this);
				break;
			case 23:
				if (MapID != 40101)
				{
					GuiDi_DCH_LoiMoi_New2();
				}
				break;
		}
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packetData, 4, 2);
		Client?.Send_Map_Data(packetData, packetData.Length);
	}

	public void Setup_ToaDo_Event_DuongDua_HuyenBotPhai()
	{
		var num = 0;
		try
		{
			num = 1;
			var rxjhMap = 101;
			var rxjhZ = 15f;
			X_Toa_Do_Class item = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1159f,
				Rxjh_Y = 2092f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item);
			X_Toa_Do_Class item2 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1097f,
				Rxjh_Y = 2110f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item2);
			X_Toa_Do_Class item3 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1099f,
				Rxjh_Y = 2120f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item3);
			X_Toa_Do_Class item4 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1079f,
				Rxjh_Y = 2124f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item4);
			X_Toa_Do_Class item5 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1218f,
				Rxjh_Y = 2128f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item5);
			X_Toa_Do_Class item6 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1218f,
				Rxjh_Y = 2098f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item6);
			X_Toa_Do_Class item7 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1242f,
				Rxjh_Y = 2125f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item7);
			X_Toa_Do_Class item8 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1242f,
				Rxjh_Y = 2098f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item8);
			X_Toa_Do_Class item9 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1220f,
				Rxjh_Y = 2027f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item9);
			X_Toa_Do_Class item10 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1220f,
				Rxjh_Y = 1995f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item10);
			X_Toa_Do_Class item11 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1220f,
				Rxjh_Y = 1995f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item11);
			X_Toa_Do_Class item12 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1160f,
				Rxjh_Y = 1961f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item12);
			X_Toa_Do_Class item13 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1184f,
				Rxjh_Y = 1959f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item13);
			X_Toa_Do_Class item14 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1134f,
				Rxjh_Y = 2017f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item14);
			X_Toa_Do_Class item15 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1120f,
				Rxjh_Y = 2021f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item15);
			X_Toa_Do_Class item16 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1100f,
				Rxjh_Y = 2019f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item16);
			X_Toa_Do_Class item17 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1101f,
				Rxjh_Y = 2041f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item17);
			X_Toa_Do_Class item18 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1100f,
				Rxjh_Y = 1975f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item18);
			X_Toa_Do_Class item19 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1098f,
				Rxjh_Y = 1935f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item19);
			X_Toa_Do_Class item20 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1183f,
				Rxjh_Y = 1909f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item20);
			X_Toa_Do_Class item21 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1154f,
				Rxjh_Y = 1910f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item21);
			X_Toa_Do_Class item22 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1122f,
				Rxjh_Y = 1910f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item22);
			X_Toa_Do_Class item23 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1091f,
				Rxjh_Y = 1915f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item23);
			X_Toa_Do_Class item24 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1061f,
				Rxjh_Y = 1915f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item24);
			X_Toa_Do_Class item25 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1032f,
				Rxjh_Y = 1914f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item25);
			X_Toa_Do_Class item26 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1003f,
				Rxjh_Y = 1885f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item26);
			X_Toa_Do_Class item27 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1239f,
				Rxjh_Y = 1880f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item27);
			X_Toa_Do_Class item28 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1231f,
				Rxjh_Y = 1850f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item28);
			X_Toa_Do_Class item29 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1214f,
				Rxjh_Y = 1850f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item29);
			X_Toa_Do_Class item30 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1186f,
				Rxjh_Y = 1850f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item30);
			X_Toa_Do_Class item31 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1155f,
				Rxjh_Y = 1850f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item31);
			X_Toa_Do_Class item32 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1123f,
				Rxjh_Y = 1850f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item32);
			X_Toa_Do_Class item33 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1094f,
				Rxjh_Y = 1850f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item33);
			X_Toa_Do_Class item34 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1075f,
				Rxjh_Y = 1843f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item34);
			X_Toa_Do_Class item35 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1063f,
				Rxjh_Y = 1853f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item35);
			X_Toa_Do_Class item36 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1063f,
				Rxjh_Y = 1796f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item36);
			X_Toa_Do_Class item37 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1063f,
				Rxjh_Y = 1767f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item37);
			X_Toa_Do_Class item38 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1063f,
				Rxjh_Y = 1735f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item38);
			X_Toa_Do_Class item39 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1063f,
				Rxjh_Y = 1704f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item39);
			X_Toa_Do_Class item40 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1067f,
				Rxjh_Y = 1687f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item40);
			X_Toa_Do_Class item41 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1058f,
				Rxjh_Y = 1693f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item41);
			X_Toa_Do_Class item42 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1043f,
				Rxjh_Y = 1687f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item42);
			X_Toa_Do_Class item43 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1024f,
				Rxjh_Y = 1681f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item43);
			X_Toa_Do_Class item44 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 999f,
				Rxjh_Y = 1684f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item44);
			X_Toa_Do_Class item45 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 997f,
				Rxjh_Y = 1664f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item45);
			X_Toa_Do_Class item46 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1018f,
				Rxjh_Y = 1660f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item46);
			X_Toa_Do_Class item47 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1001f,
				Rxjh_Y = 1803f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item47);
			X_Toa_Do_Class item48 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1001f,
				Rxjh_Y = 1777f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item48);
			X_Toa_Do_Class item49 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1001f,
				Rxjh_Y = 1777f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item49);
			X_Toa_Do_Class item50 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1006f,
				Rxjh_Y = 1744f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item50);
			X_Toa_Do_Class item51 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 939f,
				Rxjh_Y = 1709f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item51);
			X_Toa_Do_Class item52 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 937f,
				Rxjh_Y = 1677f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item52);
			X_Toa_Do_Class item53 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 938f,
				Rxjh_Y = 1627f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item53);
			X_Toa_Do_Class item54 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 954f,
				Rxjh_Y = 1600f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item54);
			X_Toa_Do_Class item55 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 974f,
				Rxjh_Y = 1599f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item55);
			X_Toa_Do_Class item56 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1164f,
				Rxjh_Y = 1787f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item56);
			X_Toa_Do_Class item57 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1138f,
				Rxjh_Y = 1788f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item57);
			X_Toa_Do_Class item58 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1125f,
				Rxjh_Y = 1789f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item58);
			X_Toa_Do_Class item59 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1128f,
				Rxjh_Y = 1757f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item59);
			X_Toa_Do_Class item60 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1125f,
				Rxjh_Y = 1736f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item60);
			X_Toa_Do_Class item61 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1124f,
				Rxjh_Y = 1707f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item61);
			X_Toa_Do_Class item62 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1124f,
				Rxjh_Y = 1678f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item62);
			X_Toa_Do_Class item63 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1123f,
				Rxjh_Y = 1648f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item63);
			X_Toa_Do_Class item64 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1120f,
				Rxjh_Y = 1631f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item64);
			X_Toa_Do_Class item65 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1120f,
				Rxjh_Y = 1607f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item65);
			X_Toa_Do_Class item66 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1120f,
				Rxjh_Y = 1577f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item66);
			X_Toa_Do_Class item67 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1137f,
				Rxjh_Y = 1564f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item67);
			X_Toa_Do_Class item68 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1137f,
				Rxjh_Y = 1534f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item68);
			X_Toa_Do_Class item69 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1137f,
				Rxjh_Y = 1498f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item69);
			X_Toa_Do_Class item70 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1240f,
				Rxjh_Y = 1671f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item70);
			X_Toa_Do_Class item71 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1240f,
				Rxjh_Y = 1698f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item71);
			X_Toa_Do_Class item72 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1180f,
				Rxjh_Y = 1685f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item72);
			X_Toa_Do_Class item73 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1180f,
				Rxjh_Y = 1650f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item73);
			X_Toa_Do_Class item74 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1181f,
				Rxjh_Y = 1625f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item74);
			X_Toa_Do_Class item75 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1211f,
				Rxjh_Y = 1619f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item75);
			X_Toa_Do_Class item76 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1270f,
				Rxjh_Y = 1617f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item76);
			X_Toa_Do_Class item77 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1298f,
				Rxjh_Y = 1623f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item77);
			X_Toa_Do_Class item78 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1297f,
				Rxjh_Y = 1656f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item78);
			X_Toa_Do_Class item79 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1297f,
				Rxjh_Y = 1694f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item79);
			X_Toa_Do_Class item80 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1215f,
				Rxjh_Y = 1794f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item80);
			X_Toa_Do_Class item81 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1215f,
				Rxjh_Y = 1834f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item81);
			X_Toa_Do_Class item82 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1239f,
				Rxjh_Y = 1796f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item82);
			X_Toa_Do_Class item83 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1245f,
				Rxjh_Y = 1741f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item83);
			X_Toa_Do_Class item84 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1214f,
				Rxjh_Y = 1741f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item84);
			X_Toa_Do_Class item85 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1178f,
				Rxjh_Y = 1741f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item85);
			X_Toa_Do_Class item86 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1157f,
				Rxjh_Y = 1739f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item86);
			X_Toa_Do_Class item87 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1188f,
				Rxjh_Y = 1593f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item87);
			X_Toa_Do_Class item88 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1188f,
				Rxjh_Y = 1559f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item88);
			X_Toa_Do_Class item89 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1188f,
				Rxjh_Y = 1523f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item89);
			X_Toa_Do_Class item90 = new()
			{
				Rxjh_Map = rxjhMap,
				Rxjh_X = 1188f,
				Rxjh_Y = 1492f,
				Rxjh_Z = rxjhZ
			};
			World.Event_ToaDo_DuongDua.Add(item90);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tại tọa độ này: [" + num + "]");
		}
	}

	public void Setup_ToaDo_CamPK_TLC()
	{
		var num = 0;
		try
		{
			num = 1;
			X_Toa_Do_Class xToaDoClass = new();
			xToaDoClass.Rxjh_Map = 801;
			xToaDoClass.Rxjh_X = -495f;
			xToaDoClass.Rxjh_Y = 435f;
			xToaDoClass.Rxjh_Z = 15f;
			World.TLC_ToaDo_CamPK.Add(xToaDoClass);
			num = 2;
			X_Toa_Do_Class xToaDoClass2 = new();
			xToaDoClass2.Rxjh_Map = 801;
			xToaDoClass2.Rxjh_X = -450f;
			xToaDoClass2.Rxjh_Y = 435f;
			xToaDoClass2.Rxjh_Z = 15f;
			World.TLC_ToaDo_CamPK.Add(xToaDoClass2);
			num = 3;
			X_Toa_Do_Class xToaDoClass3 = new();
			xToaDoClass3.Rxjh_Map = 801;
			xToaDoClass3.Rxjh_X = -550f;
			xToaDoClass3.Rxjh_Y = 435f;
			xToaDoClass3.Rxjh_Z = 15f;
			World.TLC_ToaDo_CamPK.Add(xToaDoClass2);
			num = 4;
			X_Toa_Do_Class xToaDoClass4 = new();
			xToaDoClass4.Rxjh_Map = 801;
			xToaDoClass4.Rxjh_X = 485f;
			xToaDoClass4.Rxjh_Y = 435f;
			xToaDoClass4.Rxjh_Z = 15f;
			World.TLC_ToaDo_CamPK.Add(xToaDoClass4);
			num = 5;
			X_Toa_Do_Class xToaDoClass5 = new();
			xToaDoClass5.Rxjh_Map = 801;
			xToaDoClass5.Rxjh_X = 550f;
			xToaDoClass5.Rxjh_Y = 435f;
			xToaDoClass5.Rxjh_Z = 15f;
			World.TLC_ToaDo_CamPK.Add(xToaDoClass5);
			num = 6;
			X_Toa_Do_Class xToaDoClass6 = new();
			xToaDoClass6.Rxjh_Map = 801;
			xToaDoClass6.Rxjh_X = 420f;
			xToaDoClass6.Rxjh_Y = 435f;
			xToaDoClass6.Rxjh_Z = 15f;
			World.TLC_ToaDo_CamPK.Add(xToaDoClass6);
			num = 7;
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tại tọa độ này: [" + num + "]");
		}
	}

	public void Setup_ToaDo_UnCheck_DCH()
	{
		var num = 0;
		try
		{
			num = 1;
			X_Toa_Do_Class xToaDoClass = new();
			xToaDoClass.Rxjh_Map = 40101;
			xToaDoClass.Rxjh_X = -215f;
			xToaDoClass.Rxjh_Y = -715f;
			xToaDoClass.Rxjh_Z = 15f;
			World.DCH_ToaDo_UnCheck.Add(xToaDoClass);
			num = 2;
			X_Toa_Do_Class xToaDoClass2 = new();
			xToaDoClass2.Rxjh_Map = 40101;
			xToaDoClass2.Rxjh_X = 245f;
			xToaDoClass2.Rxjh_Y = 705f;
			xToaDoClass2.Rxjh_Z = 15f;
			World.DCH_ToaDo_UnCheck.Add(xToaDoClass2);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tại tọa độ này đây, Đại CHiến Hồn num:[" + num + "]");
		}
	}

	public void TLC_Control_TimerEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (MapID == 801 && World.TheLucChien_Progress > 0)
			{
				UnCheck_ToaDo_PK_TLC();
			}
		}
		catch
		{
			if (TlcPlayerTimer != null)
			{
				TlcPlayerTimer.Enabled = false;
				TlcPlayerTimer.Close();
				TlcPlayerTimer.Dispose();
				TlcPlayerTimer = null;
			}
			LogHelper.WriteLine(LogLevel.Error, "Thiên cơ các tự động của Thế Lực Chiến gặp lỗi nên đã Try tại đây !!");
		}
	}

	public void DCH_Control_TimerEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (MapID != 40101 || World.DCH_Progress <= 3)
			{
				return;
			}
			UnCheck_ToaDo_PK_DCH();
			var list = GroupAttackSearchRange_DCH(this, 4);
			var source = list.Except(_listLgbtCache).ToList();
			var source2 = _listLgbtCache.Except(list).ToList();
			if (source.Any() || source2.Any())
			{
				_listLgbtCache = list;
				foreach (var value in NearbyPlayers.Values)
				{
					if (Player_Zx == 1)
					{
						DCH_DOIXANH_EFFECT(this, value, DCH_TrangThaiTangHinh, list);
					}
					else
					{
						DCH_DOIDO_EFFECT(this, value, DCH_TrangThaiTangHinh, list);
					}
				}
			}
			var xToaDoClass = (from f in World.DCH_ToaDo_TanHinh
							   let distanceSquared = (PosX - f.Rxjh_X) * (PosX - f.Rxjh_X) + (PosY - f.Rxjh_Y) * (PosY - f.Rxjh_Y)
							   where distanceSquared < 625.0
							   select f).FirstOrDefault();
			if (xToaDoClass == null)
			{
				if (DCH_CheckTrangThaiTangHinh == 1)
				{
					if (Player_Zx == 1)
					{
						DCH_DOIXANH(this, 0);
					}
					else if (Player_Zx == 2)
					{
						DCH_DOIDO(this, 0);
					}
					DCH_CheckTrangThaiTangHinh = 0;
				}
			}
			else if (DCH_CheckTrangThaiTangHinh == 0)
			{
				DCH_CheckTrangThaiTangHinh = 1;
				if (Player_Zx == 1)
				{
					DCH_DOIXANH(this, 1);
				}
				else if (Player_Zx == 2)
				{
					DCH_DOIDO(this, 1);
				}
			}
		}
		catch
		{
			if (DchPlayerTimer != null)
			{
				DchPlayerTimer.Enabled = false;
				DchPlayerTimer.Close();
				DchPlayerTimer.Dispose();
				DchPlayerTimer = null;
			}
			LogHelper.WriteLine(LogLevel.Error, "Thiên cơ các tự động của Đại CHiến Hồn gặp lỗi nên đã Try tại đây !!");
		}
	}

	public void DaiChienHon_HeThong(byte[] data, int length)
	{
		var num = BitConverter.ToInt16(data, 10);
		if (num != 1)
		{
			return;
		}
		var num2 = 0;
		if (FLD_VIP != World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han)
		{
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (MacAddress == value2.MacAddress && value2.MapID == 40101)
				{
					num2++;
				}
			}
		}
		if (num2 < World.Gioi_han_acc_vao_TLC)
		{
			if (!World.DanhSachNhanVat_ThamGia_DCH.TryGetValue(SessionID, out var _))
			{
				World.DanhSachNhanVat_ThamGia_DCH.Add(SessionID, this);
				HeThongNhacNho("Đại hiệp đã đăng ký tham gia Đại Chiến Hồn, xin chờ giây lát!", 10, "Thiên cơ các");
				return;
			}
			if (World.DCH_Progress > 3)
			{
				HeThongNhacNho("Hết thời gian tham gia, xin quay lại sau!", 20, "Thiên cơ các");
				return;
			}
			if (Player_Zx == 1)
			{
				Mobile(-5f, -710f, 15f, 40101, 1);
				DCH_DOIXANH(this, 0);
				DCH_TrangThaiTangHinh = 0;
				DCH_PhePhai = "CHINHPHAI";
			}
			else if (Player_Zx == 2)
			{
				Mobile(15f, 690f, 15f, 40101, 1);
				DCH_DOIDO(this, 0);
				DCH_TrangThaiTangHinh = 0;
				DCH_PhePhai = "TAPHAI";
			}
			DchPlayerTimer = new System.Timers.Timer();
			DchPlayerTimer.Elapsed += DCH_Control_TimerEvent;
			DchPlayerTimer.Interval = 1000.0;
			DchPlayerTimer.Enabled = true;
			DchPlayerTimer.AutoReset = true;
		}
		else
		{
			HeThongNhacNho("Giới hạn Đại Chiến Hồn: [" + World.Gioi_han_acc_vao_TLC + "] tài khoản trên 1 IP!!!", 10, "Truyền Âm Các");
		}
	}

	public void DaiChienHon_TrangThai_TangHinh(byte[] data, int length)
	{
		var array = new byte[4];
		Buffer.BlockCopy(data, 14, array, 0, 2);
		int num = BitConverter.ToInt16(array, 0);
	}

	public void DaiChienHon_XepHang(byte[] data, int length)
	{
		GUI_DI_DCH_CHINHPHAI_RECORD(this);
		GUI_DI_DCH_TAPHAI_RECORD(this);
	}

	public void ChuaBietLaGi_1(byte[] data, int length)
	{
		var num = 0;
		try
		{
			num = 1;
			var num2 = BitConverter.ToInt16(data, 10);
			num = 2;
			int num3 = BitConverter.ToInt16(data, 10);
			num = 3;
			var array = new byte[4];
			num = 4;
			Buffer.BlockCopy(data, 14, array, 0, 2);
			num = 5;
			int num4 = BitConverter.ToInt16(array, 0);
			num = 6;
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi ChuaBietLaGi_1 - tại num: " + num);
		}
	}

	public void ChuaBietLaGi_2(byte[] data, int length)
	{
		var num = 0;
		try
		{
			num = 1;
			var num2 = BitConverter.ToInt16(data, 10);
			num = 2;
			int num3 = BitConverter.ToInt16(data, 10);
			num = 3;
			var array = new byte[4];
			num = 4;
			Buffer.BlockCopy(data, 14, array, 0, 2);
			num = 5;
			int num4 = BitConverter.ToInt16(array, 0);
			num = 6;
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi ChuaBietLaGi_2 - tại num: " + num);
		}
	}

	public void DaiChienHon_LuaChon_HoiSinh(byte[] data, int length)
	{
		var array = new byte[4];
		Buffer.BlockCopy(data, 14, array, 0, 2);
		int num = BitConverter.ToInt16(array, 0);
		if (MapID == 40101)
		{
			switch (num)
			{
				case 1:
					if (Player_Money >= 10000000)
					{
						DCH_HS_LuaChon = 1;
						Packet_Huy_Delay_HoiSinh();
						DeathMove(PosX, PosY, 15f, 40101);
						NhanVat_HP = CharacterMax_HP;
						PlayerTuVong = false;
						CapNhat_HP_MP_SP();
						Player_Money -= 10000000L;
						UpdateMoneyAndWeight();
						HeThongNhacNho("Đại hiệp hồi sinh tại chỗ, bị trừ 10,000,000 ngân lượng!", 10, "Thiên cơ các");
					}
					else
					{
						DCH_HS_LuaChon = 0;
						HeThongNhacNho("Ngân lượng không đủ, đại hiệp sẽ hồi sinh sau 10 khắc!", 10, "Thiên cơ các");
					}
					break;
				case 0:
					if (Player_Money >= 1000000)
					{
						DCH_HS_LuaChon = 2;
						Packet_Huy_Delay_HoiSinh();
						if (Player_Zx == 1)
						{
							DeathMove(222f, -688f, 15f, 40101);
						}
						else if (Player_Zx == 2)
						{
							DeathMove(205f, 700f, 15f, 40101);
						}
						NhanVat_HP = CharacterMax_HP;
						CapNhat_HP_MP_SP();
						PlayerTuVong = false;
						Player_Money -= 1000000L;
						UpdateMoneyAndWeight();
						HeThongNhacNho("Đại hiệp hồi sinh nhanh, bị trừ 1,000,000 ngân lượng!", 10, "Thiên cơ các");
					}
					else
					{
						DCH_HS_LuaChon = 0;
						HeThongNhacNho("Ngân lượng không đủ, đại hiệp sẽ hồi sinh sau 10 khắc!", 10, "Thiên cơ các");
					}
					break;
				default:
					DCH_HS_LuaChon = 0;
					HeThongNhacNho("Đại hiệp sẽ hồi sinh sau 10 khắc!", 10, "Thiên cơ các");
					break;
			}
			if (FLD_VIP == World.FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han)
			{
				LogHelper.WriteLine(LogLevel.Error, " lựa chọn: [" + num + "]");
			}
		}
		Skill_KinhCong_10x();
	}

	public void Packet_Huy_Delay_HoiSinh()
	{
		var array = Converter.HexStringToByte("AA550E007F022B5104000100000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}

	public void DCH_Stack_Add(Players players, int buffid, int soLuong, int onOff)
	{
		var text = "AA5536007F0243012C00000000000000000005E3143C00000000740002000200000000000000010000006D2AD289000000000000000055AA";
		var array = Converter.HexStringToByte(text);
		Buffer.BlockCopy(BitConverter.GetBytes(soLuong), 0, array, 30, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(onOff), 0, array, 38, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(buffid), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
		players.Client?.Send_Map_Data(array, array.Length);
	}
	
	public async void LoadCongThanhChienData()
	{
		var num = 0;
		try
		{
			num = 1;
			var dBToDataTable = await GameDb.FindAllSiegeParticipants();
			if (dBToDataTable == null)
			{
				return;
			}
			num = 2;
			if (dBToDataTable.Count != 0)
			{
				num = 3;
				World.CongThanhSoLieu_list.Clear();
				for (var i = 0; i < dBToDataTable.Count; i++)
				{
					num = 4;
					X_Cong_Thanh_So_Lieu xCongThanhSoLieu = new()
					{
						ID = (int)dBToDataTable[i].bangphaiid,
						CongThanhChien_TenBang = dBToDataTable[i].congthanhchien_tenbang,
						ThienMaCongThanhThoiGian = DateTime.Parse(dBToDataTable[i].congthanhthoigian.ToString()),
						ThienMaCongThanhChienBanThuongThoiGian = DateTime.Parse(dBToDataTable[i].congthanhbanthuongthoigian.ToString()),
						TenThanhChu = dBToDataTable[i].tenthanhchu.ToString()
					};
					num = 5;
					num = 6;
					if (World.CongThanhSoLieu_list != null && !World.CongThanhSoLieu_list.TryGetValue(xCongThanhSoLieu.ID, out var value))
					{
						num = 7;
						if (value != null)
						{
							num = 8;
							World.CongThanhSoLieu_list.Add(xCongThanhSoLieu.ID, xCongThanhSoLieu);
							num = 9;
						}
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Load CongThanhChien Data Lỗi:[" + num + "]-[" + AccountID + "]-[" + CharacterName + "]- " + ex.Message);
		}
	}

}
