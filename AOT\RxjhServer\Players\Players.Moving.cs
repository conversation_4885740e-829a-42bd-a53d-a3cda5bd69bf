using System;
using System.Collections.Generic;
using System.Text;
using System.Timers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.AOI;
using RxjhServer.HelperTools;
using RxjhServer.Systems;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace RxjhServer
{
    /// <summary>
    /// Movement data structure for safe packet parsing and validation
    /// </summary>
    public struct MovementData
    {
        public int Dst;
        public float CurrentX, CurrentY, CurrentZ;
        public float TargetX, TargetY, TargetZ;
        public float DistanceLeft;
        public byte MoveType;
        public DateTime Timestamp;
        public float CalculatedSpeed;
        public int SessionId;

        public byte[] BasePacketLength;

        public MovementData(int dst, float currentX, float currentY, float currentZ,
                           float targetX, float targetY, float targetZ,
                           float distanceLeft, byte moveType, int sessionId, byte[] basePacketLength)
        {
            Dst = dst;
            CurrentX = currentX;
            CurrentY = currentY;
            CurrentZ = currentZ;
            TargetX = targetX;
            TargetY = targetY;
            TargetZ = targetZ;
            DistanceLeft = distanceLeft;
            MoveType = moveType;
            SessionId = sessionId;
            Timestamp = DateTime.Now;
            CalculatedSpeed = 0f;
            BasePacketLength = basePacketLength; // Adjust as needed
        }
    }

    /// <summary>
    /// Configuration for movement validation system
    /// </summary>
    public static class MovementConfig
    {
        public static float SpeedTolerance = 1.25f;        // 15% tolerance for network lag
        public static float PositionTolerance = 1.25f;     // 25% tolerance for position validation
        public static int MaxViolationsPerMinute = 8;      // Max violations before action
        public static int ViolationResetTime = 1000;      // 30 seconds reset time
        public static bool EnablePositionCorrection = true;
        public static bool EnableSpeedLogging = true;
        public static float MinValidDistance = 0.1f;       // Minimum distance to consider as movement
        public static float MaxTeleportDistance = 300f;    // Max distance before considering teleport
    }

    public partial class Players
    {
        // Movement validation tracking
        private DateTime _lastMovementTime = DateTime.Now;
        private float _lastValidX, _lastValidY, _lastValidZ;
        private int _speedViolationCount = 0;
        private DateTime _lastViolationReset = DateTime.Now;

        // AOI update throttling
        private DateTime _lastAOIUpdateTime = DateTime.Now;
        private const int AOI_UPDATE_THROTTLE_MS = 1000; // Update AOI max once per second

        public DateTime LastMovementTime
        {
            get => _lastMovementTime;
            set => _lastMovementTime = value;
        }
        public void DashQuyenSu(int voCongId, Players targetPlayer, X_Vo_Cong_Loai currentSkill)
        {
            MagicUse(currentSkill.FLD_MP);
            var value45 = new X_Di_Thuong_Trang_Thai_Loai(targetPlayer, World.QuyenSu_TroiChan_ThoiGian, 33, 0.0);
            targetPlayer.TrangThai_BatThuong[33] = value45;
            targetPlayer.TeleCharacter(targetPlayer.PosX, targetPlayer.PosY, targetPlayer.PosZ);
            QuyenSu_Move(PosX, PosY, PosZ);
            SendAttackerData(this, targetPlayer.SessionID, voCongId, 0, 126, NhanVat_HP, 0, 0);
            //this.TeleCharacter(PosX, PosY, 15.0f);
        }



        public void MoveWithPacket(float targetX, float targetY, float distance, int walkingState = -1)
        {
            try
            {
                var packetHex = "aa554200f3030700380002000000a1b514c2239daec36c9c9343646e794100007041933aad4301010000f8189342ffff0000f724ac41ccafaf434c49cdc188209c4355aa";
                var packet = Converter.HexStringToByte(packetHex);

                System.Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packet, 4, 2);

                // Set target position
                System.Buffer.BlockCopy(BitConverter.GetBytes(targetX), 0, packet, 14, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, packet, 18, 4); // Z offset
                System.Buffer.BlockCopy(BitConverter.GetBytes(targetY), 0, packet, 22, 4);

                // Set current position
                System.Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, packet, 26, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, packet, 30, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, packet, 34, 4);

                // Set movement speed
                System.Buffer.BlockCopy(BitConverter.GetBytes(walkingState != -1 ? walkingState : WalkingStatusId), 0, packet, 39, 1);
                System.Buffer.BlockCopy(BitConverter.GetBytes(distance), 0, packet, 42, 4);

                this.CharacterMove(packet, packet.Length);

                LogHelper.WriteLine(LogLevel.Info, $"Sent movement packet to ({targetX}, {targetY})");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Error sending movement packet: {ex.Message}");
            }
        }

        public void ProcessMovementSecure(MovementData movement)
        {
            try
            {
                // Basic state validation
                if (!ValidatePlayerState())
                {
                    return;
                }

                // Reset violation counter if enough time has passed
                ResetViolationCounterIfNeeded();

                // Validate movement speed
                if (!ValidateMovementSpeed(movement))
                {
                    // HandleSpeedViolation(movement);
                    // return;
                }

                // Validate position
                // if (!ValidatePosition(movement))
                // {
                //     HandlePositionViolation(movement);
                //     return;
                // }

                // Apply movement if all validations pass
                ApplyValidMovement(movement);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ProcessMovementSecure error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate basic player state for movement
        /// </summary>
        private bool ValidatePlayerState()
        {
            if (PlayerTuVong || TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(27))
            {
                return false;
            }

            if (TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(8) ||
                TrangThai_BatThuong.ContainsKey(24) || TrangThai_BatThuong.ContainsKey(23))
            {
                HeThongNhacNho("Trạng thái hiện tại không thể dịch chuyển!");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Reset violation counter if reset time has passed
        /// </summary>
        private void ResetViolationCounterIfNeeded()
        {
            var timeSinceLastReset = (int)DateTime.Now.Subtract(_lastViolationReset).TotalMilliseconds;
            if (timeSinceLastReset >= MovementSystem.Config.ViolationResetTime)
            {
                _speedViolationCount = 0;
                _lastViolationReset = DateTime.Now;
            }
        }

        /// <summary>
        /// Validate movement speed against maximum allowed speed
        /// </summary>
        private bool ValidateMovementSpeed(MovementData movement)
        {
            try
            {
                var timeDelta = (float)DateTime.Now.Subtract(_lastMovementTime).TotalMilliseconds;
                if (timeDelta <= 0) return true; // Skip validation for same-time packets

                var distance = CalculateDistance(PosX, PosY, movement.CurrentX, movement.CurrentY);
                if (distance < MovementSystem.Config.MinValidDistance) return true; // Skip micro-movements

                var maxAllowedSpeed = MovementSystem.CalculateMaxSpeed(this);

                if (!MovementSystem.IsMovementReasonable(distance, timeDelta, maxAllowedSpeed))
                {
                    MovementSystem.Statistics.RecordSpeedViolation();

                    if (MovementSystem.Config.EnableSpeedLogging)
                    {
                        var actualSpeed = (distance * 1000f) / timeDelta;
                        LogHelper.WriteLine(LogLevel.Warning,
                            $"Speed violation: Player {CharacterName} - Actual: {actualSpeed:F2}, Max: {maxAllowedSpeed:F2}, Distance: {distance:F2}, Time: {timeDelta:F2}ms");
                    }
                    return false;
                }

                MovementSystem.Statistics.RecordMovement();
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ValidateMovementSpeed error: {ex.Message}");
                return true; // Allow movement on validation error to prevent stuck players
            }
        }

        /// <summary>
        /// Validate position for teleport detection and terrain collision
        /// </summary>
        private bool ValidatePosition(MovementData movement)
        {
            try
            {
                if (!MovementSystem.Config.EnableTeleportDetection)
                {
                    return true;
                }

                // Check for teleportation (sudden large distance movement)
                var distance = CalculateDistance(PosX, PosY, movement.CurrentX, movement.CurrentY);
                if (MovementSystem.IsTeleportDistance(distance))
                {
                    MovementSystem.Statistics.RecordTeleportDetection();

                    LogHelper.WriteLine(LogLevel.Warning,
                        $"Teleport detected: Player {CharacterName} moved {distance:F2} pixels instantly");
                    return false;
                }

                // Additional position validations can be added here
                // - Terrain collision checking
                // - Map boundary validation
                // - No-clip detection

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ValidatePosition error: {ex.Message}");
                return true; // Allow movement on validation error
            }
        }

        /// <summary>
        /// Calculate distance between two points
        /// </summary>
        private float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var dx = x2 - x1;
            var dy = y2 - y1;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }

        /// <summary>
        /// Handle speed violation with progressive penalties
        /// </summary>
        private void HandleSpeedViolation(MovementData movement)
        {
            _speedViolationCount++;

            if (_speedViolationCount >= MovementSystem.Config.MaxViolationsPerMinute)
            {
                // Severe violation - disconnect player
                LogHelper.WriteLine(LogLevel.Error,
                    $"Player {CharacterName} disconnected for excessive speed violations ({_speedViolationCount})");

                Client?.Dispose();
                return;
            }

            // Apply position correction
            if (MovementSystem.Config.EnablePositionCorrection)
            {
                CorrectPlayerPosition();
            }

            // Log violation for monitoring
            LogHelper.WriteLine(LogLevel.Warning,
                $"Speed violation #{_speedViolationCount} for player {CharacterName}");
        }

        /// <summary>
        /// Handle position violation (teleport detection)
        /// </summary>
        private void HandlePositionViolation(MovementData movement)
        {
            _speedViolationCount++; // Count as speed violation for simplicity

            LogHelper.WriteLine(LogLevel.Warning,
                $"Position violation for player {CharacterName} - possible teleport hack");

            // Always correct position for teleport violations
            CorrectPlayerPosition();
        }

        /// <summary>
        /// Correct player position to last valid position
        /// </summary>
        private void CorrectPlayerPosition()
        {
            try
            {
                MovementSystem.Statistics.RecordPositionCorrection();

                // Rollback to last valid position
                PosX = _lastValidX;
                PosY = _lastValidY;
                PosZ = _lastValidZ;
                TargetPositionX = _lastValidX;
                TargetPositionY = _lastValidY;

                // Send position correction packet to client
                SendPositionCorrection();

                LogHelper.WriteLine(LogLevel.Info,
                    $"Position corrected for player {CharacterName} to ({_lastValidX:F2}, {_lastValidY:F2})");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CorrectPlayerPosition error: {ex.Message}");
            }
        }

        /// <summary>
        /// Send position correction packet to client
        /// </summary>
        private void SendPositionCorrection()
        {
            try
            {
                var array = Converter.HexStringToByte("AA5522005C0079001C000000000000000000000070410000C64465000000000000000000000055AA");
                Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 14, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 22, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, array, 18, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(MapID), 0, array, 26, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
                Client?.Send_Map_Data(array, array.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendPositionCorrection error: {ex.Message}");
            }
        }

        /// <summary>
        /// Apply valid movement after all validations pass
        /// </summary>
        public void ApplyValidMovement(MovementData movement)
        {
            try
            {
                // Store last valid position for fallback
                _lastValidX = PosX;
                _lastValidY = PosY;
                _lastValidZ = PosZ;
                _lastMovementTime = DateTime.Now;

                // Update target position first
                TargetPositionX = movement.TargetX;
                TargetPositionY = movement.TargetY;

                // CRITICAL: Handle arrival packets (distanceLeft == 0) specially
                if (movement.DistanceLeft <= 0.01f) // Use small epsilon for float comparison
                {
                    // This is an arrival packet - player has reached destination
                    // Update position to target position for perfect synchronization
                    PosX = movement.TargetX;
                    PosY = movement.TargetY;
                    PosZ = movement.TargetZ;

                    LogHelper.WriteLine(LogLevel.Debug, $"Player {CharacterName} arrived at destination: ({PosX:F2}, {PosY:F2})");
                }
                else
                {
                    // This is a movement packet - update to current position
                    PosX = movement.CurrentX;
                    PosY = movement.CurrentY;
                    PosZ = movement.CurrentZ;
                }

                // Update AOI position immediately for real-time synchronization
                AOIExtensions.UpdateAOIPosition(this, PosX, PosY);

                // Enhanced AOI update logic - no throttling for grid changes
                var timeSinceLastAOIUpdate = DateTime.Now - _lastAOIUpdateTime;
                // bool isNearBoundary = this.IsNearGridBoundary();

                // // Skip throttling if player is near grid boundary or enough time has passed
                // if (isNearBoundary || timeSinceLastAOIUpdate.TotalMilliseconds >= AOI_UPDATE_THROTTLE_MS)
                // {
                //     // Use optimized movement AOI update
                //     this.UpdateMovementAOI();
                //     _lastAOIUpdateTime = DateTime.Now;

                //     if (isNearBoundary)
                //     {
                //         LogHelper.WriteLine(LogLevel.Debug, $"AOI update triggered for player {CharacterName} near grid boundary");
                //     }
                // }
                this.UpdateMovementAOI();
                _lastAOIUpdateTime = DateTime.Now;

                // Reset movement tracking variables
                if (TriggerMapMovementEvent)
                {
                    _yxsl = 0;
                }
                tracking_status = false;
                // Handle offline mode positioning
                if (Offline_TreoMay_Mode_ON_OFF == 1)
                {
                    PosX = movement.TargetX;
                    PosY = movement.TargetY;
                }

                // Reset combat states
                PKTuVong = false;
                Player_VoDich = false;

                // Stop automatic systems
                StopAutomaticSystems();

                // Broadcast movement to other players
                BroadcastMovement(movement);
                // Handle special map events
                HandleSpecialMapEvents();

                // Call movement hooks for zone management
                CallMovementHooks();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ApplyValidMovement error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Stop automatic systems when player moves
        /// </summary>
        private void StopAutomaticSystems()
        {

            if (AutomaticRecovery != null)
            {
                AutomaticRecovery.Enabled = false;
                AutomaticRecovery.Close();
                AutomaticRecovery.Dispose();
                AutomaticRecovery = null;
            }

            if (Automatic_Coordinates != null)
            {
                Automatic_Coordinates.Enabled = false;
                Automatic_Coordinates.Close();
                Automatic_Coordinates.Dispose();
                Automatic_Coordinates = null;
            }
        }

        /// <summary>
        /// Broadcast movement to nearby players
        /// </summary>
        private void BroadcastMovement(MovementData movement)
        {
            try
            {
                if (movement.BasePacketLength == null)
                    return;
                byte[] response = new byte[movement.BasePacketLength.Length];
                Buffer.BlockCopy(movement.BasePacketLength, 0, response, 0, movement.BasePacketLength.Length);
                Buffer.BlockCopy(BitConverter.GetBytes(base.SessionID), 0, response, 4, 2);
                Buffer.BlockCopy(BitConverter.GetBytes(movement.DistanceLeft), 0, response, 46, 4);
                response[6] = 101;
                Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, response, 18, 4);
                SendCurrentRangeBroadcastData(response, response.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"BroadcastMovement error: {ex.Message}");
            }
        }



        /// <summary>
        /// Handle special map events and triggers
        /// </summary>
        private void HandleSpecialMapEvents()
        {
            try
            {
                // Handle special map 43001 event
                if (MapID == 43001)
                {
                    触发人物靠近();
                }

                // Handle racing event lock
                if (Event_TrangThai_Lock_DuongDua_F1 == 1 && World.Event_DuongDua_F1_ON_OFF != null)
                {
                    var timeSinceFreeze = (int)DateTime.Now.Subtract(Delay_DongBang).TotalMilliseconds;
                    if (GMMode == 0 && timeSinceFreeze > 4000)
                    {
                        Delay_DongBang = DateTime.Now;
                        foreach (var player in World.allConnectedChars.Values)
                        {
                            player.HeThongNhacNho($"[{CharacterName}] bị loại vì dẫm lên hoa !!", 7, "Thiên cơ các");
                        }
                        Send_IceBlock(SessionID);
                        UpdateMartialArtsAndStatus();
                        Event_TrangThai_Lock_DuongDua_F1 = 0;
                    }
                }

                // Handle racing event coordinate lock
                Lock_ToaDo_Event_DuongDua_HuyenBotPhai();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"HandleSpecialMapEvents error: {ex.Message}");
            }
        }

        /// <summary>
        /// Call movement hooks for zone management and cross-server functionality
        /// </summary>
        private void CallMovementHooks()
        {
            try
            {
                // Hook for cross-server zone handling
                //HookMove(PosX, PosY, PosZ);

                //// Hook for zone management
                //Hooks_PlayerMove.AfterPlayerMove(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CallMovementHooks error: {ex.Message}");
            }
        }



        /// <summary>
        /// Parse legacy movement packet format used by CharacterMove
        /// </summary>
        private MovementData? ParseLegacyMovementPacket(byte[] packetData, int packetSize)
        {
            try
            {
                if (packetData == null || packetSize < 50)
                {
                    return null;
                }

                // Parse legacy packet format
                var array = new byte[4];
                var array2 = new byte[4];
                var dst = new byte[4];
                var array3 = new byte[4];
                var array4 = new byte[4];
                var dst2 = new byte[4];
                var array5 = new byte[4];
                var dst3 = new byte[4];
                var dst4 = new byte[4];
                var array6 = new byte[4];

                Buffer.BlockCopy(packetData, 4, array6, 0, 2);
                Buffer.BlockCopy(packetData, 10, dst3, 0, 4);
                Buffer.BlockCopy(packetData, 14, array, 0, 4);
                Buffer.BlockCopy(packetData, 18, dst, 0, 4);
                Buffer.BlockCopy(packetData, 22, array2, 0, 4);
                Buffer.BlockCopy(packetData, 26, array3, 0, 4);
                Buffer.BlockCopy(packetData, 30, dst2, 0, 4);
                Buffer.BlockCopy(packetData, 34, array4, 0, 4);
                Buffer.BlockCopy(packetData, 42, array5, 0, 4);
                Buffer.BlockCopy(packetData, 46, dst4, 0, 4);

                var sessionId = BitConverter.ToInt32(array6, 0);
                if (sessionId != SessionID)
                {
                    return null;
                }

                var targetX = BitConverter.ToSingle(array, 0);
                var targetY = BitConverter.ToSingle(array2, 0);
                var currentX = BitConverter.ToSingle(array3, 0);
                var currentY = BitConverter.ToSingle(array4, 0);
                var distanceLeft = BitConverter.ToSingle(array5, 0);

                // Validate basic packet data - IMPORTANT: Don't skip packets with distanceLeft == 0
                // These are "arrival" packets that indicate the player has reached their destination
                if (CuaHangCaNhan != null || PlayerTuVong ||
                    NhanVat_HP <= 0 || Exiting || GiaoDich.GiaoDichBenTrong || InTheShop)
                {
                    return null;
                }

                return new MovementData(BitConverter.ToInt32(dst, 0), currentX, currentY, 15f, targetX, targetY, 15f, distanceLeft, 0, sessionId, packetData);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ParseLegacyMovementPacket error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Legacy CharacterMove method - now uses secure movement processing
        /// </summary>
        public void CharacterMove(byte[] PacketData, int packetSize)
        {
            try
            {
                byte[] _finishX = new byte[4];
                byte[] _finishY = new byte[4];
                byte[] _finishZ = new byte[4];
                byte[] _currentX = new byte[4];
                byte[] _currentY = new byte[4];
                byte[] _currentZ = new byte[4];
                byte[] array5 = new byte[4];
                byte[] dst3 = new byte[4];
                byte[] dst4 = new byte[4];
                byte[] array6 = new byte[4];
                System.Buffer.BlockCopy(PacketData, 4, array6, 0, 2);
                System.Buffer.BlockCopy(PacketData, 10, dst3, 0, 4);
                System.Buffer.BlockCopy(PacketData, 14, _finishX, 0, 4);
                System.Buffer.BlockCopy(PacketData, 18, _finishZ, 0, 4);
                System.Buffer.BlockCopy(PacketData, 22, _finishY, 0, 4);
                System.Buffer.BlockCopy(PacketData, 26, _currentX, 0, 4);
                System.Buffer.BlockCopy(PacketData, 30, _currentZ, 0, 4);
                System.Buffer.BlockCopy(PacketData, 34, _currentY, 0, 4);
                System.Buffer.BlockCopy(PacketData, 42, array5, 0, 4);
                System.Buffer.BlockCopy(PacketData, 46, dst4, 0, 4);
                int num = BitConverter.ToInt32(array6, 0);
                float finishX = BitConverter.ToSingle(_finishX, 0);
                float finishY = BitConverter.ToSingle(_finishY, 0);
                float currentX = BitConverter.ToSingle(_currentX, 0);
                float currentY = BitConverter.ToSingle(_currentY, 0);
                float deltaX = finishX - base.PosX;
                float deltaY = finishY - base.PosY;
                float isRunning = BitConverter.ToSingle(array5, 0);
                if (base.PlayerTuVong || TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(8) || TrangThai_BatThuong.ContainsKey(27))
                {
                    finishX = PosX;
                    finishY = PosY;
                    currentX = PosX;
                    currentY = PosY;
                    // HeThongNhacNho($"Di chuyen bat thuong {finishX}-{currentX} {finishY}-{currentY} - {PosX} {PosY}");
                }
                // Movement validation
                if (!ValidateMovement(currentX, currentY))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"{this.CharacterName} Di chuyển không hợp lệ: {base.PosX:F1},{base.PosY:F1} -> {currentX:F1},{currentY:F1}");
                    return;
                }

                lock (_positionLock)
                {
                    var oldX = base.PosX;
                    var oldY = base.PosY;
                    base.PosX = currentX;
                    base.PosY = currentY;

                    // Check if player changed grid
                    var oldGridCoords = AOI.AOISystem.Instance.GetGridCoords(oldX, oldY, this.MapID);
                    var newGridCoords = AOI.AOISystem.Instance.GetGridCoords(currentX, currentY, this.MapID);
                    bool changedGrid = (oldGridCoords.Item1 != newGridCoords.Item1 || oldGridCoords.Item2 != newGridCoords.Item2);

                    AOIExtensions.UpdateAOIPosition(this, currentX, currentY);

                    // If player changed grid, update AOI immediately without throttling
                    if (changedGrid)
                    {
                        this.UpdateAOI(); // Use force update for grid changes
                        _lastAOIUpdateTime = DateTime.Now;
                    }
                    else
                    {
                        // Check if player is in overlap zone for smooth transitions
                        var timeSinceLastAOIUpdate = DateTime.Now - _lastAOIUpdateTime;
                        if (timeSinceLastAOIUpdate.TotalMilliseconds >= AOI_UPDATE_THROTTLE_MS)
                        {
                            // Use overlap-aware visibility update
                            this.UpdateAOI();
                            _lastAOIUpdateTime = DateTime.Now;
                        }
                    }
                }
                //HeThongNhacNho($"Moving {isRunning} fx{finishX}-fy{finishX} cX{currentX}-cY{currentY} num {num} {deltaX}-{deltaY} {text}");
                if ((double)BitConverter.ToSingle(array5, 0) != 0.0 && num == base.SessionID && CuaHangCaNhan ==
                    null && !base.PlayerTuVong && base.NhanVat_HP > 0 && !Exiting && !GiaoDich.GiaoDichBenTrong && !InTheShop)
                {
                    PKTuVong = false;
                }
                else if (isRunning == 0)
                {
                    PKTuVong = false;
                }

                MoveResponse101(PacketData, packetSize, isRunning);
                // Use new secure movement processing
                //var movementData = ParseLegacyMovementPacket(packetData, packetSize);
                // if (movementData.HasValue)
                // {
                //     //HeThongNhacNho($"{PosX}-{PosY} - {movementData.Value.CurrentX}-{movementData.Value.CurrentY}");
                // 	ProcessMovementSecure(movementData.Value);
                //     //HeThongNhacNho($"{PosX}-{PosY} - {movementData.Value.CurrentX}-{movementData.Value.CurrentY}");
                // 	return;
                // }

                // Fallback to legacy processing for compatibility (if needed)
                // ProcessLegacyMovement(packetData, packetSize);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CharacterMove error for player {CharacterName}: {ex.Message}");
            }
        }
        private DateTime MovingTime = DateTime.Now;
        private bool ValidateMovement(float newX, float newY)
        {
            try
            {
                // Calculate distance moved
                float deltaX = newX - base.PosX;
                float deltaY = newY - base.PosY;
                float distance = (float)Math.Sqrt(deltaX * deltaX + deltaY * deltaY);

                // Check time since last movement
                var timeSinceLastMove = DateTime.Now - MovingTime;
                double timeDeltaSeconds = timeSinceLastMove.TotalSeconds;

                // If time delta is too small, use minimum time to prevent division by zero
                if (timeDeltaSeconds < 0.1)
                {
                    timeDeltaSeconds = 0.1;
                }

                // Calculate speed (units per second)
                float speed = distance / (float)timeDeltaSeconds;

                // Speed limit: 300 units per second for normal movement
                const float MAX_SPEED = 300f;

                // Special case: If player just changed map (TriggerMapMovementEvent is true)
                // Allow larger movements for a short period after map transition
                if (TriggerMapMovementEvent)
                {
                    // Allow up to 2000 units movement within 5 seconds after map change
                    var timeSinceMapChange = DateTime.Now - MovingTime;
                    if (timeSinceMapChange.TotalSeconds <= 5.0 && distance <= 2000f)
                    {
                        return true;
                    }
                    // Reset flag after 5 seconds
                    if (timeSinceMapChange.TotalSeconds > 5.0)
                    {
                        TriggerMapMovementEvent = false;
                    }
                }

                // Normal speed validation
                if (speed > MAX_SPEED)
                {
                    // Log suspicious movement for GM debugging
                    if (GMMode == 8)
                    {
                        LogHelper.WriteLine(LogLevel.Info, $"Speed violation: Player {CharacterName} moved {distance:F1} units in {timeDeltaSeconds:F2}s (speed: {speed:F1} u/s)");
                    }
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ValidateMovement error for player {CharacterName}: {ex.Message}");
                return true; // Allow movement on error to prevent player getting stuck
            }
        }
        private void MoveResponse101(byte[] PacketData, int PacketSize, float isRunning)
        {
            byte[] response = new byte[PacketSize];
            System.Buffer.BlockCopy(PacketData, 0, response, 0, PacketSize);
            System.Buffer.BlockCopy(BitConverter.GetBytes(base.SessionID), 0, response, 4, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(isRunning), 0, response, 46, 4);
            response[6] = 101;
            System.Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, response, 18, 4);
            SendCurrentRangeBroadcastData(response, response.Length);
        }
        private readonly object _positionLock = new object();

        /// <summary>
        /// Legacy movement processing for backward compatibility
        /// </summary>
        private void ProcessLegacyMovement(byte[] packetData, int packetSize)
        {
            try
            {
                if (PlayerTuVong || TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(27))
                {
                    return;
                }
                if (TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(8) || TrangThai_BatThuong.ContainsKey(24) || TrangThai_BatThuong.ContainsKey(23))
                {
                    HeThongNhacNho("Trạng thái hiện tại không thể dịch chuyển!");
                    return;
                }

                // Legacy packet parsing (kept for compatibility)
                var array = new byte[4];
                var array2 = new byte[4];
                var dst = new byte[4];
                var array3 = new byte[4];
                var array4 = new byte[4];
                var dst2 = new byte[4];
                var array5 = new byte[4];
                var dst3 = new byte[4];
                var dst4 = new byte[4];
                var array6 = new byte[4];
                Buffer.BlockCopy(packetData, 4, array6, 0, 2);
                Buffer.BlockCopy(packetData, 10, dst3, 0, 4);
                Buffer.BlockCopy(packetData, 14, array, 0, 4);
                Buffer.BlockCopy(packetData, 18, dst, 0, 4);
                Buffer.BlockCopy(packetData, 22, array2, 0, 4);
                Buffer.BlockCopy(packetData, 26, array3, 0, 4);
                Buffer.BlockCopy(packetData, 30, dst2, 0, 4);
                Buffer.BlockCopy(packetData, 34, array4, 0, 4);
                Buffer.BlockCopy(packetData, 42, array5, 0, 4);
                Buffer.BlockCopy(packetData, 46, dst4, 0, 4);
                var num = BitConverter.ToInt32(array6, 0);
                var dIchuyenCHuotDenToaDoCuoiCungX = BitConverter.ToSingle(array, 0);
                var num2 = BitConverter.ToSingle(array2, 0);
                // FIXED: Process all movement packets, including arrival packets (distanceLeft == 0)
                if (num == SessionID && CuaHangCaNhan == null && !PlayerTuVong && NhanVat_HP > 0 && !Exiting && !GiaoDich.GiaoDichBenTrong && !InTheShop)
                {
                    if (TriggerMapMovementEvent)
                    {
                        _yxsl = 0;
                    }
                    tracking_status = false;
                    var num3 = BitConverter.ToSingle(array3, 0); // currentX
                    var num4 = BitConverter.ToSingle(array4, 0); // currentY
                    var value = BitConverter.ToSingle(array5, 0); // distanceLeft
                    var targetX = BitConverter.ToSingle(array, 0);
                    var targetY = BitConverter.ToSingle(array2, 0);

                    // CRITICAL: Handle arrival packets (distanceLeft == 0) specially
                    if (value <= 0.01f) // Arrival packet - player reached destination
                    {
                        // Use target position for perfect synchronization
                        num3 = targetX;
                        num4 = targetY;
                        LogHelper.WriteLine(LogLevel.Debug, $"Player {CharacterName} arrived at destination (legacy): ({num3:F2}, {num4:F2})");
                    }

                    TocDo_TinhToan(num3, num4);
                    _toaDoCuoiCungX = num3;
                    _toaDoCuoiCungY = num4;
                    if (Offline_TreoMay_Mode_ON_OFF == 1)
                    {
                        num3 = targetX;
                        num4 = targetY;
                    }
                    PKTuVong = false;
                    Player_VoDich = false;
                    TargetPositionX = targetX;
                    TargetPositionY = targetY;
                    var array7 = new byte[packetSize];
                    Buffer.BlockCopy(packetData, 0, array7, 0, packetSize);
                    Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array7, 4, 2);
                    Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array7, 46, 4);
                    array7[6] = 101;
                    Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, array7, 18, 4);
                    SendCurrentRangeBroadcastData(array7, array7.Length);
                    if (MapID == 43001)
                    {
                        触发人物靠近();
                    }
                    PosX = num3;
                    PosY = num4;
                    lock (_positionLock)
                    {
                        base.PosX = num3;
                        base.PosY = num4;
                        AOIExtensions.UpdateAOIPosition(this, num3, num4);
                    }
                }
                if (Event_TrangThai_Lock_DuongDua_F1 == 1 && World.Event_DuongDua_F1_ON_OFF != null)
                {
                    var num6 = (int)DateTime.Now.Subtract(Delay_DongBang).TotalMilliseconds;
                    if (GMMode == 0 && num6 > 4000)
                    {
                        Delay_DongBang = DateTime.Now;
                        foreach (var value2 in World.allConnectedChars.Values)
                        {
                            value2.HeThongNhacNho("[" + CharacterName + "] bi loại vi dẫm lên hoa !!", 7, "Thiên cơ các");
                        }
                        Send_IceBlock(SessionID);
                        UpdateMartialArtsAndStatus();
                        Event_TrangThai_Lock_DuongDua_F1 = 0;
                    }
                }
                Lock_ToaDo_Event_DuongDua_HuyenBotPhai();

                // Hook để xử lý di chuyển trong Zone liên server
                try
                {
                    // Gọi hook di chuyển để xử lý Zone liên server
                    //HookMove(PosX, PosY, PosZ);

                    //// Gọi hook để kiểm tra và cập nhật zone của người chơi dựa theo vị trí
                    //Hooks_PlayerMove.AfterPlayerMove(this);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, "Lỗi khi xử lý hook di chuyển: " + ex.Message);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, "Legacy moving error: " + ex.Message);
            }
        }
        	public void DemonMobile(float x, float y, float z, int map)
	{
		var num = 0;
		try
		{
			if (map != 801 || (World.TheLucChien_Progress < 3 && World.TheLucChien_Progress != 0))
			{
				var flag = false;
				if (map != MapID)
				{
					MobileSwitchingScreen();
				}
				if (MapID != map && !DiDong_BanDo_Vip_KiemTra(map))
				{
					return;
				}
				if (MapID == map && PosX == (double)x && PosY == (double)y)
				{
					flag = true;
				}
				num = 1;
				if (MapID == 801)
				{
					GuiDi_NoiDungKetThuc_TheLucChien();
				}
				PlayerLeaveMap(map);
				num = 2;
				var array = Converter.HexStringToByte("AA5522005C0079001C000000000000000000000070410000C64465000000000000000000000055AA");
				AOIExtensions.UpdateAOIPosition(this, x, y);
				PosZ = z;
				MapID = map;
				TargetPositionX = x;
				TargetPositionY = y;
				_movingTime = DateTime.Now;
				num = 3;
				Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, array, 18, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(MapID), 0, array, 26, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
				num = 4;
				GetTheReviewRangePlayers();
				num = 5;
				GetReviewScopeNpc();
				num = 6;
				ScanGroundItems();
				num = 7;
				ServerTime();
				Packet_TuyetRoi();
				OpenWarehouse = false;
				if (map != 42001 && World.DangKyDanhSach_NguoiChoiCongThanhChien.Contains(this))
				{
					World.DangKyDanhSach_NguoiChoiCongThanhChien.Remove(this);
				}
				_toaDoCuoiCungX = x;
				_toaDoCuoiCungY = y;
				num = 8;
				if (map != 9001 && map != 9101 && map != 9201)
				{
					switch (WalkingStatusId)
					{
						case 2:
							if (GetAddState(601101))
							{
								AppendStatusList[601101].ThoiGianKetThucSuKien();
								AppendStatusList.Add(601101, new StatusEffect(this, 300000, 601101, 0));
								WalkingStatusId = 2;
								StatusEffect(BitConverter.GetBytes(601101), 1, 300000);
								WalkingState(BitConverter.GetBytes(601101), 2);
								num = 9;
								UpdateMovementSpeed();
							}
							break;
						case 3:
							if (GetAddState(601102))
							{
								AppendStatusList[601102].ThoiGianKetThucSuKien();
								AppendStatusList.Add(601102, new StatusEffect(this, 300000, 601102, 0));
								WalkingStatusId = 3;
								StatusEffect(BitConverter.GetBytes(601102), 1, 300000);
								WalkingState(BitConverter.GetBytes(601102), 3);
								num = 10;
								UpdateMovementSpeed();
							}
							break;
						case 5:
							if (GetAddState(601103))
							{
								AppendStatusList[601103].ThoiGianKetThucSuKien();
								AppendStatusList.Add(601103, new StatusEffect(this, 300000, 601103, 0));
								WalkingStatusId = 5;
								StatusEffect(BitConverter.GetBytes(601103), 1, 300000);
								WalkingState(BitConverter.GetBytes(601103), 5);
								num = 11;
								UpdateMovementSpeed();
							}
							break;
						case 6:
							if (GetAddState(1001101))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001101].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001101, new StatusEffect(this, 300000, 1001101, 0));
								WalkingStatusId = 6;
								StatusEffect(BitConverter.GetBytes(1001101), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001101), 6);
								UpdateMovementSpeed();
							}
							break;
						case 7:
							if (GetAddState(1001102))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001102].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001102, new StatusEffect(this, 300000, 1001102, 0));
								WalkingStatusId = 7;
								StatusEffect(BitConverter.GetBytes(1001102), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001102), 7);
								UpdateMovementSpeed();
							}
							break;
						case 8:
							if (GetAddState(1001201))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001201].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001201, new StatusEffect(this, 300000, 1001201, 0));
								WalkingStatusId = 8;
								StatusEffect(BitConverter.GetBytes(1001201), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001201), 8);
								UpdateMovementSpeed();
							}
							break;
						case 9:
							if (GetAddState(1001202))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001202].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001202, new StatusEffect(this, 300000, 1001202, 0));
								WalkingStatusId = 9;
								StatusEffect(BitConverter.GetBytes(1001202), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001202), 9);
								UpdateMovementSpeed();
							}
							break;
					}
				}
				if (map != 801 || World.TheLucChien_Progress != 3)
				{
					return;
				}
				GuiDi_TheLucChien_TinTuc2(this);
				GuiDi_TheLucChien_CapNhat_DiemSo(this);
				if (!flag)
				{
					if (MobileMapTimer != null)
					{
						MobileMapTimer.Enabled = false;
						MobileMapTimer.Close();
						MobileMapTimer.Dispose();
						MobileMapTimer = null;
					}
					TriggerMapMovementEvent = true;
					MobileMapTimer = new System.Timers.Timer(90000.0);
					MobileMapTimer.Elapsed += MobileEndEvent;
					MobileMapTimer.Enabled = true;
					MobileMapTimer.AutoReset = false;
				}
			}
			else if (map == 801)
			{
				if (GetAddState(601101))
				{
					AppendStatusList[601101].ThoiGianKetThucSuKien();
				}
				if (GetAddState(601102))
				{
					AppendStatusList[601102].ThoiGianKetThucSuKien();
				}
				if (GetAddState(601103))
				{
					AppendStatusList[601103].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001101))
				{
					AppendStatusList[1001101].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001102))
				{
					AppendStatusList[1001102].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001201))
				{
					AppendStatusList[1001201].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001202))
				{
					AppendStatusList[1001202].ThoiGianKetThucSuKien();
				}
				//UpdateEquipmentEffectTo(this, this);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "DiDong error 11 [" + AccountID + "][" + CharacterName + "][" + num + "][" + x + "][" + y + "][" + z + "][" + map + "]" + ex.Message);
		}
	}

	public void DeathMove(float x, float y, float z, int map)
	{
		try
		{
			if (MapID != map && !DiDong_BanDo_Vip_KiemTra(map))
				return;

			bool isSamePosition = MapID == map && PosX == x && PosY == y;
			// Hủy các timer
			void DisposeTimer(ref System.Timers.Timer timer)
			{
				if (timer != null)
				{
					timer.Enabled = false;
					timer.Close();
					timer.Dispose();
					timer = null;
				}
			}
			DisposeTimer(ref Automatic_Coordinates);
			DisposeTimer(ref MobileMapTimer);

			// Xử lý bản đồ và dữ liệu
			if (map == 801)
			{
				GuiDi_NoiDungKetThuc_TheLucChien();
				if (World.TheLucChien_Progress == 3)
				{
					GuiDi_TheLucChien_TinTuc2(this);
					GuiDi_TheLucChien_CapNhat_DiemSo(this);
				}
				if (World.TheLucChien_Progress == 1)
				{
					UpdateCharacterData(this);
					UpdateEquipmentEffects();
				}
			}

			PlayerLeaveMap(map);

			// Cập nhật tọa độ và gửi dữ liệu
			var array = Converter.HexStringToByte("AA5522005C0079001C000000000000000000000070410000C64465000000000000000000000055AA");
			lock (_positionLock)
			{
				PosZ = z;
				MapID = map;
				_movingTime = DateTime.Now;
				// if (// AOIConfiguration.Instance. ShouldUseAOI(map))
				AOIExtensions.UpdateAOIPosition(this, x, y);
				LastMovementTime = DateTime.MaxValue;
			}


			Buffer.BlockCopy(BitConverter.GetBytes(x), 0, array, 14, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(y), 0, array, 22, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(z), 0, array, 18, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(map), 0, array, 26, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 34, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);

			// Cập nhật trạng thái và dữ liệu khác
			// Force AOI update after respawn to ensure proper visibility synchronization
			try
			{
				// Force immediate AOI update for respawn
				AOIExtensions.ForceUpdateAOI(this);

				// Also broadcast respawn to nearby players using Grid system
				var respawnPacket = Converter.HexStringToByte("AA5522005C0079001C000000000000000000000070410000C64465000000000000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(x), 0, respawnPacket, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(y), 0, respawnPacket, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(z), 0, respawnPacket, 18, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(map), 0, respawnPacket, 26, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, respawnPacket, 4, 2);

				// Broadcast to nearby players via Grid system
				this.SendToNearbyPlayers(respawnPacket, respawnPacket.Length, true);

				LogHelper.WriteLine(LogLevel.Debug, $"Player {CharacterName} respawned and broadcasted to nearby players");
			}
			catch (Exception aoiEx)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error updating AOI after respawn: {aoiEx.Message}");
				// Fallback to old system
				GetTheReviewRangePlayers();
				GetReviewScopeNpc();
				ScanGroundItems();
			}
			ServerTime();
			Packet_TuyetRoi();
			OpenWarehouse = false;

			if (map != 42001 && World.DangKyDanhSach_NguoiChoiCongThanhChien.Contains(this))
				World.DangKyDanhSach_NguoiChoiCongThanhChien.Remove(this);

			if (map == 801)
			{
				TinhToan_NhanVatCoBan_DuLieu();
				UpdateMartialArtsAndStatus();
				UpdateCharacterData(this);
				CapNhat_HP_MP_SP();
				UpdateKhiCong();
				if (CharacterBeast != null)
				{
					SummoningReminder(1, 1);
					ClearTheBeastState();
				}
			}

			_toaDoCuoiCungX = x;
			_toaDoCuoiCungY = y;

			// Xử lý trạng thái di chuyển
			if (map != 9001 && map != 9101 && map != 9201)
			{
				var statusMap = new Dictionary<int, int>
			{
				{ 2, 601101 }, { 3, 601102 }, { 5, 601103 },
				{ 6, 1001101 }, { 7, 1001102 }, { 8, 1001201 }, { 9, 1001202 }
			};

				if (statusMap.TryGetValue(WalkingStatusId, out int statusId) && GetAddState(statusId))
				{
					if (statusId >= 1001101 && HinhThuc_TangHinh == 1)
					{
						HinhThuc_TangHinh = 0;
						CheDo_TangHinh(0);
					}
					AppendStatusList[statusId]?.ThoiGianKetThucSuKien();
					AppendStatusList.Add(statusId, new StatusEffect(this, 300000, statusId, 0));
					StatusEffect(BitConverter.GetBytes(statusId), 1, 300000);
					WalkingState(BitConverter.GetBytes(statusId), WalkingStatusId);
					UpdateMovementSpeed();
				}
			}

			// Xử lý hồi sinh
			if (KhuVuc_HoiSinh_Trong_TheLucChien || KhuVuc_HoiSinh_Ngoai_TheLucChien)
			{
				NhanVat_HoiSinh_BatTu();
				KhuVuc_HoiSinh_Trong_TheLucChien = KhuVuc_HoiSinh_Ngoai_TheLucChien = false;
			}

			// Khởi tạo timer di chuyển nếu cần
			if (!isSamePosition)
			{
				TriggerMapMovementEvent = true;
				MobileMapTimer = new System.Timers.Timer(90000.0)
				{
					Enabled = true,
					AutoReset = false
				};
				MobileMapTimer.Elapsed += MobileEndEvent;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"DiDong error 22 [{AccountID}][{CharacterName}][{x}][{y}][{z}][{map}]{ex.Message}");
		}
	}
	public void Mobile(float x, float y, float z, int map, int type)
	{
		var num = 0;
		try
		{
			_lastMovementTime = DateTime.MaxValue;
			if (GMMode != 0 || map != 801 || (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress != 0))
			{
				var flag = false;
				if (MapID != map && !DiDong_BanDo_Vip_KiemTra(map))
				{
					return;
				}
				if (MapID == map && PosX == (double)x && PosY == (double)y)
				{
					flag = true;
				}
				if (MapID == 801)
				{
					GuiDi_NoiDungKetThuc_TheLucChien();
				}
				if (type != 79)
				{
					PlayerLeaveMap(map);
				}
				bool isMapChange = (base.MapID != map);
				var array = Converter.HexStringToByte("AA5522005C0079001C00000000000060D1C5000070410000D0412D010000000000000000000055AA");
				lock (_positionLock)
				{
					if (isMapChange)
					{
						try
						{
							AOI.AOISystem.Instance.RemovePlayer(base.SessionID);
						}
						catch (Exception ex)
						{
							LogHelper.WriteLine(LogLevel.Error, $"Error removing player from old AOI: {ex.Message}");
						}
					}
					AOIExtensions.UpdateAOIPosition(this, x, y);
					PosZ = z;
					MapID = map;
					TargetPositionX = x;
					TargetPositionY = y;
					_movingTime = DateTime.Now;
					LastMovementTime = DateTime.MinValue;
					if (isMapChange)
					{
						// Force add to new map's AOI system
						AOI.AOISystem.Instance.AddPlayer(this);
					}
					else
					{
						// Update position in current map
						AOI.AOISystem.Instance.UpdatePlayerPosition(this, x, y);
					}
				}

				Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, array, 18, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(MapID), 0, array, 26, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
				if (isMapChange)
				{
					// Use specialized map transition synchronization to prevent race conditions
					// Remove the problematic delay that causes race conditions
					try
					{
						this.UpdateAOI();
					}
					catch (Exception aoiEx)
					{
						LogHelper.WriteLine(LogLevel.Error, $"Error in map transition AOI sync: {aoiEx.Message}");
						// Fallback to old system
						GetTheReviewRangePlayers();
						GetReviewScopeNpc();
						ScanGroundItems();
					}
				}
				else
				{
					// Use immediate update for same-map movement
					try
					{
						this.UpdateAOI();
					}
					catch (Exception aoiEx)
					{
						LogHelper.WriteLine(LogLevel.Error, $"Error in immediate AOI update: {aoiEx.Message}");
						// Fallback to old system
						GetTheReviewRangePlayers();
						GetReviewScopeNpc();
						ScanGroundItems();
					}
				}
				ServerTime();
				Packet_TuyetRoi();
				OpenWarehouse = false;
				if (map != 42001 && World.DangKyDanhSach_NguoiChoiCongThanhChien.Contains(this))
				{
					World.DangKyDanhSach_NguoiChoiCongThanhChien.Remove(this);
				}
				switch (map)
				{
					case 801:
						if (CharacterBeast != null)
						{
							SummoningReminder(1, 1);
							ClearTheBeastState();
						}
						if (map == 801 && World.TheLucChien_Progress == 3)
						{
							GuiDi_TheLucChien_TinTuc2(this);
							GuiDi_TheLucChien_CapNhat_DiemSo(this);
						}
						if (map == 801 && World.TheLucChien_Progress == 1)
						{
							UpdateCharacterData(this);
							UpdateEquipmentEffects();
						}
						// GetUpdatedCharacterData(this);
						TlcPlayerTimer = new System.Timers.Timer();
						TlcPlayerTimer.Elapsed += TLC_Control_TimerEvent;
						TlcPlayerTimer.Interval = 1000.0;
						TlcPlayerTimer.Enabled = true;
						TlcPlayerTimer.AutoReset = true;
						break;
					case 40101:
						if (CharacterBeast != null)
						{
							SummoningReminder(1, 1);
							ClearTheBeastState();
						}
						break;
					default:
						if (map != 40101 && (DCH_HP_BONUS > 0 || DCH_ATTACK_BONUS > 0 || DCH_DEF_BONUS > 0 || DCH_CLVC_BONUS > 0.0 || DCH_ULPT_BONUS > 0.0))
						{
							DCH_HP_BONUS = 0;
							DCH_ATTACK_BONUS = 0;
							DCH_DEF_BONUS = 0;
							DCH_CLVC_BONUS = 0.0;
							DCH_ULPT_BONUS = 0.0;
						}
						break;
				}
				if (type == 1)
				{
					UpdateCharacterData(this);
					UpdateEquipmentEffects();
				}
				_toaDoCuoiCungX = x;
				_toaDoCuoiCungY = y;
				if (map != 9001 && map != 9101 && map != 9201)
				{
					switch (WalkingStatusId)
					{
						case 2:
							if (GetAddState(601101))
							{
								AppendStatusList[601101].ThoiGianKetThucSuKien();
								AppendStatusList.Add(601101, new StatusEffect(this, 300000, 601101, 0));
								WalkingStatusId = 2;
								StatusEffect(BitConverter.GetBytes(601101), 1, 300000);
								WalkingState(BitConverter.GetBytes(601101), 2);
								num = 9;
								UpdateMovementSpeed();
							}
							break;
						case 3:
							if (GetAddState(601102))
							{
								AppendStatusList[601102].ThoiGianKetThucSuKien();
								AppendStatusList.Add(601102, new StatusEffect(this, 300000, 601102, 0));
								WalkingStatusId = 3;
								StatusEffect(BitConverter.GetBytes(601102), 1, 300000);
								WalkingState(BitConverter.GetBytes(601102), 3);
								num = 10;
								UpdateMovementSpeed();
							}
							break;
						case 5:
							if (GetAddState(601103))
							{
								AppendStatusList[601103].ThoiGianKetThucSuKien();
								AppendStatusList.Add(601103, new StatusEffect(this, 300000, 601103, 0));
								WalkingStatusId = 5;
								StatusEffect(BitConverter.GetBytes(601103), 1, 300000);
								WalkingState(BitConverter.GetBytes(601103), 5);
								num = 11;
								UpdateMovementSpeed();
							}
							break;
						case 6:
							if (GetAddState(1001101))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001101].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001101, new StatusEffect(this, 300000, 1001101, 0));
								WalkingStatusId = 6;
								StatusEffect(BitConverter.GetBytes(1001101), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001101), 6);
								UpdateMovementSpeed();
							}
							break;
						case 7:
							if (GetAddState(1001102))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001102].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001102, new StatusEffect(this, 300000, 1001102, 0));
								WalkingStatusId = 7;
								StatusEffect(BitConverter.GetBytes(1001102), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001102), 7);
								UpdateMovementSpeed();
							}
							break;
						case 8:
							if (GetAddState(1001201))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001201].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001201, new StatusEffect(this, 300000, 1001201, 0));
								WalkingStatusId = 8;
								StatusEffect(BitConverter.GetBytes(1001201), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001201), 8);
								UpdateMovementSpeed();
							}
							break;
						case 9:
							if (GetAddState(1001202))
							{
								if (HinhThuc_TangHinh == 1)
								{
									HinhThuc_TangHinh = 0;
									CheDo_TangHinh(0);
								}
								AppendStatusList[1001202].ThoiGianKetThucSuKien();
								AppendStatusList.Add(1001202, new StatusEffect(this, 300000, 1001202, 0));
								WalkingStatusId = 9;
								StatusEffect(BitConverter.GetBytes(1001202), 1, 300000);
								WalkingState(BitConverter.GetBytes(1001202), 9);
								UpdateMovementSpeed();
							}
							break;
					}
				}
				if (!flag)
				{
					if (MobileMapTimer != null)
					{
						MobileMapTimer.Enabled = false;
						MobileMapTimer.Close();
						MobileMapTimer.Dispose();
						MobileMapTimer = null;
					}
					TriggerMapMovementEvent = true;
					MobileMapTimer = new System.Timers.Timer(90000.0);
					MobileMapTimer.Elapsed += MobileEndEvent;
					MobileMapTimer.Enabled = true;
					MobileMapTimer.AutoReset = false;
				}
			}
			else if (map == 801)
			{
				if (GetAddState(601101))
				{
					AppendStatusList[601101].ThoiGianKetThucSuKien();
				}
				if (GetAddState(601102))
				{
					AppendStatusList[601102].ThoiGianKetThucSuKien();
				}
				if (GetAddState(601103))
				{
					AppendStatusList[601103].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001101))
				{
					AppendStatusList[1001101].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001102))
				{
					AppendStatusList[1001102].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001201))
				{
					AppendStatusList[1001201].ThoiGianKetThucSuKien();
				}
				if (GetAddState(1001202))
				{
					AppendStatusList[1001202].ThoiGianKetThucSuKien();
				}
				//UpdateEquipmentEffectTo(this, this);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Di Dong (Move) lỗi 333 [" + AccountID + "][" + CharacterName + "] - Lỗi:[" + num + "] - Tọa độ[" + x + "],[" + y + "],[" + z + "] - Bản đồ:[" + map + "] - [" + ex.Message);
		}
	}

	public void MobileEndEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			TriggerMapMovementEvent = false;
			if (MobileMapTimer != null)
			{
				MobileMapTimer.Enabled = false;
				MobileMapTimer.Close();
				MobileMapTimer.Dispose();
				MobileMapTimer = null;
			}
		}
		catch
		{
		}
	}

	public void NpcTransmission(byte[] packetData, int length)
	{
		PacketModification(packetData, length);
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, packetData, 3, 1);
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, packetData, 4, 2);
		Client?.Send_Map_Data(packetData, packetData.Length);
		var array = new byte[2];
		Buffer.BlockCopy(packetData, 10, array, 0, 2);
		if (BitConverter.ToInt16(array, 0) != 3)
		{
			return;
		}
		if (World.TheLucChien_Progress < 4 && World.TheLucChien_Progress > 0)
		{
			if (TheLucChien_PhePhai == "CHINH_PHAI")
			{
				Mobile(520f, 435f, 15f, 801, 0);
			}
			else
			{
				Mobile(-520f, 435f, 15f, 801, 0);
			}
		}
		var array2 = Converter.HexStringToByte("AA551E0071009100100002000000020000000900000000000000000000000000569A55AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array2, 4, 2);
		Client?.Send_Map_Data(array2, array2.Length);
	}

	public bool DiDong_BanDo_Vip_KiemTra(int rxjhMapId)
	{
		if (FLD_VIP == 0)
		{
			var vIpBanDo = World.VIP_BanDo;
			var separator = new char[1] { ';' };
			var array = vIpBanDo.Split(separator);
			var array2 = array;
			foreach (var text in array2)
			{
				if (X_Toa_Do_Class.getmapname(rxjhMapId) == text)
				{
					HeThongNhacNho("Bí tịch [Bản Đồ] chỉ dành cho thành viên, kẻ ngoài không được phép sử dụng!", 10, "Thiên cơ các");
					return false;
				}
			}
		}
		return true;
	}

	public void MobileSwitchingScreen()
	{
		var array = Converter.HexStringToByte("AA551E001C019100180011000000110000007D00000000000000020000006500000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
		Client?.Send_Map_Data(array, array.Length);
	}
        
	public void ThienMa_ChangeLineMove(float x, float y, float z, int tomap)
	{
		try
		{
			if ((tomap != 1201 || (!PlayerTuVong && !Exiting)) && (CuaHangCaNhan == null || !CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa) && (GiaoDich == null || GiaoDich.NguoiGiaoDich == null))
			{
				if (AutomaticRecovery != null)
				{
					AutomaticRecovery.Enabled = false;
					AutomaticRecovery.Close();
					AutomaticRecovery.Dispose();
					AutomaticRecovery = null;
				}
				if (MapID == 801)
				{
					GuiDi_NoiDungKetThuc_TheLucChien();
				}
				var nhanVatToaDoBanDo = MapID;
				PlayerLeaveMap(tomap);
				SaveCharacterData();
				var array = Converter.HexStringToByte("AA5522000000790014000020D7C500007041000040422D01000000000000000000000000000055AA");
				AOIExtensions.UpdateAOIPosition(this, x, y);
				PosZ = z;
				MapID = tomap;
				TargetPositionX = x;
				TargetPositionY = y;
				_movingTime = DateTime.Now;
				if (CharacterBeast != null)
				{
					CharacterBeast.NhanVatToaDo_X = x;
					CharacterBeast.NhanVatToaDo_Z = z;
					CharacterBeast.NhanVatToaDo_Y = y;
					CharacterBeast.NhanVatToaDo_MAP = tomap;
				}
				_toaDoCuoiCungX = x;
				_toaDoCuoiCungY = y;
				Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 10, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 18, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(MapID), 0, array, 22, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
				if (tomap == 101)
				{
					ChangeLine(OriginalServerID, OriginalServerIP, OriginalServerPort);
				}
				else
				{
					ChangeLine(OriginalServerID, SilverCoinSquareServerIP, SilverCoinSquareServerPort);
				}
				SaveCharacterData();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thay đổi dòng DiDong error 11 [" + AccountID + "][" + CharacterName + "]      " + ex.Message);
		}
	}

	public void ChangeLineMove(float x, float y, float z, int tomap)
	{
		try
		{
			if (AutomaticRecovery != null)
			{
				AutomaticRecovery.Enabled = false;
				AutomaticRecovery.Close();
				AutomaticRecovery.Dispose();
				AutomaticRecovery = null;
			}
			if (MapID == 801)
			{
				GuiDi_NoiDungKetThuc_TheLucChien();
			}
			var nhanVatToaDoBanDo = MapID;
			PlayerLeaveMap(tomap);
			var array = Converter.HexStringToByte("AA5522005C0079001C00000000000060D1C5000070410000D0412D010000000000000000000055AA");
			PosX = x;
			PosY = y;
			PosZ = z;
			MapID = tomap;
			TargetPositionX = x;
			TargetPositionY = y;
			_movingTime = DateTime.Now;
			Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 14, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 22, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, array, 18, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(MapID), 0, array, 26, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
			Client?.Send_Map_Data(array, array.Length);
			if (tomap == 101)
			{
				ChangeLine(OriginalServerID, OriginalServerIP, OriginalServerPort);
			}
			else
			{
				ChangeLine(SilverCoinSquareServerID, SilverCoinSquareServerIP, SilverCoinSquareServerPort);
			}
			SaveCharacterData();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thay đổi dòng DiDong error 22 [" + AccountID + "][" + CharacterName + "]      " + ex.Message);
		}
	}

	public async void ChangeLine(int serverId, string ip, int port)
	{
		try
		{
			Xoa_TrangThai_Pill();
			Client.Online = false;
			var num = 0;
			//var dBToDataTable = DBA.GetDBToDataTable($"select  *  from  [TBL_XWWL_Char]  where  FLD_NAME=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, CharacterName) });
			var dBToDataTable = await GameDb.FindCharacter(AccountID, CharacterName);
			if (dBToDataTable != null || Client == null)
			{
				num = (int)dBToDataTable.fld_index;
				var array = Converter.HexStringToByte("************************");
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
				Client?.Send_Map_Data(array, array.Length);
				var array2 = Converter.HexStringToByte("AA552E00E703D20028000100000000000000000000000000000000000000000000003200000000000000000000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(serverId), 0, array2, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array2, 18, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(port), 0, array2, 30, 4);
				var bytes = Encoding.Default.GetBytes(ip);
				Buffer.BlockCopy(bytes, 0, array2, 34, bytes.Length);
				World.conn.Transmit("CHANGE_CHANNEL_NOTI|" + AccountID + "|" + XacDinhXemCoDangNhapBangPacketHayKhong);
				Client?.Send_Map_Data(array2, array2.Length);
				Logout();
				LogHelper.WriteLine(LogLevel.Error, "Người chơi đổi kênh: |" + AccountID + "|" + XacDinhXemCoDangNhapBangPacketHayKhong);
			}
			else
			{
				kickidlog("Change Line() Thu hoạch nhân vật phạm sai lầm");
				LogHelper.WriteLine(LogLevel.Error, "Thu hút mọi người error，[" + AccountID + "][" + CharacterName + "]");
				OpClient(1);
				Client.Dispose();
			}
		}
		catch
		{
		}
	}
	}

    }